
import React from 'react';
import { cn } from '@/lib/utils';

interface TypographyProps {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
  variant?: 'quantum' | 'cosmic' | '5xl' | '4xl' | '3xl' | '2xl' | 'xl' | 'lg' | 'md' | 'base' | 'sm' | 'xs' | 'micro' | 'nano';
  color?: 'primary' | 'secondary' | 'tertiary' | 'quaternary' | 'consciousness' | 'quantum' | 'inverted' | 'division' | 'harmony' | 'creativity' | 'intuition' | 'transcendence' | 'symbioautomate' | 'symbiolabs' | 'symbioxchange' | 'symbioedge' | 'symbioimpact' | 'symbioventures' | 'symbioalliance';
  gradient?: 'quantum' | 'neural' | 'holographic' | 'consciousness' | 'creativity' | 'division';
  weight?: 'thin' | 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold' | 'black';
  align?: 'left' | 'center' | 'right' | 'justify';
  font?: 'sans' | 'mono' | 'display' | 'quantum';
  spacing?: 'tighter' | 'tight' | 'normal' | 'wide' | 'wider' | 'widest';
  precision?: 'display' | 'body' | 'caption' | 'mono';
  animate?: boolean | 'flicker' | 'pulse' | 'glow';
  division?: 'symbioautomate' | 'symbiolabs' | 'symbioxchange' | 'symbioedge' | 'symbioimpact' | 'symbioventures' | 'symbioalliance';
  children: React.ReactNode;
  className?: string;
}

const Typography: React.FC<TypographyProps> = ({
  as: Component = 'p',
  variant = 'base',
  color = 'primary',
  gradient,
  weight = 'normal',
  align = 'left',
  font = 'sans',
  spacing = 'normal',
  precision,
  animate = false,
  division,
  children,
  className,
  ...props
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'quantum':
        return 'text-quantum leading-tight';
      case 'cosmic':
        return 'text-cosmic leading-tight';
      case '5xl':
        return 'text-5xl leading-tight';
      case '4xl':
        return 'text-4xl leading-tight';
      case '3xl':
        return 'text-3xl leading-snug';
      case '2xl':
        return 'text-2xl leading-snug';
      case 'xl':
        return 'text-xl leading-relaxed';
      case 'lg':
        return 'text-lg leading-relaxed';
      case 'md':
        return 'text-md leading-relaxed';
      case 'sm':
        return 'text-sm leading-normal';
      case 'xs':
        return 'text-xs leading-normal';
      case 'micro':
        return 'text-micro leading-tight';
      case 'nano':
        return 'text-nano leading-tight';
      default:
        return 'text-base leading-relaxed';
    }
  };

  const getColorStyles = () => {
    if (gradient) {
      switch (gradient) {
        case 'quantum':
          return 'bg-gradient-to-r from-consciousness-500 via-consciousness-secondary to-consciousness-500 bg-clip-text text-transparent animate-holographic-shift';
        case 'neural':
          return 'bg-gradient-to-r from-consciousness-500 via-symbioxchange-500 to-symbiolabs-500 bg-clip-text text-transparent animate-holographic-shift';
        case 'holographic':
          return 'bg-gradient-to-r from-consciousness-500 via-symbioventures-500 to-symbioautomate-500 bg-clip-text text-transparent animate-holographic-shift';
        case 'consciousness':
          return 'bg-gradient-to-r from-consciousness-500 to-consciousness-secondary bg-clip-text text-transparent';
        case 'creativity':
          return 'bg-gradient-to-r from-symbioxchange-500 to-symbiolabs-500 bg-clip-text text-transparent';
        case 'division':
          if (division) {
            const divisionGradients = {
              symbioautomate: 'bg-gradient-to-r from-symbioautomate-500 to-symbioautomate-600 bg-clip-text text-transparent',
              symbiolabs: 'bg-gradient-to-r from-symbiolabs-500 to-symbiolabs-600 bg-clip-text text-transparent',
              symbioxchange: 'bg-gradient-to-r from-symbioxchange-500 to-symbioxchange-600 bg-clip-text text-transparent',
              symbioedge: 'bg-gradient-to-r from-symbioedge-500 to-symbioedge-600 bg-clip-text text-transparent',
              symbioimpact: 'bg-gradient-to-r from-symbioimpact-500 to-symbioimpact-600 bg-clip-text text-transparent',
              symbioventures: 'bg-gradient-to-r from-symbioventures-500 to-symbioventures-600 bg-clip-text text-transparent',
              symbioalliance: 'bg-gradient-to-r from-symbioalliance-500 to-symbioalliance-600 bg-clip-text text-transparent',
            };
            return divisionGradients[division];
          }
          return '';
        default:
          return '';
      }
    }

    if (division && color === 'division') {
      const divisionColors = {
        symbioautomate: 'text-symbioautomate-500',
        symbiolabs: 'text-symbiolabs-500',
        symbioxchange: 'text-symbioxchange-500',
        symbioedge: 'text-symbioedge-500',
        symbioimpact: 'text-symbioimpact-500',
        symbioventures: 'text-symbioventures-500',
        symbioalliance: 'text-symbioalliance-500',
      };
      return divisionColors[division];
    }

    switch (color) {
      case 'secondary':
        return 'text-text-secondary';
      case 'tertiary':
        return 'text-text-tertiary';
      case 'quaternary':
        return 'text-text-quaternary';
      case 'consciousness':
        return 'text-consciousness-500';
      case 'quantum':
        return 'text-text-quantum';
      case 'inverted':
        return 'text-abyssal-void';
      case 'harmony':
        return 'text-harmony-500';
      case 'creativity':
        return 'text-creativity-500';
      case 'intuition':
        return 'text-intuition-500';
      case 'transcendence':
        return 'text-transcendence-500';
      case 'symbioautomate':
        return 'text-symbioautomate-500';
      case 'symbiolabs':
        return 'text-symbiolabs-500';
      case 'symbioxchange':
        return 'text-symbioxchange-500';
      case 'symbioedge':
        return 'text-symbioedge-500';
      case 'symbioimpact':
        return 'text-symbioimpact-500';
      case 'symbioventures':
        return 'text-symbioventures-500';
      case 'symbioalliance':
        return 'text-symbioalliance-500';
      default:
        return 'text-text-primary';
    }
  };

  const getWeightStyles = () => {
    switch (weight) {
      case 'thin':
        return 'font-thin';
      case 'light':
        return 'font-light';
      case 'medium':
        return 'font-medium';
      case 'semibold':
        return 'font-semibold';
      case 'bold':
        return 'font-bold';
      case 'extrabold':
        return 'font-extrabold';
      case 'black':
        return 'font-black';
      default:
        return 'font-normal';
    }
  };

  const getAlignStyles = () => {
    switch (align) {
      case 'center':
        return 'text-center';
      case 'right':
        return 'text-right';
      case 'justify':
        return 'text-justify';
      default:
        return 'text-left';
    }
  };

  const getFontStyles = () => {
    switch (font) {
      case 'mono':
        return 'font-mono';
      case 'display':
        return 'font-display';
      case 'quantum':
        return 'font-quantum';
      default:
        return 'font-sans';
    }
  };

  const getPrecisionStyles = () => {
    if (!precision) return '';
    
    switch (precision) {
      case 'display':
        return 'text-precision-display';
      case 'body':
        return 'text-precision-body';
      case 'caption':
        return 'text-precision-caption';
      case 'mono':
        return 'text-precision-mono';
      default:
        return '';
    }
  };

  const getSpacingStyles = () => {
    switch (spacing) {
      case 'tighter':
        return 'tracking-tighter';
      case 'tight':
        return 'tracking-tight';
      case 'wide':
        return 'tracking-wide';
      case 'wider':
        return 'tracking-wider';
      case 'widest':
        return 'tracking-widest';
      default:
        return 'tracking-normal';
    }
  };

  const getAnimationStyles = () => {
    if (!animate) return '';
    
    if (typeof animate === 'string') {
      switch (animate) {
        case 'flicker':
          return 'animate-quantum-flicker';
        case 'pulse':
          return 'animate-sentient-pulse';
        case 'glow':
          return 'animate-anticipatory-glow';
        default:
          return '';
      }
    }
    
    // Legacy boolean support
    if (gradient === 'quantum' || gradient === 'neural' || gradient === 'holographic') {
      return ''; // Animation already applied via gradient
    }
    
    return 'animate-quantum-flicker';
  };

  return (
    <Component
      className={cn(
        getVariantStyles(),
        getColorStyles(),
        getWeightStyles(),
        getAlignStyles(),
        getFontStyles(),
        getPrecisionStyles(),
        getSpacingStyles(),
        getAnimationStyles(),
        'transition-all duration-medium ease-sentient',
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};

export default Typography;
