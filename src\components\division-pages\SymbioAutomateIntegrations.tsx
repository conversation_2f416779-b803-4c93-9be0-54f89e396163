
import React from 'react';
import { Globe, Database, Mail, Users, ShoppingCart, FileText, Cloud, Settings } from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';

const SymbioAutomateIntegrations: React.FC = () => {
  const integrationCategories = [
    {
      category: 'CRM & Sales',
      icon: <Users className="w-6 h-6" />,
      platforms: ['Salesforce', 'HubSpot', 'Pipedrive', 'Zoho CRM', 'ActiveCampaign'],
      color: 'consciousness' as const
    },
    {
      category: 'Marketing',
      icon: <Mail className="w-6 h-6" />,
      platforms: ['Mailchimp', 'Marketo', 'Pardot', 'Constant Contact', 'SendGrid'],
      color: 'harmony' as const
    },
    {
      category: 'E-commerce',
      icon: <ShoppingCart className="w-6 h-6" />,
      platforms: ['Shopify', 'WooCommerce', 'Magento', 'BigCommerce', 'Amazon'],
      color: 'creativity' as const
    },
    {
      category: 'Databases',
      icon: <Database className="w-6 h-6" />,
      platforms: ['MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch'],
      color: 'consciousness' as const
    },
    {
      category: 'Cloud Services',
      icon: <Cloud className="w-6 h-6" />,
      platforms: ['AWS', 'Azure', 'Google Cloud', 'Heroku', 'DigitalOcean'],
      color: 'harmony' as const
    },
    {
      category: 'Productivity',
      icon: <FileText className="w-6 h-6" />,
      platforms: ['Slack', 'Microsoft Teams', 'Notion', 'Trello', 'Asana'],
      color: 'creativity' as const
    }
  ];

  const enterpriseFeatures = [
    {
      title: 'Custom API Integration',
      description: 'Build custom connectors for proprietary systems and legacy applications.'
    },
    {
      title: 'Real-time Data Sync',
      description: 'Bidirectional sync with conflict resolution and data validation.'
    },
    {
      title: 'Webhook Management',
      description: 'Intelligent webhook routing and retry mechanisms for reliable delivery.'
    },
    {
      title: 'Data Transformation',
      description: 'Advanced ETL capabilities with visual mapping and transformation tools.'
    }
  ];

  return (
    <section className="py-20 bg-abyssal-base/30">
      <div className="container mx-auto px-8">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            color="consciousness"
            className="mb-4"
          >
            Seamless Integrations
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            Connect SymbioAutomate with your entire tech stack. 500+ pre-built integrations 
            plus custom API connectivity for any system.
          </Typography>
        </div>

        {/* Integration Categories */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {integrationCategories.map((category, index) => (
            <Card 
              key={category.category}
              variant="neural" 
              className="p-6 border-consciousness/20 hover:border-consciousness/40 transition-all duration-500 rounded-[20px] group"
            >
              <div className="flex items-center space-x-4 mb-4">
                <div className={`w-12 h-12 rounded-full bg-${category.color}/15 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                  {category.icon}
                </div>
                <Typography variant="lg" weight="semibold" color={category.color}>
                  {category.category}
                </Typography>
              </div>
              <div className="space-y-2">
                {category.platforms.map((platform, platformIndex) => (
                  <div key={platform} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-consciousness/40 rounded-full"></div>
                    <Typography variant="sm" color="secondary">
                      {platform}
                    </Typography>
                  </div>
                ))}
              </div>
            </Card>
          ))}
        </div>

        {/* Enterprise Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {enterpriseFeatures.map((feature, index) => (
            <Card 
              key={feature.title}
              variant="quantum" 
              className="p-6 border-consciousness/30 rounded-[20px] text-center"
            >
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-3">
                {feature.title}
              </Typography>
              <Typography variant="xs" color="secondary" className="leading-relaxed">
                {feature.description}
              </Typography>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Card variant="neural" className="p-12 border-consciousness/20 rounded-[32px] max-w-4xl mx-auto">
            <Typography variant="xl" weight="semibold" color="consciousness" className="mb-4">
              Don't See Your Platform?
            </Typography>
            <Typography variant="sm" color="secondary" className="mb-8 max-w-2xl mx-auto">
              Our team can build custom integrations for any API or system. 
              Enterprise clients get priority access to our integration development team.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-3 bg-consciousness/10 border border-consciousness/30 rounded-[16px] text-consciousness hover:bg-consciousness/20 transition-colors duration-300">
                Request Integration
              </button>
              <button className="px-8 py-3 border border-consciousness/30 rounded-[16px] text-consciousness hover:bg-consciousness/10 transition-colors duration-300">
                View API Docs
              </button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default SymbioAutomateIntegrations;
