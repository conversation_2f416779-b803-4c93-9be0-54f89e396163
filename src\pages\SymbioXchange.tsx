
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import { ShoppingCart, Network, ArrowRightLeft, Shield } from 'lucide-react';

const SymbioXchange: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-harmony/10 rounded-cellular blur-3xl animate-consciousness-wave"></div>
            <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-harmony/15 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
          </div>

          <div className="container mx-auto px-6 py-20 relative z-10">
            <div className="text-center space-y-8 max-w-4xl mx-auto">
              <div className="inline-flex items-center space-x-3 px-4 py-2 glass-quantum rounded-full border border-harmony/30">
                <ShoppingCart className="w-5 h-5 text-harmony animate-pulse" />
                <Typography variant="sm" weight="medium" color="harmony">
                  Division III
                </Typography>
              </div>

              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="neural"
                className="mb-4"
              >
                SymbioXchange
              </Typography>
              
              <Typography 
                as="p" 
                variant="xl" 
                color="harmony" 
                weight="medium"
                className="mb-6"
              >
                The Symbiosis Marketplace
              </Typography>

              <Typography 
                as="p" 
                variant="lg" 
                color="secondary"
                className="leading-relaxed max-w-2xl mx-auto"
              >
                An AI-driven B2B marketplace that facilitates the trading of resources, byproducts, waste streams, and underutilized assets.
              </Typography>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                <Card variant="neural" className="border-harmony/30 text-center p-6">
                  <Network className="w-8 h-8 text-harmony mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="harmony" className="mb-2">
                    AI Matching
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Intelligent resource pairing
                  </Typography>
                </Card>

                <Card variant="neural" className="border-harmony/30 text-center p-6">
                  <ArrowRightLeft className="w-8 h-8 text-harmony mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="harmony" className="mb-2">
                    Circular Economy
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Waste becomes resource
                  </Typography>
                </Card>

                <Card variant="neural" className="border-harmony/30 text-center p-6">
                  <Shield className="w-8 h-8 text-harmony mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="harmony" className="mb-2">
                    Blockchain Trust
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Secure smart contracts
                  </Typography>
                </Card>

                <Card variant="neural" className="border-harmony/30 text-center p-6">
                  <ShoppingCart className="w-8 h-8 text-harmony mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="harmony" className="mb-2">
                    Global Trading
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Industrial symbiosis at scale
                  </Typography>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default SymbioXchange;
