import React from 'react';
import { Mail } from 'lucide-react';
import Typography from '../atoms/Typography';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Ecosystem",
      links: [
        { name: "SymbioLabs", href: "/symbiolabs", description: "Research & Development" },
        { name: "SymbioImpact", href: "/symbioimpact", description: "ESG & Impact" },
        { name: "SymbioVentures", href: "/symbioventures", description: "Investment Platform" },
        { name: "SymbioAlliance", href: "/symbioalliance", description: "Partner Network" },
      ]
    },
    {
      title: "Solutions",
      links: [
        { name: "SymbioCore", href: "/symbiocore", description: "Core ACI Infrastructure" },
        { name: "SymbioXchange", href: "/symbioxchange", description: "ACI Marketplace" },
        { name: "SymbioAutomate", href: "/symbioautomate", description: "Process Automation" },
        { name: "SymbioEdge", href: "/symbioedge", description: "Edge Computing" },
      ]
    },
    {
      title: "Developers",
      links: [
        { name: "API Documentation", href: "/documentation", description: "Technical Guides" },
        { name: "SDK & Tools", href: "/sdk", description: "Development Kit" },
        { name: "Case Studies", href: "/case-studies", description: "Success Stories" },
        { name: "ACI Technology", href: "/aci-technology", description: "Core Technology" },
      ]
    },
    {
      title: "Company",
      links: [
        { name: "About SymbioWave", href: "/about-us", description: "Our Story" },
        { name: "Leadership Team", href: "/leadership", description: "Executive Team" },
        { name: "Careers", href: "/careers", description: "Join Our Mission" },
        { name: "Investor Relations", href: "/investors", description: "Financial Info" },
      ]
    }
  ];

  return (
    <footer className="relative overflow-hidden bg-gradient-to-b from-abyssal-base via-abyssal-deep to-abyssal-void">
      {/* Clean Background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-br from-consciousness/2 via-transparent to-harmony/3"></div>
      </div>

      {/* Premium border gradient */}
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>
      <div className="absolute top-0 left-0 w-full h-[2px] bg-gradient-to-r from-transparent via-consciousness/20 to-transparent blur-sm"></div>

      <div className="container mx-auto px-8 py-4 relative z-10">
        {/* Main Footer Content - Reduced spacing and better alignment */}
        <div className="grid lg:grid-cols-6 gap-4 mb-4 items-start">
          
          {/* Brand Section - Optimized layout */}
          <div className="lg:col-span-2">
            <div className="flex items-start space-x-3 group mb-3">
              {/* Logo with overlay capability */}
              <div className="relative flex-shrink-0">
                <img
                  src="/lovable-uploads/69b2f8ba-ee9a-4a51-954a-923e57771f4d.png"
                  alt="SymbioWave"
                  className="w-16 h-16 opacity-90 mix-blend-screen relative z-10 group-hover:scale-105 transition-transform duration-500"
                />
              </div>
              
              {/* Text content that can overlay logo if needed */}
              <div className="flex-1 min-w-0">
                <Typography variant="xs" color="consciousness" className="uppercase tracking-widest font-semibold leading-tight mb-1">
                  Artificial Cellular Intelligence Platform
                </Typography>
                <Typography
                  variant="sm"
                  color="secondary"
                  className="leading-relaxed text-white/80 mb-2"
                >
                  Leading the transformation of global commerce through adaptive, self-optimizing ACI ecosystems.
                </Typography>
              </div>
            </div>

            {/* Contact Information - Compressed */}
            <div>
              <Typography variant="xs" weight="semibold" color="consciousness" className="flex items-center mb-1">
                <Mail className="w-3 h-3 mr-2" />
                Enterprise Contact
              </Typography>
              <div className="space-y-0.5">
                <a href="mailto:<EMAIL>" className="group flex items-center text-xs text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-1.5 h-1.5 bg-consciousness/60 rounded-full mr-2 group-hover:bg-consciousness group-hover:shadow-sm group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
                <a href="mailto:<EMAIL>" className="group flex items-center text-xs text-white/70 hover:text-consciousness transition-all duration-300 hover:translate-x-1">
                  <span className="w-1.5 h-1.5 bg-consciousness/60 rounded-full mr-2 group-hover:bg-consciousness group-hover:shadow-sm group-hover:shadow-consciousness/50 transition-all duration-300"></span>
                  <span className="font-mono"><EMAIL></span>
                </a>
              </div>
            </div>
          </div>

          {/* Footer Links - Reduced spacing */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-2">
              <Typography
                variant="xs"
                weight="semibold"
                color="consciousness"
                className="pb-0.5 border-b border-consciousness/25 relative"
              >
                {section.title}
                <div className="absolute bottom-0 left-0 w-6 h-0.5 bg-gradient-to-r from-consciousness to-harmony rounded-full"></div>
              </Typography>
              <ul className="space-y-1">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="group block transition-all duration-300 hover:translate-x-1 py-0.5 -mx-1 rounded-lg hover:bg-consciousness/5"
                    >
                      <Typography
                        variant="xs"
                        className="group-hover:text-consciousness transition-colors font-medium text-white/80 group-hover:font-semibold leading-tight"
                      >
                        {link.name}
                      </Typography>
                      <Typography
                        variant="xs"
                        className="group-hover:text-white/70 transition-colors text-white/50 leading-tight text-[10px]"
                      >
                        {link.description}
                      </Typography>
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar - Reduced padding */}
        <div className="border-t border-consciousness/25 pt-2 relative">
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/40 to-transparent"></div>

          <div className="flex flex-col sm:flex-row justify-between items-center space-y-1 sm:space-y-0">
            <Typography variant="xs" className="text-white/60 font-mono">
              © {currentYear} SymbioWave Corporation. All rights reserved.
            </Typography>
            <Typography variant="xs" className="text-white/60 font-mono">
              Powered by ACI Technology
            </Typography>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;