import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import analyticsController from '../controllers/analyticsController';

const router = Router();

// All analytics routes require authentication
router.use(authenticateToken as any);

/**
 * @swagger
 * /analytics/dashboard:
 *   get:
 *     summary: Get dashboard analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard analytics retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/dashboard', analyticsController.getDashboardMetrics as any);

/**
 * @swagger
 * /analytics/usage:
 *   get:
 *     summary: Get usage analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d]
 *           default: 30d
 *     responses:
 *       200:
 *         description: Usage analytics retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/usage', analyticsController.getUsageStatistics as any);

/**
 * @swagger
 * /analytics/workflows/{id}/metrics:
 *   get:
 *     summary: Get detailed workflow metrics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d]
 *           default: 30d
 *     responses:
 *       200:
 *         description: Workflow metrics retrieved successfully
 *       404:
 *         description: Workflow not found
 *       401:
 *         description: Unauthorized
 */
router.get('/workflows/:id/metrics', analyticsController.getWorkflowMetrics as any);

export default router;
