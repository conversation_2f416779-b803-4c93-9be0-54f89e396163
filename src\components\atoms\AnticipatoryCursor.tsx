
import React, { useState, useEffect, useRef, useCallback } from 'react';

interface AnticipatoryCursorProps {
  children: React.ReactNode;
  className?: string;
  glowColor?: 'consciousness' | 'harmony' | 'creativity' | 'intuition' | 'platinum' | 'aurora' | 'quantum' | 'transcendence';
  intensity?: 'subtle' | 'medium' | 'intense' | 'enterprise' | 'transcendent';
  enableMagneticPull?: boolean;
  enableBreathing?: boolean;
  enableQuantumField?: boolean;
}

const AnticipatoryCursor: React.FC<AnticipatoryCursorProps> = ({
  children,
  className = '',
  glowColor = 'quantum',
  intensity = 'transcendent',
  enableMagneticPull = true,
  enableBreathing = true,
  enableQuantumField = true
}) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [targetPosition, setTargetPosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const [anticipation, setAnticipation] = useState(0);
  const [velocity, setVelocity] = useState({ x: 0, y: 0 });
  const [pressure, setPressure] = useState(0);
  const [breathingPhase, setBreathingPhase] = useState(0);
  const [quantumField, setQuantumField] = useState<Array<{id: number, x: number, y: number, phase: number, life: number}>>([]);
  const [sparkles, setSparkles] = useState<Array<{id: number, x: number, y: number, life: number, scale: number}>>([]);
  const [magneticElements, setMagneticElements] = useState<Array<{x: number, y: number, strength: number}>>([]);
  
  const lastMousePosition = useRef({ x: 0, y: 0 });
  const animationFrame = useRef<number>();
  const particleId = useRef(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const colorSchemes = {
    consciousness: {
      primary: 'rgb(99, 102, 241)',
      secondary: 'rgb(139, 92, 246)',
      accent: 'rgb(168, 85, 247)',
      glow: 'rgba(99, 102, 241, 0.6)',
      spark: 'rgba(199, 210, 254, 0.9)'
    },
    harmony: {
      primary: 'rgb(34, 197, 94)',
      secondary: 'rgb(16, 185, 129)',
      accent: 'rgb(6, 182, 212)',
      glow: 'rgba(34, 197, 94, 0.6)',
      spark: 'rgba(134, 239, 172, 0.9)'
    },
    creativity: {
      primary: 'rgb(251, 146, 60)',
      secondary: 'rgb(245, 101, 101)',
      accent: 'rgb(244, 63, 94)',
      glow: 'rgba(251, 146, 60, 0.6)',
      spark: 'rgba(254, 215, 170, 0.9)'
    },
    intuition: {
      primary: 'rgb(168, 85, 247)',
      secondary: 'rgb(217, 70, 239)',
      accent: 'rgb(236, 72, 153)',
      glow: 'rgba(168, 85, 247, 0.6)',
      spark: 'rgba(233, 213, 255, 0.9)'
    },
    platinum: {
      primary: 'rgb(148, 163, 184)',
      secondary: 'rgb(203, 213, 225)',
      accent: 'rgb(226, 232, 240)',
      glow: 'rgba(148, 163, 184, 0.5)',
      spark: 'rgba(241, 245, 249, 0.9)'
    },
    aurora: {
      primary: 'rgb(34, 211, 238)',
      secondary: 'rgb(168, 85, 247)',
      accent: 'rgb(251, 146, 60)',
      glow: 'rgba(34, 211, 238, 0.6)',
      spark: 'rgba(165, 243, 252, 0.9)'
    },
    quantum: {
      primary: 'rgb(79, 70, 229)',
      secondary: 'rgb(139, 92, 246)',
      accent: 'rgb(59, 130, 246)',
      glow: 'rgba(79, 70, 229, 0.7)',
      spark: 'rgba(199, 210, 254, 0.95)'
    },
    transcendence: {
      primary: 'rgb(147, 51, 234)',
      secondary: 'rgb(124, 58, 237)',
      accent: 'rgb(109, 40, 217)',
      glow: 'rgba(147, 51, 234, 0.6)',
      spark: 'rgba(196, 181, 253, 0.9)'
    }
  };

  const getIntensityConfig = () => {
    switch (intensity) {
      case 'subtle': return { glow: 25, ripple: 35, particles: 3, magnetism: 0.05 };
      case 'medium': return { glow: 40, ripple: 55, particles: 5, magnetism: 0.08 };
      case 'intense': return { glow: 60, ripple: 85, particles: 8, magnetism: 0.12 };
      case 'enterprise': return { glow: 50, ripple: 75, particles: 6, magnetism: 0.1 };
      case 'transcendent': return { glow: 70, ripple: 100, particles: 12, magnetism: 0.15 };
      default: return { glow: 50, ripple: 75, particles: 6, magnetism: 0.1 };
    }
  };

  const config = getIntensityConfig();
  const colors = colorSchemes[glowColor] || colorSchemes.quantum;

  // Smooth cursor following with easing
  useEffect(() => {
    const smoothFollow = () => {
      setMousePosition(prev => ({
        x: prev.x + (targetPosition.x - prev.x) * 0.15,
        y: prev.y + (targetPosition.y - prev.y) * 0.15
      }));
    };

    const interval = setInterval(smoothFollow, 16);
    return () => clearInterval(interval);
  }, [targetPosition]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const newTarget = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    // Calculate velocity for dynamic effects
    const newVelocity = {
      x: newTarget.x - lastMousePosition.current.x,
      y: newTarget.y - lastMousePosition.current.y
    };

    const speed = Math.sqrt(newVelocity.x ** 2 + newVelocity.y ** 2);
    
    setTargetPosition(newTarget);
    setVelocity(newVelocity);
    setPressure(Math.min(speed * 0.1, 1));
    lastMousePosition.current = newTarget;

    // Generate sparkles on fast movement
    if (speed > 8 && Math.random() > 0.6) {
      setSparkles(prev => [...prev.slice(-15), {
        id: particleId.current++,
        x: newTarget.x + (Math.random() - 0.5) * 30,
        y: newTarget.y + (Math.random() - 0.5) * 30,
        life: 1,
        scale: 0.5 + Math.random() * 0.5
      }]);
    }

    // Magnetic pull effect
    if (enableMagneticPull) {
      const elements = Array.from(e.currentTarget.querySelectorAll('[data-magnetic]'));
      const newMagnetic = elements.map(el => {
        const elRect = el.getBoundingClientRect();
        const centerX = elRect.left + elRect.width / 2 - rect.left;
        const centerY = elRect.top + elRect.height / 2 - rect.top;
        const distance = Math.sqrt((newTarget.x - centerX) ** 2 + (newTarget.y - centerY) ** 2);
        const strength = Math.max(0, 1 - distance / 150);
        return { x: centerX, y: centerY, strength };
      });
      setMagneticElements(newMagnetic);
    }
  }, [enableMagneticPull]);

  // Main animation loop
  useEffect(() => {
    const animate = () => {
      const time = Date.now() * 0.001;
      
      if (isHovered) {
        setAnticipation(prev => Math.min(prev + 0.02, 1));
      } else {
        setAnticipation(prev => Math.max(prev - 0.03, 0));
      }

      // Breathing animation
      if (enableBreathing) {
        setBreathingPhase(time * 2);
      }

      // Update quantum field
      if (enableQuantumField && isHovered) {
        setQuantumField(prev => {
          const updated = prev
            .map(particle => ({ 
              ...particle, 
              phase: particle.phase + 0.1,
              life: particle.life - 0.008 
            }))
            .filter(particle => particle.life > 0);

          if (Math.random() > 0.85 && updated.length < config.particles) {
            const angle = Math.random() * Math.PI * 2;
            const radius = 50 + Math.random() * 100;
            updated.push({
              id: particleId.current++,
              x: mousePosition.x + Math.cos(angle) * radius,
              y: mousePosition.y + Math.sin(angle) * radius,
              phase: 0,
              life: 1
            });
          }

          return updated;
        });
      }

      // Update sparkles
      setSparkles(prev => prev
        .map(sparkle => ({ ...sparkle, life: sparkle.life - 0.03 }))
        .filter(sparkle => sparkle.life > 0)
      );

      animationFrame.current = requestAnimationFrame(animate);
    };

    animationFrame.current = requestAnimationFrame(animate);
    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, [isHovered, mousePosition, enableBreathing, enableQuantumField, config.particles]);

  const breathingScale = enableBreathing ? 1 + Math.sin(breathingPhase) * 0.1 : 1;
  const magneticInfluence = magneticElements.reduce((acc, mag) => acc + mag.strength, 0) * config.magnetism;

  const renderQuantumField = () => {
    if (!enableQuantumField) return null;
    
    return quantumField.map(particle => (
      <div
        key={particle.id}
        className="absolute pointer-events-none rounded-full"
        style={{
          left: particle.x - 4,
          top: particle.y - 4,
          width: 8,
          height: 8,
          background: `radial-gradient(circle, ${colors.accent}, transparent)`,
          opacity: particle.life * 0.7 * (0.5 + Math.sin(particle.phase) * 0.5),
          transform: `scale(${particle.life * (0.5 + Math.sin(particle.phase * 2) * 0.3)})`,
          filter: 'blur(1px)',
          boxShadow: `0 0 ${particle.life * 15}px ${colors.glow}`
        }}
      />
    ));
  };

  const renderSparkles = () => {
    return sparkles.map(sparkle => (
      <div
        key={sparkle.id}
        className="absolute pointer-events-none"
        style={{
          left: sparkle.x - 3,
          top: sparkle.y - 3,
          width: 6,
          height: 6,
          background: colors.spark,
          opacity: sparkle.life,
          transform: `scale(${sparkle.scale * sparkle.life}) rotate(${(1 - sparkle.life) * 180}deg)`,
          clipPath: 'polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%)',
          filter: 'blur(0.5px)'
        }}
      />
    ));
  };

  const renderRipples = () => {
    return Array.from({ length: 4 }, (_, i) => (
      <div
        key={i}
        className="absolute pointer-events-none rounded-full border transition-all duration-1000 ease-out"
        style={{
          left: mousePosition.x - config.ripple / 2,
          top: mousePosition.y - config.ripple / 2,
          width: config.ripple,
          height: config.ripple,
          borderColor: colors.primary,
          borderWidth: `${1 + i * 0.3}px`,
          opacity: isHovered ? (0.8 - i * 0.15) * anticipation * breathingScale : 0,
          transform: `scale(${(isHovered ? 1 + i * 0.25 + anticipation * 0.4 + magneticInfluence : 0.2) * breathingScale})`,
          transitionDelay: `${i * 80}ms`,
          filter: `blur(${i * 0.5}px)`,
          background: `radial-gradient(circle, transparent 60%, ${colors.glow}${Math.round((0.3 - i * 0.05) * 255).toString(16)} 80%, transparent)`
        }}
      />
    ));
  };

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden cursor-pointer ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ isolation: 'isolate' }}
    >
      {children}
      
      {/* Primary Glow Field */}
      <div 
        className="absolute pointer-events-none transition-all duration-700 ease-out rounded-full"
        style={{
          left: mousePosition.x - config.glow,
          top: mousePosition.y - config.glow,
          width: config.glow * 2,
          height: config.glow * 2,
          background: `radial-gradient(circle, ${colors.glow} 0%, rgba(255,255,255,0.05) 30%, transparent 65%)`,
          opacity: isHovered ? anticipation * breathingScale : 0,
          transform: `scale(${(1 + anticipation * 0.5 + pressure * 0.3 + magneticInfluence) * breathingScale})`,
          filter: `blur(${12 + pressure * 8}px)`,
          mixBlendMode: 'screen'
        }}
      />

      {/* Secondary Aura */}
      <div 
        className="absolute pointer-events-none transition-all duration-500 ease-out rounded-full"
        style={{
          left: mousePosition.x - config.glow * 0.7,
          top: mousePosition.y - config.glow * 0.7,
          width: config.glow * 1.4,
          height: config.glow * 1.4,
          background: `conic-gradient(from ${Date.now() * 0.1}deg, ${colors.primary}30, ${colors.secondary}30, ${colors.accent}30, ${colors.primary}30)`,
          opacity: isHovered ? anticipation * 0.6 * breathingScale : 0,
          transform: `scale(${(1 + anticipation * 0.3 + magneticInfluence) * breathingScale}) rotate(${Date.now() * 0.05}deg)`,
          filter: 'blur(6px)'
        }}
      />

      {/* Core Cursor */}
      <div 
        className="absolute pointer-events-none transition-all duration-200 ease-out rounded-full"
        style={{
          left: mousePosition.x - 8,
          top: mousePosition.y - 8,
          width: 16,
          height: 16,
          background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary}, ${colors.accent})`,
          opacity: isHovered ? 1 : 0,
          transform: `scale(${(1 + anticipation * 0.8 + pressure * 0.5) * breathingScale})`,
          boxShadow: `
            0 0 ${anticipation * 30 + pressure * 15}px ${colors.glow},
            inset 0 0 8px rgba(255,255,255,0.4),
            0 0 ${anticipation * 60}px ${colors.glow}50
          `,
          border: `2px solid rgba(255,255,255,0.3)`,
          backdropFilter: 'blur(2px)'
        }}
      />

      {/* Inner Core */}
      <div 
        className="absolute pointer-events-none transition-all duration-150 ease-out rounded-full"
        style={{
          left: mousePosition.x - 3,
          top: mousePosition.y - 3,
          width: 6,
          height: 6,
          background: `radial-gradient(circle, rgba(255,255,255,0.9), ${colors.spark})`,
          opacity: isHovered ? anticipation : 0,
          transform: `scale(${(1 + pressure * 0.8) * breathingScale})`,
          filter: 'blur(0.5px)'
        }}
      />

      {/* Ripple Effects */}
      {renderRipples()}

      {/* Quantum Field */}
      {renderQuantumField()}

      {/* Sparkles */}
      {renderSparkles()}
      
      {/* Velocity Trail */}
      {Math.abs(velocity.x) + Math.abs(velocity.y) > 15 && (
        <div
          className="absolute pointer-events-none transition-all duration-300 ease-out"
          style={{
            left: mousePosition.x - Math.abs(velocity.x) * 0.4,
            top: mousePosition.y - Math.abs(velocity.y) * 0.4,
            width: Math.abs(velocity.x) * 0.8,
            height: Math.abs(velocity.y) * 0.8,
            background: `linear-gradient(${Math.atan2(velocity.y, velocity.x) * 180 / Math.PI}deg, transparent, ${colors.accent}60, ${colors.primary}40, transparent)`,
            opacity: isHovered ? anticipation * 0.7 : 0,
            filter: 'blur(3px)',
            borderRadius: '50%',
            transform: `scale(${breathingScale})`
          }}
        />
      )}

      {/* Ambient Field */}
      <div 
        className="absolute pointer-events-none transition-all duration-1000 ease-out rounded-full"
        style={{
          left: mousePosition.x - config.glow * 1.5,
          top: mousePosition.y - config.glow * 1.5,
          width: config.glow * 3,
          height: config.glow * 3,
          background: `radial-gradient(circle, transparent 70%, ${colors.glow}10 85%, transparent)`,
          opacity: isHovered ? anticipation * 0.3 * breathingScale : 0,
          transform: `scale(${(1 + anticipation * 0.2) * breathingScale})`,
          filter: 'blur(20px)'
        }}
      />
    </div>
  );
};

export default AnticipatoryCursor;
