
import React from 'react';
import { Brain, Zap, Network, Shield, Clock, TrendingUp, Users, Globe } from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';

const SymbioAutomateFeatures: React.FC = () => {
  const features = [
    {
      icon: <Brain className="w-8 h-8" />,
      title: 'ACI-Powered Intelligence',
      description: 'Artificial Cellular Intelligence adapts workflows in real-time based on changing conditions and data patterns.',
      color: 'consciousness' as const
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: 'Lightning-Fast Execution',
      description: 'Process thousands of tasks simultaneously with quantum-optimized performance and minimal latency.',
      color: 'harmony' as const
    },
    {
      icon: <Network className="w-8 h-8" />,
      title: 'Seamless Integrations',
      description: 'Connect with 500+ platforms including CRM, ERP, marketing tools, and custom APIs.',
      color: 'creativity' as const
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: 'Enterprise Security',
      description: 'Bank-grade encryption, compliance monitoring, and secure data handling across all workflows.',
      color: 'consciousness' as const
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: 'Smart Scheduling',
      description: 'Intelligent workflow scheduling that optimizes resource usage and minimizes conflicts.',
      color: 'harmony' as const
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: 'Predictive Analytics',
      description: 'Advanced analytics and forecasting to optimize processes before bottlenecks occur.',
      color: 'creativity' as const
    }
  ];

  const useCases = [
    {
      title: 'Lead Qualification',
      description: 'Automatically score, route, and nurture leads based on behavioral data and demographics.',
      metrics: ['85% faster qualification', '40% higher conversion', '24/7 processing']
    },
    {
      title: 'Customer Onboarding',
      description: 'Streamline new customer setup with automated document processing and system provisioning.',
      metrics: ['70% time reduction', '99% accuracy rate', 'Zero manual errors']
    },
    {
      title: 'Data Processing',
      description: 'Transform, validate, and sync data across multiple systems with intelligent error handling.',
      metrics: ['10x faster processing', '99.9% uptime', 'Real-time sync']
    }
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-8">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            color="consciousness"
            className="mb-4"
          >
            Intelligent Automation Features
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            Powered by Artificial Cellular Intelligence, SymbioAutomate delivers 
            enterprise-grade automation that thinks, adapts, and evolves.
          </Typography>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <Card 
              key={feature.title}
              variant="neural" 
              className="p-8 border-consciousness/20 hover:border-consciousness/40 transition-all duration-500 rounded-[24px] group"
            >
              <div className={`w-16 h-16 rounded-full bg-${feature.color}/15 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                {feature.icon}
              </div>
              <Typography variant="lg" weight="semibold" color={feature.color} className="mb-4">
                {feature.title}
              </Typography>
              <Typography variant="sm" color="secondary" className="leading-relaxed">
                {feature.description}
              </Typography>
            </Card>
          ))}
        </div>

        {/* Use Cases */}
        <div className="mb-20">
          <Typography 
            as="h3" 
            variant="2xl" 
            weight="bold" 
            color="consciousness"
            className="text-center mb-12"
          >
            Proven Use Cases
          </Typography>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => (
              <Card 
                key={useCase.title}
                variant="quantum" 
                className="p-8 border-consciousness/30 rounded-[24px]"
              >
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                  {useCase.title}
                </Typography>
                <Typography variant="sm" color="secondary" className="mb-6 leading-relaxed">
                  {useCase.description}
                </Typography>
                <div className="space-y-2">
                  {useCase.metrics.map((metric, metricIndex) => (
                    <div key={metricIndex} className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-harmony rounded-full"></div>
                      <Typography variant="xs" color="harmony" weight="medium">
                        {metric}
                      </Typography>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SymbioAutomateFeatures;
