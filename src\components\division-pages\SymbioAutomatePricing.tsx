
import React from 'react';
import { Check, Zap, Crown, Rocket } from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import Button from '../atoms/Button';

const SymbioAutomatePricing: React.FC = () => {
  const plans = [
    {
      name: 'Starter',
      price: '$99',
      period: '/month',
      description: 'Perfect for small teams getting started with automation',
      icon: <Zap className="w-6 h-6" />,
      color: 'harmony' as const,
      features: [
        '10 Active Workflows',
        '1,000 Tasks/Month',
        'Basic Integrations',
        'Email Support',
        '99.5% Uptime SLA',
        'Standard Analytics'
      ],
      popular: false
    },
    {
      name: 'Professional',
      price: '$299',
      period: '/month',
      description: 'Advanced automation for growing businesses',
      icon: <Rocket className="w-6 h-6" />,
      color: 'consciousness' as const,
      features: [
        '50 Active Workflows',
        '10,000 Tasks/Month',
        'Premium Integrations',
        'Priority Support',
        '99.9% Uptime SLA',
        'Advanced Analytics',
        'Custom Templates',
        'Team Collaboration'
      ],
      popular: true
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      period: 'pricing',
      description: 'Unlimited automation for large organizations',
      icon: <Crown className="w-6 h-6" />,
      color: 'creativity' as const,
      features: [
        'Unlimited Workflows',
        'Unlimited Tasks',
        'Custom Integrations',
        'Dedicated Support',
        '99.99% Uptime SLA',
        'White-label Solution',
        'SSO & SAML',
        'Custom Development'
      ],
      popular: false
    }
  ];

  const addOns = [
    { name: 'Additional Workflows', price: '$10/month per workflow' },
    { name: 'Extra Task Volume', price: '$0.05 per additional task' },
    { name: 'Premium Support', price: '$500/month' },
    { name: 'Custom Integration', price: '$2,500 one-time' }
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-8">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            color="consciousness"
            className="mb-4"
          >
            Transparent Pricing
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            Choose the perfect plan for your automation needs. 
            All plans include our core ACI technology and 24/7 monitoring.
          </Typography>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <Card 
              key={plan.name}
              variant={plan.popular ? "quantum" : "neural"}
              className={`p-8 rounded-[24px] relative ${
                plan.popular ? 'border-consciousness/40 scale-105' : 'border-consciousness/20'
              } transition-all duration-500 hover:scale-105`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2 px-4 py-2 bg-consciousness rounded-[12px]">
                  <Typography variant="xs" weight="semibold" color="inverted">
                    Most Popular
                  </Typography>
                </div>
              )}

              <div className="text-center mb-8">
                <div className={`w-16 h-16 rounded-full bg-${plan.color}/15 flex items-center justify-center mx-auto mb-4`}>
                  {plan.icon}
                </div>
                <Typography variant="xl" weight="bold" color={plan.color} className="mb-2">
                  {plan.name}
                </Typography>
                <div className="flex items-center justify-center mb-4">
                  <Typography variant="3xl" weight="bold" color="primary">
                    {plan.price}
                  </Typography>
                  <Typography variant="sm" color="tertiary" className="ml-2">
                    {plan.period}
                  </Typography>
                </div>
                <Typography variant="sm" color="secondary" className="leading-relaxed">
                  {plan.description}
                </Typography>
              </div>

              <div className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-harmony flex-shrink-0" />
                    <Typography variant="sm" color="secondary">
                      {feature}
                    </Typography>
                  </div>
                ))}
              </div>

              <Button 
                variant={plan.popular ? "quantum" : "outline-quantum"}
                size="lg"
                className="w-full rounded-[16px]"
              >
                {plan.name === 'Enterprise' ? 'Contact Sales' : 'Start Free Trial'}
              </Button>
            </Card>
          ))}
        </div>

        {/* Add-ons */}
        <div className="max-w-4xl mx-auto">
          <Typography 
            as="h3" 
            variant="xl" 
            weight="semibold" 
            color="consciousness"
            className="text-center mb-8"
          >
            Add-ons & Extras
          </Typography>
          
          <Card variant="neural" className="p-8 border-consciousness/20 rounded-[24px]">
            <div className="grid md:grid-cols-2 gap-6">
              {addOns.map((addon, index) => (
                <div key={addon.name} className="flex items-center justify-between">
                  <Typography variant="sm">{addon.name}</Typography>
                  <Typography variant="sm" weight="semibold" color="consciousness">
                    {addon.price}
                  </Typography>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Typography variant="lg" color="secondary" className="mb-8">
            All plans include a 14-day free trial. No credit card required.
          </Typography>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="quantum" size="lg" className="rounded-[16px]">
              Start Free Trial
            </Button>
            <Button variant="outline-quantum" size="lg" className="rounded-[16px]">
              Schedule Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SymbioAutomatePricing;
