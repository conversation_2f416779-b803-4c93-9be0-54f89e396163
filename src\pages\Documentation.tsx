
import React from 'react';
import { Book, Code, Zap, FileText, Globe, Users } from 'lucide-react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';

const Documentation: React.FC = () => {
  const docSections = [
    {
      title: 'Getting Started',
      description: 'Quick setup guides and tutorials',
      icon: <Zap className="w-6 h-6" />,
      items: ['Installation Guide', 'First Workflow', 'Basic Configuration', 'Quick Start Tutorial']
    },
    {
      title: 'API Reference',
      description: 'Complete API documentation',
      icon: <Code className="w-6 h-6" />,
      items: ['REST API', 'GraphQL API', 'Webhooks', 'Authentication']
    },
    {
      title: 'Integrations',
      description: 'Connect with your favorite tools',
      icon: <Globe className="w-6 h-6" />,
      items: ['CRM Integrations', 'Marketing Tools', 'Database Connectors', 'Custom APIs']
    },
    {
      title: 'Best Practices',
      description: 'Optimization tips and patterns',
      icon: <Users className="w-6 h-6" />,
      items: ['Workflow Design', 'Performance Optimization', 'Security Guidelines', 'Troubleshooting']
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="py-20">
          <div className="container mx-auto px-8">
            <div className="text-center mb-16">
              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="consciousness"
                className="mb-6"
              >
                Documentation
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Everything you need to build, deploy, and scale with SymbioWave
              </Typography>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {docSections.map((section, index) => (
                <Card 
                  key={section.title}
                  variant="neural" 
                  className="p-6 border-consciousness/20 hover:border-consciousness/40 transition-all duration-500 rounded-[24px]"
                >
                  <div className="w-12 h-12 rounded-full bg-consciousness/15 flex items-center justify-center mb-4">
                    {section.icon}
                  </div>
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                    {section.title}
                  </Typography>
                  <Typography variant="sm" color="secondary" className="mb-4">
                    {section.description}
                  </Typography>
                  <div className="space-y-2">
                    {section.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="flex items-center space-x-2">
                        <div className="w-1 h-1 bg-consciousness rounded-full"></div>
                        <Typography variant="xs" color="tertiary">
                          {item}
                        </Typography>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Documentation;
