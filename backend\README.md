# SymbioWave Backend

A robust, professional backend API for the SymbioWave Genesis platform, built with Node.js, TypeScript, Express, and PostgreSQL.

## Features

- **Authentication & Authorization**: JWT-based auth with Google OAuth support
- **Workflow Management**: Complete CRUD operations for AI workflows
- **Real-time Communication**: WebSocket support for live updates
- **Analytics & Metrics**: Comprehensive dashboard and usage analytics
- **AI Readiness Assessment**: Intelligent scoring and recommendations
- **Demo Request Management**: Lead capture and management system
- **Chat System**: ACI-powered conversational interface
- **Database Migrations**: Structured database schema management
- **API Documentation**: Auto-generated Swagger documentation
- **Security**: Rate limiting, CORS, helmet, input validation
- **Monitoring**: Structured logging and error handling

## Tech Stack

- **Runtime**: Node.js 18+
- **Language**: TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL with Knex.js ORM
- **Cache**: Redis
- **Authentication**: JWT + Passport.js
- **Real-time**: Socket.IO
- **Documentation**: Swagger/OpenAPI
- **Security**: Helmet, CORS, Rate Limiting
- **Validation**: Joi
- **Testing**: Jest
- **Process Management**: PM2 (production)

## Project Structure

```
backend/
├── src/
│   ├── config/           # Configuration files
│   ├── controllers/      # Route controllers
│   ├── middleware/       # Express middleware
│   ├── migrations/       # Database migrations
│   ├── routes/          # API routes
│   ├── services/        # Business logic services
│   ├── types/           # TypeScript type definitions
│   ├── utils/           # Utility functions
│   └── server.ts        # Main server file
├── tests/               # Test files
├── logs/               # Application logs
├── uploads/            # File uploads
└── dist/               # Compiled JavaScript
```

## Getting Started

### Prerequisites

- Node.js 18 or higher
- PostgreSQL 12 or higher
- Redis 6 or higher
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database Setup**
   ```bash
   # Create database
   createdb symbiowave_db
   
   # Run migrations
   npm run migrate
   
   # Seed data (optional)
   npm run seed
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

The server will start on `http://localhost:3001`

### Environment Variables

Key environment variables to configure:

```env
# Server
NODE_ENV=development
PORT=3001
HOST=localhost

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/symbiowave_db

# JWT
JWT_SECRET=your_super_secure_jwt_secret
JWT_REFRESH_SECRET=your_refresh_secret

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Email
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## API Documentation

When running in development mode, API documentation is available at:
- **Swagger UI**: `http://localhost:3001/api-docs`
- **Health Check**: `http://localhost:3001/health`

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/google` - Google OAuth
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Workflows
- `GET /api/workflows` - List user workflows
- `POST /api/workflows` - Create workflow
- `GET /api/workflows/:id` - Get workflow details
- `PUT /api/workflows/:id` - Update workflow
- `DELETE /api/workflows/:id` - Delete workflow
- `POST /api/workflows/:id/execute` - Execute workflow
- `GET /api/workflows/:id/metrics` - Get workflow metrics

### Demo Requests
- `POST /api/demo/request` - Submit demo request
- `GET /api/demo/requests` - List demo requests (admin)
- `PUT /api/demo/requests/:id/status` - Update request status

### Assessments
- `POST /api/assessments/ai-readiness` - Submit AI readiness assessment
- `GET /api/assessments/ai-readiness` - Get user assessments
- `GET /api/assessments/ai-readiness/:id` - Get specific assessment

### Chat
- `POST /api/chat/sessions` - Create chat session
- `GET /api/chat/sessions` - List chat sessions
- `POST /api/chat/sessions/:id/messages` - Send message
- `GET /api/chat/sessions/:id/messages` - Get messages

### Analytics
- `GET /api/analytics/dashboard` - Dashboard metrics
- `GET /api/analytics/workflows/:id/metrics` - Workflow analytics
- `GET /api/analytics/usage` - Usage statistics

## Database Schema

### Core Tables

- **users**: User accounts and profiles
- **workflows**: Workflow definitions and metadata
- **workflow_executions**: Execution history and results
- **workflow_metrics**: Performance and impact metrics
- **ai_readiness_assessments**: Assessment responses and scores
- **demo_requests**: Sales lead management
- **chat_sessions**: Conversation sessions
- **chat_messages**: Individual messages
- **integrations**: External service connections
- **api_keys**: API access management
- **audit_logs**: System activity tracking

## Development

### Scripts

```bash
# Development
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start        # Start production server

# Database
npm run migrate      # Run database migrations
npm run migrate:rollback  # Rollback last migration
npm run seed         # Seed database with sample data

# Testing
npm test             # Run tests
npm run test:watch   # Run tests in watch mode

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
```

### Adding New Features

1. **Create Migration**
   ```bash
   npx knex migrate:make create_new_table
   ```

2. **Add Types**
   ```typescript
   // src/types/index.ts
   export interface NewFeature {
     id: string;
     name: string;
     // ...
   }
   ```

3. **Create Controller**
   ```typescript
   // src/controllers/newFeatureController.ts
   export class NewFeatureController {
     // Implementation
   }
   ```

4. **Add Routes**
   ```typescript
   // src/routes/newFeatureRoutes.ts
   import { Router } from 'express';
   // Route definitions
   ```

5. **Update Server**
   ```typescript
   // src/server.ts
   app.use('/api/new-feature', newFeatureRoutes);
   ```

## Security

### Authentication
- JWT tokens with configurable expiration
- Refresh token rotation
- Google OAuth integration
- Password hashing with bcrypt

### Authorization
- Role-based access control
- Resource ownership validation
- API key authentication for external access

### Security Headers
- Helmet.js for security headers
- CORS configuration
- Rate limiting per IP
- Input validation and sanitization

### Data Protection
- SQL injection prevention via parameterized queries
- XSS protection
- CSRF protection for state-changing operations

## Monitoring & Logging

### Logging
- Structured logging with Winston
- Different log levels (error, warn, info, debug)
- Log rotation and archival
- Request/response logging

### Error Handling
- Global error handler
- Operational vs programming error distinction
- Error reporting to external services (Sentry)

### Health Checks
- Database connectivity
- Redis connectivity
- External service status
- System resource monitoring

## Deployment

### Production Setup

1. **Environment**
   ```bash
   NODE_ENV=production
   # Set all production environment variables
   ```

2. **Build**
   ```bash
   npm run build
   ```

3. **Database**
   ```bash
   npm run migrate
   ```

4. **Start**
   ```bash
   npm start
   # Or use PM2 for process management
   pm2 start ecosystem.config.js
   ```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3001
CMD ["npm", "start"]
```

### Environment-specific Configurations

- **Development**: Hot reload, detailed logging, Swagger docs
- **Testing**: In-memory database, mocked external services
- **Production**: Optimized builds, minimal logging, security hardening

## Performance

### Database Optimization
- Proper indexing on frequently queried columns
- Connection pooling
- Query optimization
- Read replicas for analytics queries

### Caching
- Redis for session storage
- API response caching
- Database query result caching

### Rate Limiting
- Per-IP rate limiting
- Per-user rate limiting for authenticated endpoints
- Different limits for different endpoint types

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

### Code Style
- TypeScript strict mode
- ESLint configuration
- Prettier formatting
- Conventional commit messages

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the API documentation

## Changelog

See CHANGELOG.md for version history and updates.
