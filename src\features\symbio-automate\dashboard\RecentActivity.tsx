
import React from 'react';
import { useIsMobile } from '../../../hooks/use-mobile';
import MorphicContainer from '../../../components/atoms/MorphicContainer';
import Typography from '../../../components/atoms/Typography';
import { Clock, CheckCircle, AlertCircle, Zap, Settings } from 'lucide-react';

const RecentActivity: React.FC = () => {
  const isMobile = useIsMobile();
  
  const activities = [
    {
      id: '1',
      type: 'workflow_completed',
      icon: CheckCircle,
      title: 'Energy Optimization completed',
      description: 'Saved 12.3kWh, reduced CO₂ by 0.8t',
      timestamp: '2 minutes ago',
      color: 'symbioautomate'
    },
    {
      id: '2',
      type: 'aci_optimization',
      icon: Zap,
      title: 'ACI optimization applied',
      description: 'Physarum optimizer enhanced supply chain route',
      timestamp: '15 minutes ago',
      color: 'consciousness'
    },
    {
      id: '3',
      type: 'workflow_error',
      icon: AlertCircle,
      title: 'Workflow error resolved',
      description: 'Manufacturing monitor reconnected',
      timestamp: '1 hour ago',
      color: 'destructive'
    },
    {
      id: '4',
      type: 'workflow_started',
      icon: Settings,
      title: 'Predictive maintenance started',
      description: 'Analyzing sensor data from 23 machines',
      timestamp: '2 hours ago',
      color: 'symbioedge'
    }
  ];

  const getStatusColor = (type: string) => {
    switch (type) {
      case 'workflow_completed': return 'symbioautomate';
      case 'aci_optimization': return 'consciousness';
      case 'workflow_error': return 'destructive';
      case 'workflow_started': return 'symbioedge';
      default: return 'tertiary';
    }
  };

  return (
    <MorphicContainer
      variant="card"
      division="symbioautomate"
      interactive="hover"
      className="p-4 sm:p-6"
    >
      <div className="flex items-center gap-3 mb-4 sm:mb-6">
        <Clock className="text-consciousness-500" size={isMobile ? 18 : 20} />
        <Typography 
          variant={isMobile ? "base" : "lg"} 
          weight="semibold" 
          color="consciousness"
        >
          Recent Activity
        </Typography>
      </div>

      <div className="space-y-2 sm:space-y-3">
        {activities.map((activity) => (
          <div
            key={activity.id}
            className="flex items-start gap-3 p-2 sm:p-3 rounded-organic hover:bg-consciousness-500/5 transition-all duration-medium"
          >
            <activity.icon 
              size={isMobile ? 14 : 16} 
              className={`text-${getStatusColor(activity.type)}-500 mt-1 flex-shrink-0`}
            />
            <div className="flex-1 space-y-1 min-w-0">
              <Typography 
                variant="sm" 
                weight="medium" 
                className="truncate"
              >
                {activity.title}
              </Typography>
              <Typography 
                variant="xs" 
                color="secondary" 
                className="leading-relaxed line-clamp-2"
              >
                {activity.description}
              </Typography>
              <Typography variant="xs" color="tertiary">
                {activity.timestamp}
              </Typography>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-consciousness-500/20">
        <Typography variant="xs" color="tertiary" className="text-center">
          All workflow events are logged and monitored
        </Typography>
      </div>
    </MorphicContainer>
  );
};

export default RecentActivity;
