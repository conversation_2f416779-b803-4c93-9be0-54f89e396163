
import React, { useState, useEffect } from 'react';
import { useIsMobile } from '../../../hooks/use-mobile';
import MorphicContainer from '../../../components/atoms/MorphicContainer';
import Typography from '../../../components/atoms/Typography';
import Button from '../../../components/atoms/Button';
import { Play, Pause, Settings, MoreVertical, Activity } from 'lucide-react';
import { fetchUserWorkflows } from '../../../services/supabaseApi';
import type { WorkflowData } from '../../../types/symbioAutomate';

const ActiveWorkflows: React.FC = () => {
  const isMobile = useIsMobile();
  const [workflows, setWorkflows] = useState<WorkflowData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadWorkflows = async () => {
      try {
        const data = await fetchUserWorkflows('user-1');
        setWorkflows(data);
      } catch (error) {
        console.error('Failed to load workflows:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadWorkflows();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'symbioautomate';
      case 'optimizing': return 'consciousness';
      case 'error': return 'destructive';
      case 'idle': return 'tertiary';
      default: return 'tertiary';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return Play;
      case 'optimizing': return Activity;
      case 'error': return Pause;
      case 'idle': return Pause;
      default: return Pause;
    }
  };

  if (loading) {
    return (
      <MorphicContainer variant="card" className="p-4 sm:p-6">
        <Typography variant="base" color="secondary">Loading workflows...</Typography>
      </MorphicContainer>
    );
  }

  return (
    <MorphicContainer
      variant="card"
      division="symbioautomate"
      className="p-4 sm:p-6"
    >
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4 sm:mb-6">
        <div className="flex items-center gap-3">
          <Activity className="text-consciousness-500" size={isMobile ? 18 : 20} />
          <Typography 
            variant={isMobile ? "base" : "lg"} 
            weight="semibold" 
            color="consciousness"
          >
            Active Workflows
          </Typography>
        </div>
        
        <Button 
          variant="secondary" 
          size={isMobile ? "sm" : "md"}
          className="w-full sm:w-auto"
        >
          Create New
        </Button>
      </div>

      <div className="space-y-3 sm:space-y-4">
        {workflows.map((workflow) => {
          const StatusIcon = getStatusIcon(workflow.status);
          const statusColor = getStatusColor(workflow.status);
          
          return (
            <div
              key={workflow.id}
              className="p-3 sm:p-4 rounded-organic bg-consciousness-500/5 border border-consciousness-500/20 hover:border-consciousness-500/40 transition-all duration-medium"
            >
              <div className="flex flex-col sm:flex-row sm:items-start gap-3">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-${statusColor}-500/20 flex items-center justify-center flex-shrink-0`}>
                    <StatusIcon 
                      size={isMobile ? 14 : 16} 
                      className={`text-${statusColor}-500`}
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <Typography 
                      variant={isMobile ? "sm" : "base"} 
                      weight="semibold" 
                      className="truncate"
                    >
                      {workflow.name}
                    </Typography>
                    <Typography 
                      variant="xs" 
                      color="secondary" 
                      className="line-clamp-2 sm:line-clamp-1"
                    >
                      {workflow.description}
                    </Typography>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                  <div className="grid grid-cols-2 sm:flex sm:gap-4 gap-2 text-center">
                    <div>
                      <Typography variant="xs" weight="semibold" color="symbioautomate">
                        {workflow.performance.executionCount}
                      </Typography>
                      <Typography variant="xs" color="tertiary">Executions</Typography>
                    </div>
                    <div>
                      <Typography variant="xs" weight="semibold" color="consciousness">
                        {workflow.performance.successRate}%
                      </Typography>
                      <Typography variant="xs" color="tertiary">Success</Typography>
                    </div>
                    {!isMobile && (
                      <>
                        <div>
                          <Typography variant="xs" weight="semibold" color="symbioimpact">
                            {workflow.performance.co2Reduction}T
                          </Typography>
                          <Typography variant="xs" color="tertiary">CO₂ Saved</Typography>
                        </div>
                        <div>
                          <Typography variant="xs" weight="semibold" color="consciousness">
                            {workflow.performance.aciOptimizations}
                          </Typography>
                          <Typography variant="xs" color="tertiary">ACI Opts</Typography>
                        </div>
                      </>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button variant="tertiary" size="sm" className="p-2">
                      <Settings size={14} />
                    </Button>
                    <Button variant="tertiary" size="sm" className="p-2">
                      <MoreVertical size={14} />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </MorphicContainer>
  );
};

export default ActiveWorkflows;
