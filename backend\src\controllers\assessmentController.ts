import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import db from '../utils/database';
import { AuthenticatedRequest } from '../middleware/auth';
import { ValidationError, NotFoundError } from '../types';

export class AssessmentController {
  async submitAIReadinessAssessment(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { responses, company_info } = req.body;
      const userId = req.user?.id;

      if (!responses || typeof responses !== 'object') {
        throw new ValidationError('Assessment responses are required');
      }

      // Calculate AI readiness score
      const score = this.calculateReadinessScore(responses);
      const recommendations = this.generateRecommendations(score, responses);
      const timeline = this.estimateTimeline(score);

      const assessmentData = {
        id: uuidv4(),
        user_id: userId || null,
        responses: JSON.stringify(responses),
        company_info: company_info ? JSON.stringify(company_info) : null,
        score,
        recommendations: JSON.stringify(recommendations),
        timeline_estimate: timeline,
        created_at: new Date(),
      };

      const [assessment] = await db('ai_readiness_assessments')
        .insert(assessmentData)
        .returning('*');

      res.status(201).json({
        success: true,
        data: {
          ...assessment,
          responses: JSON.parse(assessment.responses),
          company_info: assessment.company_info ? JSON.parse(assessment.company_info) : null,
          recommendations: JSON.parse(assessment.recommendations),
        },
        message: 'AI readiness assessment completed successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async getAssessments(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { page = 1, limit = 20 } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      let query = db('ai_readiness_assessments')
        .select('id', 'score', 'timeline_estimate', 'created_at')
        .orderBy('created_at', 'desc')
        .limit(Number(limit))
        .offset(offset);

      if (userId) {
        query = query.where({ user_id: userId });
      }

      const assessments = await query;
      const totalQuery = db('ai_readiness_assessments').count('* as count');
      if (userId) {
        totalQuery.where({ user_id: userId });
      }
      const [{ count }] = await totalQuery;

      res.json({
        success: true,
        data: {
          assessments,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: Number(count),
            pages: Math.ceil(Number(count) / Number(limit)),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async getAssessment(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      let query = db('ai_readiness_assessments').where({ id });
      if (userId) {
        query = query.where({ user_id: userId });
      }

      const assessment = await query.first();

      if (!assessment) {
        throw new NotFoundError('Assessment not found');
      }

      res.json({
        success: true,
        data: {
          ...assessment,
          responses: JSON.parse(assessment.responses),
          company_info: assessment.company_info ? JSON.parse(assessment.company_info) : null,
          recommendations: JSON.parse(assessment.recommendations),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  private calculateReadinessScore(responses: any): number {
    // Simple scoring algorithm - can be enhanced with more sophisticated logic
    const weights = {
      current_automation: 0.2,
      data_quality: 0.25,
      technical_infrastructure: 0.2,
      team_readiness: 0.15,
      budget_allocation: 0.1,
      change_management: 0.1,
    };

    let totalScore = 0;
    let totalWeight = 0;

    for (const [key, weight] of Object.entries(weights)) {
      if (responses[key] !== undefined) {
        const value = Number(responses[key]) || 0;
        totalScore += value * weight;
        totalWeight += weight;
      }
    }

    return totalWeight > 0 ? Math.round((totalScore / totalWeight) * 100) : 0;
  }

  private generateRecommendations(score: number, responses: any): string[] {
    const recommendations: string[] = [];

    if (score < 30) {
      recommendations.push('Start with basic process documentation and data collection');
      recommendations.push('Invest in foundational digital infrastructure');
      recommendations.push('Begin team training on automation concepts');
    } else if (score < 60) {
      recommendations.push('Implement pilot automation projects in low-risk areas');
      recommendations.push('Establish data governance and quality processes');
      recommendations.push('Develop internal automation capabilities');
    } else if (score < 80) {
      recommendations.push('Scale successful automation initiatives across departments');
      recommendations.push('Integrate AI-powered decision making tools');
      recommendations.push('Establish center of excellence for automation');
    } else {
      recommendations.push('Explore advanced AI and machine learning applications');
      recommendations.push('Consider industry-specific automation solutions');
      recommendations.push('Share best practices and mentor other organizations');
    }

    return recommendations;
  }

  private estimateTimeline(score: number): string {
    if (score < 30) {
      return '12-18 months for foundational readiness';
    } else if (score < 60) {
      return '6-12 months for pilot implementations';
    } else if (score < 80) {
      return '3-6 months for scaled deployment';
    } else {
      return '1-3 months for advanced implementations';
    }
  }
}

export default new AssessmentController();
