
import React from 'react';
import { Star, Quote } from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';

const SymbioAutomateTestimonials: React.FC = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      role: 'VP of Operations',
      company: 'TechCorp Solutions',
      content: 'SymbioAutomate transformed our lead qualification process. We now process 3x more leads with 40% higher conversion rates. The ACI technology is simply revolutionary.',
      rating: 5,
      avatar: '/api/placeholder/64/64'
    },
    {
      name: '<PERSON>',
      role: 'CEO',
      company: 'Digital Marketing Pro',
      content: 'The intelligence behind SymbioAutomate is incredible. It adapts to our changing campaigns automatically. Our team can focus on strategy while automation handles execution.',
      rating: 5,
      avatar: '/api/placeholder/64/64'
    },
    {
      name: '<PERSON>',
      role: 'Director of IT',
      company: 'Enterprise Solutions Inc',
      content: 'Implementation was seamless, and the ROI was immediate. We saved over 2,000 hours in the first quarter alone. The enterprise features are exactly what we needed.',
      rating: 5,
      avatar: '/api/placeholder/64/64'
    }
  ];

  const stats = [
    { value: '500+', label: 'Enterprise Clients' },
    { value: '99.7%', label: 'Customer Satisfaction' },
    { value: '10M+', label: 'Tasks Automated' },
    { value: '24/7', label: 'Support Available' }
  ];

  return (
    <section className="py-20 bg-abyssal-base/30">
      <div className="container mx-auto px-8">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            color="consciousness"
            className="mb-4"
          >
            Trusted by Leading Organizations
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            See how businesses are transforming their operations with SymbioAutomate
          </Typography>
        </div>

        {/* Testimonials */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {testimonials.map((testimonial, index) => (
            <Card 
              key={testimonial.name}
              variant="neural" 
              className="p-8 border-consciousness/20 rounded-[24px] relative"
            >
              <div className="absolute top-6 right-6 opacity-20">
                <Quote className="w-8 h-8 text-consciousness" />
              </div>
              
              <div className="flex items-center space-x-1 mb-6">
                {[...Array(testimonial.rating)].map((_, starIndex) => (
                  <Star key={starIndex} className="w-5 h-5 text-harmony fill-harmony" />
                ))}
              </div>

              <Typography variant="sm" color="secondary" className="mb-6 leading-relaxed">
                "{testimonial.content}"
              </Typography>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-full bg-consciousness/20 flex items-center justify-center">
                  <Typography variant="sm" weight="bold" color="consciousness">
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </Typography>
                </div>
                <div>
                  <Typography variant="sm" weight="semibold" color="primary">
                    {testimonial.name}
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    {testimonial.role}, {testimonial.company}
                  </Typography>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Stats */}
        <div className="grid md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={stat.label} className="text-center">
              <Typography variant="3xl" weight="bold" color="consciousness" className="mb-2">
                {stat.value}
              </Typography>
              <Typography variant="sm" color="secondary">
                {stat.label}
              </Typography>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <Card variant="quantum" className="p-12 border-consciousness/30 rounded-[32px] max-w-4xl mx-auto">
            <Typography variant="xl" weight="semibold" color="consciousness" className="mb-4">
              Ready to Transform Your Business?
            </Typography>
            <Typography variant="sm" color="secondary" className="mb-8 max-w-2xl mx-auto">
              Join hundreds of forward-thinking companies that have revolutionized their operations with SymbioAutomate. 
              Start your free trial today and experience the power of ACI-driven automation.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-3 bg-consciousness rounded-[16px] text-white hover:bg-consciousness/90 transition-colors duration-300">
                Start Free Trial
              </button>
              <button className="px-8 py-3 border border-consciousness/30 rounded-[16px] text-consciousness hover:bg-consciousness/10 transition-colors duration-300">
                Schedule Demo
              </button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default SymbioAutomateTestimonials;
