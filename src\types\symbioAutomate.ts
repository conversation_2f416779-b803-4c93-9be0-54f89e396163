// SymbioAutomate Type Definitions

export interface WorkflowNode {
  id: string;
  type: 'trigger' | 'action' | 'aci-decision' | 'aci-optimization' | 'integration' | 'logic';
  name: string;
  description?: string;
  position: { x: number; y: number };
  config: Record<string, any>;
  connections: {
    input: string[];
    output: string[];
  };
  status?: 'idle' | 'running' | 'success' | 'error' | 'optimizing';
  division?: 'symbiocore' | 'symbioxchange' | 'symbioedge' | 'symbioimpact';
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  version: number;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  metadata: {
    createdAt: string;
    updatedAt: string;
    createdBy: string;
    tags: string[];
    category: string;
  };
  performance?: WorkflowMetrics;
}

export interface WorkflowConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  sourcePort: string;
  targetPort: string;
  animated?: boolean;
  color?: string;
}

export interface WorkflowMetrics {
  executionCount: number;
  avgExecutionTime: number;
  successRate: number;
  resourcesSaved: number;
  co2Reduction: number;
  costOptimization: number;
  aciOptimizations: number;
}

export interface WorkflowData {
  id: string;
  name: string;
  description: string;
  status: 'running' | 'idle' | 'error' | 'optimizing';
  performance: WorkflowMetrics;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'aci';
  content: string;
  timestamp: string;
  metadata?: {
    workflowAction?: string;
    canvasData?: any;
    attachments?: any[];
  };
}

export interface ACIModel {
  id: string;
  name: string;
  type: 'physarum-optimizer' | 'immune-detector' | 'neural-predictor' | 'swarm-coordinator';
  description: string;
  capabilities: string[];
  performance: {
    accuracy: number;
    speed: number;
    resourceEfficiency: number;
  };
  status: 'active' | 'training' | 'idle';
}

export interface DashboardWidget {
  id: string;
  type: 'kpi' | 'chart' | 'workflow-status' | 'aci-insights' | 'activity-feed';
  title: string;
  config: Record<string, any>;
  position: { x: number; y: number; w: number; h: number };
}

export interface Integration {
  id: string;
  name: string;
  type: 'symbiowave' | 'external';
  division?: 'symbiocore' | 'symbioxchange' | 'symbioedge' | 'symbioimpact';
  icon: string;
  color: string;
  description: string;
  status: 'connected' | 'disconnected' | 'error';
  credentials?: Record<string, any>;
}
