import { Request, Response, NextFunction } from 'express';
import { AppError } from '../types';
import config from '../config';

const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal server error';
  let errors: string[] = [];

  // Handle operational errors
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  }
  
  // Handle validation errors
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation error';
    errors = [error.message];
  }
  
  // Handle JWT errors
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  }
  
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  }
  
  // Handle database errors
  else if (error.message.includes('duplicate key')) {
    statusCode = 409;
    message = 'Resource already exists';
  }
  
  else if (error.message.includes('foreign key')) {
    statusCode = 400;
    message = 'Invalid reference';
  }

  // Log error in development
  if (config.server.env === 'development') {
    console.error('Error:', error);
  }

  // Send error response
  res.status(statusCode).json({
    success: false,
    message,
    errors: errors.length > 0 ? errors : undefined,
    ...(config.server.env === 'development' && {
      stack: error.stack,
    }),
  });
};

export { errorHandler };
export default errorHandler;
