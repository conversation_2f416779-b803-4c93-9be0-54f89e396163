import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcryptjs';
import jwt, { SignOptions } from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { 
  AuthRequest, 
  RegisterRequest, 
  AuthResponse, 
  ValidationError, 
  AuthenticationError,
  ConflictError,
  User,
  UserRole,
  AccessLevel,
  AuthProvider
} from '../types';
import config from '../config';
import db from '../utils/database';
import { AuthenticatedRequest } from '../middleware/auth';

export class AuthController {
  async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email, password, full_name, company_name }: RegisterRequest = req.body;

      // Validate input
      if (!email || !password || !full_name) {
        throw new ValidationError('Email, password, and full name are required');
      }

      if (password.length < 8) {
        throw new ValidationError('Password must be at least 8 characters long');
      }

      // Check if user already exists
      const existingUser = await db('users').where({ email }).first();
      if (existingUser) {
        throw new ConflictError('User with this email already exists');
      }

      // Hash password
      const password_hash = await bcrypt.hash(password, config.security.bcryptRounds);

      // Create user
      const [user] = await db('users')
        .insert({
          id: uuidv4(),
          email,
          password_hash,
          full_name,
          company_name,
          role: UserRole.USER,
          access_level: AccessLevel.BASIC,
          auth_provider: AuthProvider.LOCAL,
          is_verified: false,
          is_active: true,
        })
        .returning('*');

      // Generate tokens
      const access_token = this.generateAccessToken(user.id);
      const refresh_token = this.generateRefreshToken(user.id);

      // Remove password hash from response
      const { password_hash: _, ...userResponse } = user;

      const response: AuthResponse = {
        user: userResponse,
        access_token,
        refresh_token,
        expires_in: 7 * 24 * 60 * 60, // 7 days in seconds
      };

      res.status(201).json({
        success: true,
        data: response,
        message: 'User registered successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email, password }: AuthRequest = req.body;

      if (!email || !password) {
        throw new ValidationError('Email and password are required');
      }

      // Find user
      const user = await db('users')
        .where({ email, is_active: true })
        .first();

      if (!user || !user.password_hash) {
        throw new AuthenticationError('Invalid email or password');
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        throw new AuthenticationError('Invalid email or password');
      }

      // Update last login
      await db('users')
        .where({ id: user.id })
        .update({ last_login: new Date() });

      // Generate tokens
      const access_token = this.generateAccessToken(user.id);
      const refresh_token = this.generateRefreshToken(user.id);

      // Remove password hash from response
      const { password_hash: _, ...userResponse } = user;

      const response: AuthResponse = {
        user: userResponse,
        access_token,
        refresh_token,
        expires_in: 7 * 24 * 60 * 60, // 7 days in seconds
      };

      res.json({
        success: true,
        data: response,
        message: 'Login successful',
      });
    } catch (error) {
      next(error);
    }
  }

  async googleAuth(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // This would be called after successful Google OAuth
      const { email, full_name, avatar_url, google_id } = req.body;

      let user = await db('users').where({ email }).first();

      if (!user) {
        // Create new user from Google OAuth
        [user] = await db('users')
          .insert({
            id: uuidv4(),
            email,
            full_name,
            avatar_url,
            role: UserRole.USER,
            access_level: AccessLevel.BASIC,
            auth_provider: AuthProvider.GOOGLE,
            is_verified: true,
            is_active: true,
          })
          .returning('*');
      } else {
        // Update existing user
        await db('users')
          .where({ id: user.id })
          .update({ 
            last_login: new Date(),
            avatar_url: avatar_url || user.avatar_url,
          });
      }

      // Generate tokens
      const access_token = this.generateAccessToken(user.id);
      const refresh_token = this.generateRefreshToken(user.id);

      const { password_hash: _, ...userResponse } = user;

      const response: AuthResponse = {
        user: userResponse,
        access_token,
        refresh_token,
        expires_in: 7 * 24 * 60 * 60,
      };

      res.json({
        success: true,
        data: response,
        message: 'Google authentication successful',
      });
    } catch (error) {
      next(error);
    }
  }

  async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refresh_token } = req.body;

      if (!refresh_token) {
        throw new ValidationError('Refresh token is required');
      }

      const decoded = jwt.verify(refresh_token, config.jwt.refreshSecret) as any;
      
      const user = await db('users')
        .where({ id: decoded.userId, is_active: true })
        .first();

      if (!user) {
        throw new AuthenticationError('Invalid refresh token');
      }

      // Generate new access token
      const access_token = this.generateAccessToken(user.id);

      res.json({
        success: true,
        data: {
          access_token,
          expires_in: 7 * 24 * 60 * 60,
        },
        message: 'Token refreshed successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async logout(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // In a production environment, you might want to blacklist the token
      // For now, we'll just return success
      res.json({
        success: true,
        message: 'Logout successful',
      });
    } catch (error) {
      next(error);
    }
  }

  async getProfile(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = await db('users')
        .where({ id: req.user!.id })
        .select('id', 'email', 'full_name', 'company_name', 'role', 'access_level', 'auth_provider', 'avatar_url', 'is_verified', 'created_at')
        .first();

      if (!user) {
        throw new AuthenticationError('User not found');
      }

      res.json({
        success: true,
        data: user,
      });
    } catch (error) {
      next(error);
    }
  }

  async updateProfile(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { full_name, company_name } = req.body;
      const userId = req.user!.id;

      const updateData: Partial<User> = {};
      if (full_name) updateData.full_name = full_name;
      if (company_name) updateData.company_name = company_name;

      if (Object.keys(updateData).length === 0) {
        throw new ValidationError('No valid fields to update');
      }

      await db('users')
        .where({ id: userId })
        .update({
          ...updateData,
          updated_at: new Date(),
        });

      const updatedUser = await db('users')
        .where({ id: userId })
        .select('id', 'email', 'full_name', 'company_name', 'role', 'access_level', 'auth_provider', 'avatar_url', 'is_verified', 'created_at', 'updated_at')
        .first();

      res.json({
        success: true,
        data: updatedUser,
        message: 'Profile updated successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  private generateAccessToken(userId: string): string {
    const payload = { userId };
    const secret = config.jwt.secret as string;
    const options: SignOptions = { expiresIn: config.jwt.expiresIn as any };
    return jwt.sign(payload, secret, options);
  }

  private generateRefreshToken(userId: string): string {
    const payload = { userId };
    const secret = config.jwt.refreshSecret as string;
    const options: SignOptions = { expiresIn: config.jwt.refreshExpiresIn as any };
    return jwt.sign(payload, secret, options);
  }
}

export default new AuthController();
