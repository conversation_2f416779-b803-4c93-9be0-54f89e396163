
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface NetworkNode {
  id: string;
  label: string;
  size: number;
  color: string;
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
  connections: string[];
}

interface EcosystemNetworkProps {
  nodes: NetworkNode[];
  isActive: boolean;
  variant?: 'force' | 'circular' | 'hierarchical';
  className?: string;
}

const EcosystemNetwork: React.FC<EcosystemNetworkProps> = ({
  nodes,
  isActive,
  variant = 'force',
  className
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const nodesRef = useRef<NetworkNode[]>([]);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * 2;
      canvas.height = rect.height * 2;
      ctx.scale(2, 2);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize nodes with positions
    const initializeNodes = () => {
      const centerX = canvas.width / 4;
      const centerY = canvas.height / 4;
      
      nodesRef.current = nodes.map((node, index) => ({
        ...node,
        x: variant === 'circular' 
          ? centerX + Math.cos((index / nodes.length) * Math.PI * 2) * 100
          : centerX + (Math.random() - 0.5) * 200,
        y: variant === 'circular'
          ? centerY + Math.sin((index / nodes.length) * Math.PI * 2) * 100
          : centerY + (Math.random() - 0.5) * 200,
        vx: 0,
        vy: 0
      }));
    };

    initializeNodes();

    let time = 0;
    const animate = () => {
      if (!isActive) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      ctx.fillRect(0, 0, canvas.width / 2, canvas.height / 2);
      
      time += 0.016;

      // Apply forces for dynamic layout
      if (variant === 'force') {
        applyForces();
      }

      // Draw connections
      drawConnections(ctx);
      
      // Draw nodes
      drawNodes(ctx, time);

      animationRef.current = requestAnimationFrame(animate);
    };

    const applyForces = () => {
      const centerX = canvas.width / 4;
      const centerY = canvas.height / 4;
      
      nodesRef.current.forEach((node, i) => {
        if (!node.x || !node.y) return;
        
        // Center attraction
        const dcx = centerX - node.x;
        const dcy = centerY - node.y;
        const centerDistance = Math.sqrt(dcx * dcx + dcy * dcy);
        const centerForce = centerDistance * 0.0001;
        
        node.vx! += dcx * centerForce;
        node.vy! += dcy * centerForce;
        
        // Node repulsion
        nodesRef.current.forEach((otherNode, j) => {
          if (i === j || !otherNode.x || !otherNode.y) return;
          
          const dx = node.x! - otherNode.x!;
          const dy = node.y! - otherNode.y!;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            const force = (100 - distance) * 0.01;
            const fx = (dx / distance) * force;
            const fy = (dy / distance) * force;
            
            node.vx! += fx;
            node.vy! += fy;
          }
        });
        
        // Connection attraction
        node.connections.forEach(connectionId => {
          const connectedNode = nodesRef.current.find(n => n.id === connectionId);
          if (connectedNode && connectedNode.x && connectedNode.y) {
            const dx = connectedNode.x - node.x!;
            const dy = connectedNode.y - node.y!;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const force = distance * 0.001;
            
            node.vx! += (dx / distance) * force;
            node.vy! += (dy / distance) * force;
          }
        });
        
        // Apply damping
        node.vx! *= 0.9;
        node.vy! *= 0.9;
        
        // Update position
        node.x! += node.vx!;
        node.y! += node.vy!;
      });
    };

    const drawConnections = (ctx: CanvasRenderingContext2D) => {
      nodesRef.current.forEach(node => {
        if (!node.x || !node.y) return;
        
        node.connections.forEach(connectionId => {
          const connectedNode = nodesRef.current.find(n => n.id === connectionId);
          if (connectedNode && connectedNode.x && connectedNode.y) {
            const isHighlighted = hoveredNode === node.id || hoveredNode === connectionId;
            
            ctx.strokeStyle = isHighlighted 
              ? 'rgba(0, 255, 170, 0.8)' 
              : 'rgba(0, 255, 170, 0.3)';
            ctx.lineWidth = isHighlighted ? 3 : 1;
            
            // Animate connection flow
            const gradient = ctx.createLinearGradient(node.x, node.y, connectedNode.x, connectedNode.y);
            const flowOffset = (time * 2) % 1;
            gradient.addColorStop(flowOffset, 'rgba(0, 255, 170, 0.8)');
            gradient.addColorStop((flowOffset + 0.3) % 1, 'rgba(0, 255, 170, 0.2)');
            gradient.addColorStop((flowOffset + 0.6) % 1, 'rgba(0, 255, 170, 0.8)');
            
            ctx.strokeStyle = gradient;
            ctx.beginPath();
            ctx.moveTo(node.x, node.y);
            ctx.lineTo(connectedNode.x, connectedNode.y);
            ctx.stroke();
          }
        });
      });
    };

    const drawNodes = (ctx: CanvasRenderingContext2D, time: number) => {
      nodesRef.current.forEach(node => {
        if (!node.x || !node.y) return;
        
        const isHovered = hoveredNode === node.id;
        const size = node.size + (isHovered ? 5 : 0) + Math.sin(time * 2) * 2;
        
        // Holographic glow
        ctx.shadowBlur = size * 2;
        ctx.shadowColor = node.color;
        
        // Main node
        ctx.fillStyle = node.color;
        ctx.beginPath();
        ctx.arc(node.x, node.y, size, 0, Math.PI * 2);
        ctx.fill();
        
        // Core light
        ctx.shadowBlur = 0;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.beginPath();
        ctx.arc(node.x, node.y, size * 0.4, 0, Math.PI * 2);
        ctx.fill();
        
        // Label
        if (isHovered) {
          ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
          ctx.font = '12px Inter, sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(node.label, node.x, node.y + size + 20);
        }
      });
    };

    // Mouse interaction
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = (e.clientX - rect.left) * 2;
      const y = (e.clientY - rect.top) * 2;
      
      let hoveredNodeId = null;
      nodesRef.current.forEach(node => {
        if (!node.x || !node.y) return;
        const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
        if (distance < node.size + 10) {
          hoveredNodeId = node.id;
        }
      });
      
      setHoveredNode(hoveredNodeId);
    };

    canvas.addEventListener('mousemove', handleMouseMove);
    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      canvas.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [nodes, isActive, variant, hoveredNode]);

  return (
    <div className={cn("relative w-full h-full overflow-hidden rounded-[20px] glass-quantum border border-consciousness/25", className)}>
      <canvas ref={canvasRef} className="w-full h-full cursor-pointer" />
      
      {/* Legend */}
      <div className="absolute top-4 left-4 space-y-2">
        {nodes.slice(0, 3).map(node => (
          <div key={node.id} className="flex items-center space-x-2 text-xs text-white/70">
            <div 
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: node.color }}
            />
            <span>{node.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EcosystemNetwork;
