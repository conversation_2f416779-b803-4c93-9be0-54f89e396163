
import type { Config } from "tailwindcss";

// Bio-Quantum Synaesthesia Color System
const bioQuantumColors = {
  // Abyssal Canvas Depths
  abyssal: {
    void: '#030508',
    base: '#0A0F1A', 
    deep: '#0F1419',
    elevated: '#141922',
  },
  
  // Consciousness <PERSON><PERSON>-<PERSON><PERSON> (Primary ACI & SymbioCore)
  consciousness: {
    50: '#E0F8FF',
    100: '#B3EFFF',
    200: '#80E5FF',
    300: '#4DDBFF',
    400: '#1AD1FF',
    500: '#00E5FF', // Primary
    600: '#00C4E6',
    700: '#00A3CC',
    800: '#0082B3',
    900: '#006199',
    950: '#004080',
    secondary: '#00FFC2',
    glow: 'rgba(0, 229, 255, 0.6)',
    emissive: 'rgba(0, 255, 194, 0.8)',
  },
  
  // Division-Specific Quantum Resonances
  symbioautomate: {
    50: '#E6FFF2',
    100: '#CCFFE6',
    200: '#99FFCC',
    300: '#66FFB3',
    400: '#33FF99',
    500: '#00FF7F', // Efficiency Emerald
    600: '#00E06D',
    700: '#00C65A',
    800: '#00AD48',
    900: '#009436',
    950: '#007A23',
    glow: 'rgba(0, 255, 127, 0.5)',
  },
  
  symbiolabs: {
    50: '#FFFEF0',
    100: '#FFFCE0',
    200: '#FFF9C2',
    300: '#FFF6A3',
    400: '#FFF385',
    500: '#FFD700', // Innovation Amber
    600: '#FFC300',
    700: '#E6B000',
    800: '#CC9E00',
    900: '#B38B00',
    950: '#997800',
    glow: 'rgba(255, 215, 0, 0.5)',
  },
  
  symbioxchange: {
    50: '#F9E6FF',
    100: '#F2CCFF',
    200: '#E699FF',
    300: '#D966FF',
    400: '#CC33FF',
    500: '#C700FF', // Symbiosis Violet
    600: '#9D00FF',
    700: '#8A00E6',
    800: '#7700CC',
    900: '#6400B3',
    950: '#510099',
    glow: 'rgba(199, 0, 255, 0.5)',
  },
  
  symbioedge: {
    50: '#E6F5FF',
    100: '#CCEBFF',
    200: '#99D6FF',
    300: '#66C2FF',
    400: '#33ADFF',
    500: '#00A2FF', // Frontier Electric Blue
    600: '#33B5FF',
    700: '#0091E6',
    800: '#0080CC',
    900: '#006FB3',
    950: '#005E99',
    glow: 'rgba(0, 162, 255, 0.5)',
  },
  
  symbioimpact: {
    50: '#E6FFF9',
    100: '#CCFFF2',
    200: '#99FFE6',
    300: '#66FFD9',
    400: '#33FFCC',
    500: '#00BFA5', // Sustainability Forest Teal
    600: '#00A080',
    700: '#008A6B',
    800: '#007355',
    900: '#005D40',
    950: '#00472A',
    glow: 'rgba(0, 191, 165, 0.5)',
  },
  
  symbioventures: {
    50: '#FFF2ED',
    100: '#FFE5DB',
    200: '#FFCCB8',
    300: '#FFB294',
    400: '#FF9971',
    500: '#FF7F50', // Catalyst Coral
    600: '#FF6A3D',
    700: '#E6552A',
    800: '#CC4017',
    900: '#B32B04',
    950: '#991600',
    glow: 'rgba(255, 127, 80, 0.5)',
  },
  
  symbioalliance: {
    50: '#E8EFFF',
    100: '#D1DFFF',
    200: '#A3BFFF',
    300: '#759FFF',
    400: '#477FFF',
    500: '#0F52BA', // Global Unity Sapphire
    600: '#2A6DDB',
    700: '#0A4199',
    800: '#083177',
    900: '#062155',
    950: '#041133',
    platinum: '#E5E4E2',
    glow: 'rgba(15, 82, 186, 0.5)',
  },
  
  // Precision Text Hierarchy
  text: {
    primary: '#E0E0E0',
    secondary: '#CED4DA',
    tertiary: '#7F8C9B',
    quaternary: '#6C757D',
    quantum: '#00E5FF',
    inverted: '#030508',
  },
};

// Morphic Design Tokens - Evolved from biological forms
const morphicTokens = {
  borderRadius: {
    // Organic shapes inspired by cellular membranes
    'organic': '40% 60% 70% 30% / 40% 40% 60% 50%',
    'cellular': '63% 37% 54% 46% / 55% 48% 52% 45%',
    'neural': '48% 52% 68% 32% / 42% 61% 39% 58%',
    'quantum': '30% 70% 70% 30% / 30% 30% 70% 70%',
    'biomorphic': '51% 49% 48% 52% / 62% 44% 56% 38%',
    'flow': '60% 40% 30% 70% / 60% 30% 70% 40%',
    'morphic-subtle': '24px 8px 24px 8px',
  },
  
  boxShadow: {
    // Quantum glow effects for different states
    'quantum-primary': '0 0 20px rgba(0, 229, 255, 0.4), 0 0 40px rgba(0, 229, 255, 0.2)',
    'quantum-intense': '0 0 30px rgba(0, 229, 255, 0.6), 0 0 60px rgba(0, 229, 255, 0.3)',
    'holographic': '0 0 30px rgba(0, 229, 255, 0.4), 0 0 60px rgba(0, 162, 255, 0.3), 0 0 90px rgba(199, 0, 255, 0.2)',
    'morphic-depth': '0 12px 40px rgba(0, 0, 0, 0.3), 0 24px 80px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(224, 224, 224, 0.1)',
    'sentient-glow': '0 0 25px rgba(0, 229, 255, 0.4), 0 0 50px rgba(0, 229, 255, 0.2)',
    'division-auto': '0 0 30px rgba(0, 255, 127, 0.4), 0 0 60px rgba(0, 255, 127, 0.2)',
    'division-labs': '0 0 30px rgba(255, 215, 0, 0.4), 0 0 60px rgba(255, 215, 0, 0.2)',
  },
};

// Precision Typography - Adaptive and contextual
const precisionTypography = {
  'nano': ['clamp(10px, 0.625rem + 0.2vw, 11px)', { lineHeight: 'clamp(14px, 0.875rem + 0.2vw, 15px)', letterSpacing: '0.015em' }],
  'micro': ['clamp(11px, 0.6875rem + 0.2vw, 12px)', { lineHeight: 'clamp(16px, 1rem + 0.2vw, 17px)', letterSpacing: '0.01em' }],
  'xs': ['clamp(12px, 0.75rem + 0.3vw, 14px)', { lineHeight: 'clamp(18px, 1.125rem + 0.3vw, 20px)', letterSpacing: '0.005em' }],
  'sm': ['clamp(14px, 0.875rem + 0.3vw, 16px)', { lineHeight: 'clamp(20px, 1.25rem + 0.5vw, 24px)', letterSpacing: '0em' }],
  'base': ['clamp(16px, 1rem + 0.3vw, 18px)', { lineHeight: 'clamp(24px, 1.5rem + 0.5vw, 28px)', letterSpacing: '0.01em' }],
  'lg': ['clamp(18px, 1.125rem + 0.5vw, 22px)', { lineHeight: 'clamp(26px, 1.625rem + 0.5vw, 32px)', letterSpacing: '-0.005em' }],
  'xl': ['clamp(20px, 1.25rem + 0.7vw, 28px)', { lineHeight: 'clamp(28px, 1.75rem + 0.7vw, 38px)', letterSpacing: '-0.01em' }],
  '2xl': ['clamp(24px, 1.5rem + 1vw, 36px)', { lineHeight: 'clamp(32px, 2rem + 1vw, 44px)', letterSpacing: '-0.015em' }],
  '3xl': ['clamp(28px, 1.75rem + 1.2vw, 42px)', { lineHeight: 'clamp(36px, 2.25rem + 1.2vw, 52px)', letterSpacing: '-0.02em' }],
  '4xl': ['clamp(32px, 2rem + 1.5vw, 48px)', { lineHeight: 'clamp(40px, 2.5rem + 1.5vw, 58px)', letterSpacing: '-0.025em' }],
  '5xl': ['clamp(40px, 2.5rem + 2vw, 64px)', { lineHeight: 'clamp(48px, 3rem + 2vw, 72px)', letterSpacing: '-0.03em' }],
  'cosmic': ['clamp(48px, 3rem + 3vw, 80px)', { lineHeight: 'clamp(56px, 3.5rem + 3vw, 88px)', letterSpacing: '-0.035em' }],
  'quantum': ['clamp(64px, 4rem + 4vw, 120px)', { lineHeight: 'clamp(72px, 4.5rem + 4vw, 128px)', letterSpacing: '-0.04em' }],
};

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      // Bio-Quantum Color System Integration
      colors: {
        // Preserve Radix UI integration
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        
        // Bio-Quantum Color Palette
        ...bioQuantumColors,
      },
      
      // Precision Typography System
      fontSize: {
        ...precisionTypography,
      },
      
      // Quantum Font System
      fontFamily: {
        sans: ['Inter Variable', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono Variable', 'Fira Code', 'monospace'],
        display: ['Inter Variable', 'system-ui', 'sans-serif'],
        quantum: ['Inter Variable', 'system-ui', 'sans-serif'],
      },
      
      // Morphic Design Tokens
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        ...morphicTokens.borderRadius,
      },
      
      // Quantum Shadow System
      boxShadow: {
        ...morphicTokens.boxShadow,
      },
      
      // Bio-Quantum Animation System
      keyframes: {
        // Preserve existing animations
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        
        // New Bio-Quantum animations
        "quantum-flicker": {
          "0%, 100%": { opacity: "1", filter: "brightness(1)" },
          "50%": { opacity: "0.9", filter: "brightness(1.1)" },
        },
        "morphic-flow": {
          "0%": { 
            borderRadius: "40% 60% 70% 30% / 40% 40% 60% 50%",
            transform: "rotate(0deg) scale(1)"
          },
          "25%": { 
            borderRadius: "60% 40% 30% 70% / 60% 30% 70% 40%",
            transform: "rotate(1deg) scale(1.01)"
          },
          "50%": { 
            borderRadius: "30% 70% 70% 30% / 30% 70% 30% 70%",
            transform: "rotate(0deg) scale(1.02)"
          },
          "75%": { 
            borderRadius: "70% 30% 40% 60% / 50% 60% 40% 50%",
            transform: "rotate(-1deg) scale(1.01)"
          },
          "100%": { 
            borderRadius: "40% 60% 70% 30% / 40% 40% 60% 50%",
            transform: "rotate(0deg) scale(1)"
          },
        },
        "sentient-pulse": {
          "0%, 100%": { 
            transform: "scale(1)",
            filter: "brightness(1) saturate(1)",
            boxShadow: "0 0 20px rgba(0, 229, 255, 0.4)"
          },
          "50%": { 
            transform: "scale(1.02)",
            filter: "brightness(1.1) saturate(1.1)",
            boxShadow: "0 0 30px rgba(0, 229, 255, 0.6), 0 0 60px rgba(0, 229, 255, 0.3)"
          },
        },
        "anticipatory-glow": {
          "0%": { 
            boxShadow: "0 0 0 rgba(0, 229, 255, 0)",
            filter: "brightness(1)"
          },
          "100%": { 
            boxShadow: "0 0 25px rgba(0, 229, 255, 0.4)",
            filter: "brightness(1.05)"
          },
        },
        "holographic-shift": {
          "0%, 100%": { 
            backgroundPosition: "0% 50%",
            filter: "hue-rotate(0deg)"
          },
          "50%": { 
            backgroundPosition: "100% 50%",
            filter: "hue-rotate(5deg)"
          },
        },
      },
      
      // Animation Utilities
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "quantum-flicker": "quantum-flicker 3s ease-in-out infinite",
        "morphic-flow": "morphic-flow 12s ease-in-out infinite",
        "sentient-pulse": "sentient-pulse 4s ease-in-out infinite",
        "anticipatory-glow": "anticipatory-glow 0.3s ease-out forwards",
        "holographic-shift": "holographic-shift 8s ease-in-out infinite",
      },
      
      // Quantum Timing Functions
      transitionTimingFunction: {
        'sentient': 'cubic-bezier(0.25, 0.1, 0.25, 1.0)',
        'organic': 'cubic-bezier(0.4, 0.0, 0.2, 1)',
        'morphic': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'quantum': 'cubic-bezier(0.23, 1, 0.32, 1)',
      },
      
      // Quantum Duration Scale
      transitionDuration: {
        'instant': '83ms',
        'fast': '137ms',
        'medium': '233ms',
        'slow': '377ms',
        'ultra': '610ms',
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    // Bio-Quantum Component Plugin
    function({ addUtilities, addComponents }) {
      // Quantum interaction utilities
      addUtilities({
        '.anticipatory': {
          transition: 'all 137ms cubic-bezier(0.25, 0.1, 0.25, 1.0)',
          cursor: 'pointer',
        },
        '.anticipatory:hover': {
          animation: 'anticipatory-glow 0.3s ease-out forwards',
        },
        '.quantum-transform': {
          transform: 'translateZ(0)',
          backfaceVisibility: 'hidden',
          perspective: '1000px',
        },
      });
      
      // Morphic field components
      addComponents({
        '.morphic-container': {
          background: 'linear-gradient(135deg, rgba(224, 224, 224, 0.08) 0%, rgba(224, 224, 224, 0.03) 100%)',
          backdropFilter: 'blur(24px) saturate(150%) brightness(1.1)',
          border: '1px solid rgba(224, 224, 224, 0.12)',
          borderRadius: '40% 60% 70% 30% / 40% 40% 60% 50%',
          transition: 'all 233ms cubic-bezier(0.68, -0.55, 0.265, 1.55)',
          position: 'relative',
          overflow: 'hidden',
        },

      });
    },
  ],
} satisfies Config;
