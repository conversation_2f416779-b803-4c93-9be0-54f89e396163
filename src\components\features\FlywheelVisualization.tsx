import React, { useState, useEffect } from 'react';
import { 
  Database, 
  Brain, 
  TrendingUp, 
  Network, 
  Shield, 
  Handshake, 
  Zap,
  ArrowRight,
  Building2,
  Factory,
  Leaf,
  LineChart,
  Globe,
  Microscope,
  ArrowRightLeft,
  Activity,
  Eye,
  Target,
  Layers,
  DollarSign
} from 'lucide-react';

interface FlywheelNode {
  id: string;
  division: string;
  title: string;
  description: string;
  metric: string;
  icon: React.ReactNode;
  color: string;
  angle: number;
  connections: string[];
  revenue: string;
  synergy: string;
}

const SymbioWaveEcosystem: React.FC = () => {
  const [activeNode, setActiveNode] = useState<string | null>(null);
  const [rotationAngle, setRotationAngle] = useState(0);

  const flywheelNodes: FlywheelNode[] = [
    {
      id: 'core',
      division: 'SymbioCore',
      title: 'Core Intelligence Platform',
      description: 'Cloud-based PaaS hosting the full suite of ACI models with robust API access',
      metric: 'Dynamic ACI Models',
      icon: <Brain className="w-6 h-6" />,
      color: 'red',
      angle: 0,
      connections: ['labs', 'xchange', 'automate', 'edge', 'impact'],
      revenue: 'API subscriptions & usage fees',
      synergy: 'Central nervous system powering all divisions'
    },
    {
      id: 'labs',
      division: 'SymbioLabs',
      title: 'Research & Development Hub',
      description: 'Nature-inspired AI research advancing ACI frontiers with world-class scientists',
      metric: 'Biomimetic AI Models',
      icon: <Microscope className="w-6 h-6" />,
      color: 'orange',
      angle: 45,
      connections: ['core', 'ventures'],
      revenue: 'IP licensing & AI consultancy',
      synergy: 'Continuously upgrades SymbioCore intelligence'
    },
    {
      id: 'automate',
      division: 'SymbioAutomate',
      title: 'Intelligent Automation Suite',
      description: 'Enterprise software for dynamic, self-optimizing workflows and operations',
      metric: 'Adaptive Workflows',
      icon: <Factory className="w-6 h-6" />,
      color: 'yellow',
      angle: 90,
      connections: ['core', 'xchange', 'edge'],
      revenue: 'SaaS licenses & implementation',
      synergy: 'Executes deals from SymbioXchange automatically'
    },
    {
      id: 'xchange',
      division: 'SymbioXchange',
      title: 'Global Resource Exchange',
      description: 'AI-driven B2B platform trading resources, waste streams, and underutilized assets',
      metric: 'Circular Economy Deals',
      icon: <ArrowRightLeft className="w-6 h-6" />,
      color: 'green',
      angle: 135,
      connections: ['core', 'automate', 'edge', 'impact', 'alliance'],
      revenue: 'Transaction fees & premium analytics',
      synergy: 'Primary commercial driver generating ecosystem data'
    },
    {
      id: 'edge',
      division: 'SymbioEdge',
      title: 'Distributed Edge Intelligence',
      description: 'Lightweight ACI models on IoT devices for real-time decision-making',
      metric: 'Edge Deployments',
      icon: <Network className="w-6 h-6" />,
      color: 'blue',
      angle: 180,
      connections: ['core', 'xchange', 'automate', 'impact'],
      revenue: 'Software licensing & hardware margins',
      synergy: 'Provides real-time data feeds to entire ecosystem'
    },
    {
      id: 'impact',
      division: 'SymbioImpact',
      title: 'Global Impact Platform',
      description: 'AI-powered sustainability measurement, verification, and compliance reporting',
      metric: 'Verified Impact Reports',
      icon: <Shield className="w-6 h-6" />,
      color: 'indigo',
      angle: 225,
      connections: ['core', 'xchange', 'edge', 'ventures', 'alliance'],
      revenue: 'ESG SaaS & certification services',
      synergy: 'Provides trust layer for all ecosystem transactions'
    },
    {
      id: 'ventures',
      division: 'SymbioVentures',
      title: 'Innovation Investment',
      description: 'Corporate VC investing in symbiotic tech startups with ecosystem access',
      metric: 'Portfolio Companies',
      icon: <DollarSign className="w-6 h-6" />,
      color: 'violet',
      angle: 270,
      connections: ['labs', 'impact', 'alliance'],
      revenue: 'Management fees & carried interest',
      synergy: 'Seeds ecosystem with future partners and customers'
    },
    {
      id: 'alliance',
      division: 'SymbioAlliance',
      title: 'Strategic Partnerships',
      description: 'Strategic partnerships with corporations, governments, and NGOs for scale',
      metric: 'Strategic Partnerships',
      icon: <Handshake className="w-6 h-6" />,
      color: 'pink',
      angle: 315,
      connections: ['xchange', 'impact', 'ventures'],
      revenue: 'Strategic value creation',
      synergy: 'Amplifies entire ecosystem through industry adoption'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setRotationAngle(prev => (prev + 0.2) % 360);
    }, 50);
    
    return () => clearInterval(interval);
  }, []);

  const getNodePosition = (node: FlywheelNode) => {
    const adjustedAngle = (node.angle + rotationAngle) * (Math.PI / 180);
    const radius = 300;
    return {
      x: Math.cos(adjustedAngle) * radius,
      y: Math.sin(adjustedAngle) * radius
    };
  };

  const getColorClasses = (color: string, variant: 'bg' | 'border' | 'text' | 'shadow' = 'bg') => {
    const colors = {
      red: { bg: 'bg-red-500', border: 'border-red-500', text: 'text-red-500', shadow: 'shadow-red-500/20' },
      orange: { bg: 'bg-orange-500', border: 'border-orange-500', text: 'text-orange-500', shadow: 'shadow-orange-500/20' },
      yellow: { bg: 'bg-yellow-500', border: 'border-yellow-500', text: 'text-yellow-500', shadow: 'shadow-yellow-500/20' },
      green: { bg: 'bg-green-500', border: 'border-green-500', text: 'text-green-500', shadow: 'shadow-green-500/20' },
      blue: { bg: 'bg-blue-500', border: 'border-blue-500', text: 'text-blue-500', shadow: 'shadow-blue-500/20' },
      indigo: { bg: 'bg-indigo-500', border: 'border-indigo-500', text: 'text-indigo-500', shadow: 'shadow-indigo-500/20' },
      violet: { bg: 'bg-violet-500', border: 'border-violet-500', text: 'text-violet-500', shadow: 'shadow-violet-500/20' },
      pink: { bg: 'bg-pink-500', border: 'border-pink-500', text: 'text-pink-500', shadow: 'shadow-pink-500/20' }
    };
    return colors[color as keyof typeof colors]?.[variant] || colors.red[variant];
  };

  return (
    <div className="min-h-screen bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 p-8">
      {/* Header */}
      <div className="text-center mb-16">
        <h1 className="text-5xl font-bold text-white mb-6">
          SymbioWave Ecosystem Flywheel
        </h1>
        <p className="text-xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
          Eight synergistic divisions creating a self-reinforcing engine of value through 
          <span className="font-semibold text-cyan-300"> Artificial Cellular Intelligence</span>
        </p>
        <div className="mt-6 text-lg text-blue-200">
          AI for a Symbiotic Future • Powered by Nature's Intelligence
        </div>
      </div>

      {/* Main Flywheel */}
      <div className="relative max-w-7xl mx-auto">
        <div className="relative flex items-center justify-center h-[900px]">
          {/* Background Orbital Rings */}
          <div className="absolute inset-0 flex items-center justify-center opacity-20">
            <div className="w-[650px] h-[650px] border-2 border-dashed border-cyan-300 rounded-full"></div>
            <div className="absolute w-[700px] h-[700px] border border-cyan-200 rounded-full"></div>
            <div className="absolute w-[750px] h-[750px] border border-cyan-100 rounded-full"></div>
          </div>

          {/* Central Core */}
          <div className="absolute inset-0 flex items-center justify-center z-20">
            <div className="w-56 h-56 bg-white/10 backdrop-blur-xl rounded-full shadow-2xl border-4 border-cyan-500 flex flex-col items-center justify-center relative overflow-hidden">
              {/* Animated Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/20 via-blue-500/20 to-purple-600/20 animate-pulse"></div>
              
              {/* Content */}
              <div className="relative z-10 text-center">
                <Building2 className="w-16 h-16 text-cyan-300 mb-3 mx-auto" />
                <div className="text-2xl font-bold text-white mb-1">SymbioWave</div>
                <div className="text-sm text-cyan-200 mb-2">Ecosystem</div>
                <div className="text-xs text-cyan-300 font-medium">ACI Powered</div>
              </div>
              
              {/* Orbital Elements */}
              <div className="absolute top-4 right-4 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>
              <div className="absolute bottom-4 left-4 w-2 h-2 bg-purple-400 rounded-full animate-ping" style={{ animationDelay: '1s' }}></div>
              <div className="absolute top-6 left-6 w-2 h-2 bg-green-400 rounded-full animate-ping" style={{ animationDelay: '2s' }}></div>
            </div>
          </div>

          {/* Dynamic Connection Network */}
          <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 10 }}>
            {flywheelNodes.map(node => {
              const fromPos = getNodePosition(node);
              const centerX = 450;
              const centerY = 450;
              
              return node.connections.map(targetId => {
                const targetNode = flywheelNodes.find(n => n.id === targetId);
                if (!targetNode) return null;
                
                const toPos = getNodePosition(targetNode);
                const isActiveConnection = activeNode === node.id || activeNode === targetId;
                
                return (
                  <g key={`${node.id}-${targetId}`}>
                    {/* Connection Line */}
                    <line
                      x1={centerX + fromPos.x}
                      y1={centerY + fromPos.y}
                      x2={centerX + toPos.x}
                      y2={centerY + toPos.y}
                      stroke={isActiveConnection ? getColorClasses(node.color, 'text').replace('text-', '') : '#e2e8f0'}
                      strokeWidth={isActiveConnection ? "3" : "1"}
                      strokeDasharray={isActiveConnection ? "none" : "4,4"}
                      opacity={isActiveConnection ? "0.8" : "0.3"}
                      className="transition-all duration-300"
                    />
                    
                    {/* Data Flow Animation */}
                    {isActiveConnection && (
                      <circle
                        r="4"
                        fill={getColorClasses(node.color, 'text').replace('text-', '')}
                        opacity="0.8"
                      >
                        <animateMotion
                          dur="2s"
                          repeatCount="indefinite"
                          path={`M ${centerX + fromPos.x} ${centerY + fromPos.y} L ${centerX + toPos.x} ${centerY + toPos.y}`}
                        />
                      </circle>
                    )}
                  </g>
                );
              });
            })}
          </svg>

          {/* Division Nodes */}
          {flywheelNodes.map((node) => {
            const pos = getNodePosition(node);
            const isActive = activeNode === node.id;
            
            return (
              <div
                key={node.id}
                className="absolute transition-all duration-500 cursor-pointer group"
                style={{
                  left: '50%',
                  top: '50%',
                  transform: `translate(-50%, -50%) translate(${pos.x}px, ${pos.y}px) ${isActive ? 'scale(1.1)' : 'scale(1)'}`,
                  zIndex: isActive ? 30 : 15
                }}
                onMouseEnter={() => setActiveNode(node.id)}
                onMouseLeave={() => setActiveNode(null)}
              >
                {/* Node Card */}
                <div className={`w-36 h-36 bg-white/10 backdrop-blur-xl rounded-2xl shadow-xl border-2 hover:shadow-2xl transition-all duration-500 flex flex-col items-center justify-center relative ${getColorClasses(node.color, 'border')} ${getColorClasses(node.color, 'shadow')}`}>
                  {/* Background Gradient */}
                  <div className={`absolute inset-2 rounded-xl bg-gradient-to-br ${getColorClasses(node.color, 'bg')}/20 opacity-50`}></div>
                  
                  {/* Icon */}
                  <div className={`${getColorClasses(node.color, 'text')} mb-2 relative z-10`}>
                    {node.icon}
                  </div>
                  
                  {/* Title */}
                  <div className="text-xs font-bold text-white text-center leading-tight px-2 relative z-10">
                    {node.title}
                  </div>
                  
                  {/* Division Label */}
                  <div className={`text-xs ${getColorClasses(node.color, 'text')} font-medium mt-1 relative z-10`}>
                    {node.division}
                  </div>
                  
                  {/* Pulse Effect */}
                  {isActive && (
                    <div className={`absolute inset-0 rounded-2xl ${getColorClasses(node.color, 'bg')} opacity-20 animate-ping`}></div>
                  )}
                </div>

                {/* Detailed Info Panel */}
                <div className={`absolute top-full left-1/2 transform -translate-x-1/2 mt-6 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none z-40`}>
                  <div className="w-96 p-6 bg-white rounded-2xl shadow-2xl border border-gray-200">
                    {/* Header */}
                    <div className="flex items-center justify-between mb-4">
                      <div className={`text-xl font-bold ${getColorClasses(node.color, 'text')}`}>
                        {node.division}
                      </div>
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${getColorClasses(node.color, 'bg')} text-white`}>
                        Division {flywheelNodes.indexOf(node) + 1}
                      </div>
                    </div>
                    
                    {/* Title */}
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      {node.title}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                      {node.description}
                    </p>
                    
                    {/* Key Metrics */}
                    <div className="grid grid-cols-1 gap-3 mb-4">
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Core Function</div>
                        <div className={`text-sm font-semibold ${getColorClasses(node.color, 'text')}`}>
                          {node.metric}
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-3">
                        <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Revenue Model</div>
                        <div className="text-sm font-medium text-gray-700">
                          {node.revenue}
                        </div>
                      </div>
                    </div>
                    
                    {/* Synergy */}
                    <div className="border-t border-gray-200 pt-3">
                      <div className="text-xs text-gray-500 uppercase tracking-wide mb-2">Ecosystem Synergy</div>
                      <div className="text-sm text-gray-700 leading-relaxed">
                        {node.synergy}
                      </div>
                    </div>
                    
                    {/* Connections Indicator */}
                    <div className="mt-3 flex items-center justify-between">
                      <div className="text-xs text-gray-500">
                        Connected to {node.connections.length} divisions
                      </div>
                      <div className="flex gap-1">
                        {node.connections.map((_, i) => (
                          <div key={i} className={`w-2 h-2 rounded-full ${getColorClasses(node.color, 'bg')}`}></div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Flywheel Mechanics */}
      <div className="max-w-6xl mx-auto mt-20">
        <h2 className="text-3xl font-bold text-white text-center mb-12">
          The Synergistic Flywheel Effect
        </h2>
        
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Left: Flow Description */}
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
              <div>
                <h3 className="font-semibold text-white mb-2">Data Generation</h3>
                <p className="text-blue-200 text-sm">SymbioAutomate and SymbioEdge generate operational data from customers</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
              <div>
                <h3 className="font-semibold text-white mb-2">Intelligence Enhancement</h3>
                <p className="text-blue-200 text-sm">SymbioCore processes data, making ACI models smarter and more valuable</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-violet-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
              <div>
                <h3 className="font-semibold text-white mb-2">Market Connections</h3>
                <p className="text-blue-200 text-sm">SymbioXchange creates better matches, generating more transactions</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
              <div>
                <h3 className="font-semibold text-white mb-2">Trust & Verification</h3>
                <p className="text-blue-200 text-sm">SymbioImpact validates results, building ecosystem credibility</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
              <div>
                <h3 className="font-semibold text-white mb-2">Network Expansion</h3>
                <p className="text-blue-200 text-sm">SymbioAlliance and SymbioVentures bring in more participants</p>
              </div>
            </div>
          </div>
          
          {/* Right: Value Creation */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 p-8 rounded-2xl">
            <h3 className="text-xl font-bold text-white mb-6">Exponential Value Creation</h3>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-200">Network Effects</span>
                <div className="flex gap-1">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="w-3 h-3 bg-cyan-400 rounded-full"></div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-200">Data Intelligence</span>
                <div className="flex gap-1">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="w-3 h-3 bg-green-400 rounded-full"></div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-blue-200">Ecosystem Synergy</span>
                <div className="flex gap-1">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="w-3 h-3 bg-violet-400 rounded-full"></div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-white/10 backdrop-blur-xl rounded-xl border border-white/20">
              <div className="text-2xl font-bold text-cyan-300 mb-2">$500M</div>
              <div className="text-sm text-blue-200">Projected annual revenue run rate by Year 5</div>
            </div>
          </div>
        </div>
      </div> {/* Closing tag for Flywheel Mechanics */}

      {/* Competitive Moat */}
      <div className="max-w-4xl mx-auto mt-20 text-center">
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 p-12 rounded-2xl shadow-xl">
          <h2 className="text-3xl font-bold text-white mb-6">
            Unbeatable Competitive Moat
          </h2>
          <p className="text-lg text-blue-100 leading-relaxed mb-8">
            Our integrated ecosystem creates a self-reinforcing moat. Competitors might copy one division, 
            but they cannot replicate the synergistic value created by eight interconnected, 
            ACI-powered business units working as one.
          </p>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-cyan-500/20 backdrop-blur-xl border border-cyan-400/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Layers className="w-8 h-8 text-cyan-300" />
              </div>
              <h3 className="font-semibold text-white mb-2">Proprietary ACI Technology</h3>
              <p className="text-sm text-blue-200">Nature-inspired AI that evolves beyond traditional models</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500/20 backdrop-blur-xl border border-green-400/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Network className="w-8 h-8 text-green-300" />
              </div>
              <h3 className="font-semibold text-white mb-2">Ecosystem Network Effects</h3>
              <p className="text-sm text-blue-200">Value increases exponentially with each new participant</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-violet-500/20 backdrop-blur-xl border border-violet-400/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="w-8 h-8 text-violet-300" />
              </div>
              <h3 className="font-semibold text-white mb-2">First-Mover Advantage</h3>
              <p className="text-sm text-blue-200">Defining platform for the circular economy transition</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-16 pt-8 border-t border-white/20">
        <div className="text-blue-200 text-sm">
          SymbioWave Ecosystem • Artificial Cellular Intelligence • Building the Symbiotic Economy
        </div>
      </div>
    </div>
  );
};

export default SymbioWaveEcosystem;
