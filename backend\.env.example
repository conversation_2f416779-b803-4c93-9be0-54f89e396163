# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=symbiowave_db
DB_USER=symbiowave_user
DB_PASSWORD=your_secure_password
DATABASE_URL=postgresql://symbiowave_user:your_secure_password@localhost:5432/symbiowave_db

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key_here
JWT_REFRESH_EXPIRES_IN=30d

# Session Configuration
SESSION_SECRET=your_super_secure_session_secret_here

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:3001/auth/google/callback

# Email Configuration (Nodemailer)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=SymbioWave

# File Upload Configuration
UPLOAD_PATH=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/csv

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# External APIs
N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/symbio-assist
N8N_API_URL=https://your-n8n-instance.com/api/v1
N8N_API_KEY=your_n8n_api_key

# SymbioWave Ecosystem APIs
SYMBIO_CORE_API=https://api.symbiocore.com
SYMBIO_XCHANGE_API=https://api.symbioxchange.com
SYMBIO_EDGE_API=https://api.symbioedge.com
SYMBIO_IMPACT_API=https://api.symbioimpact.com

# Monitoring & Analytics
SENTRY_DSN=your_sentry_dsn
ANALYTICS_API_KEY=your_analytics_api_key

# Security
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Feature Flags
ENABLE_SWAGGER=true
ENABLE_METRICS=true
ENABLE_WEBSOCKETS=true
ENABLE_CRON_JOBS=true

# AI/ML Configuration
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret_for_verification
