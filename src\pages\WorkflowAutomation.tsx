import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Button from '../components/atoms/Button';
import Card from '../components/atoms/Card';
import { ArrowRight, Workflow, Brain, Zap, Settings, BarChart3, Shield, Clock, CheckCircle } from 'lucide-react';

const WorkflowAutomation: React.FC = () => {
  const automationTypes = [
    {
      title: "Process Automation",
      description: "Automate repetitive business processes with intelligent decision-making capabilities",
      icon: <Workflow className="w-8 h-8" />,
      features: [
        "Document processing and routing",
        "Approval workflows",
        "Data validation and cleansing",
        "Multi-system integration"
      ],
      benefits: "75% reduction in processing time"
    },
    {
      title: "Cognitive Automation",
      description: "AI-powered automation that can understand, learn, and make complex decisions",
      icon: <Brain className="w-8 h-8" />,
      features: [
        "Natural language processing",
        "Pattern recognition",
        "Predictive analytics",
        "Adaptive learning"
      ],
      benefits: "90% accuracy in decision making"
    },
    {
      title: "Infrastructure Automation",
      description: "Automate IT operations, monitoring, and maintenance tasks",
      icon: <Settings className="w-8 h-8" />,
      features: [
        "Server provisioning",
        "Performance monitoring",
        "Security compliance",
        "Backup and recovery"
      ],
      benefits: "99.9% uptime guarantee"
    },
    {
      title: "Analytics Automation",
      description: "Automated data collection, analysis, and reporting for real-time insights",
      icon: <BarChart3 className="w-8 h-8" />,
      features: [
        "Real-time dashboards",
        "Automated reporting",
        "Anomaly detection",
        "Predictive modeling"
      ],
      benefits: "60% faster insights delivery"
    }
  ];

  const implementationSteps = [
    {
      step: "1",
      title: "Assessment & Planning",
      description: "Comprehensive analysis of your current workflows and automation opportunities",
      duration: "1-2 weeks"
    },
    {
      step: "2",
      title: "Design & Architecture",
      description: "Custom workflow design using ACI technology and best practices",
      duration: "2-3 weeks"
    },
    {
      step: "3",
      title: "Development & Testing",
      description: "Build and test automation workflows in a controlled environment",
      duration: "3-4 weeks"
    },
    {
      step: "4",
      title: "Deployment & Training",
      description: "Roll out automation and train your team on the new processes",
      duration: "1-2 weeks"
    },
    {
      step: "5",
      title: "Optimization & Support",
      description: "Continuous monitoring and optimization for maximum efficiency",
      duration: "Ongoing"
    }
  ];

  const benefits = [
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Time Savings",
      value: "80%",
      description: "Average reduction in manual processing time"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Error Reduction",
      value: "95%",
      description: "Decrease in human errors and inconsistencies"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Productivity Boost",
      value: "3x",
      description: "Increase in overall team productivity"
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "ROI Achievement",
      value: "300%",
      description: "Average return on investment within 12 months"
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-32 pb-20">
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-6 text-center">
            <Typography variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Full Workflow Automation
            </Typography>
            
            <Typography variant="xl" color="secondary" className="mb-12 max-w-4xl mx-auto">
              Transform your entire business operation with intelligent automation that adapts, 
              learns, and evolves with your organization's needs.
            </Typography>

            <div className="grid md:grid-cols-4 gap-6 mb-16">
              {benefits.map((benefit, index) => (
                <Card key={benefit.title} variant="neural" className="p-6 text-center border-consciousness/20">
                  <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-consciousness/15 flex items-center justify-center">
                    {benefit.icon}
                  </div>
                  <Typography variant="2xl" weight="bold" color="consciousness" className="mb-2">
                    {benefit.value}
                  </Typography>
                  <Typography variant="sm" weight="bold" color="consciousness" className="mb-1">
                    {benefit.title}
                  </Typography>
                  <Typography variant="xs" color="secondary">
                    {benefit.description}
                  </Typography>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Automation Types */}
        <section className="py-20 bg-abyssal-base/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography variant="3xl" weight="bold" color="consciousness" className="mb-4">
                Comprehensive Automation Solutions
              </Typography>
              <Typography variant="lg" color="secondary" className="max-w-3xl mx-auto">
                From simple task automation to complex cognitive processes, we cover every aspect of your business
              </Typography>
            </div>

            <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {automationTypes.map((type, index) => (
                <Card key={type.title} variant="quantum" className="p-8 border-consciousness/20 hover:border-consciousness/40 transition-all duration-500 rounded-[24px]">
                  <div className="flex items-start space-x-6 mb-6">
                    <div className="w-16 h-16 rounded-full bg-consciousness/15 flex items-center justify-center border border-consciousness/30 flex-shrink-0">
                      {type.icon}
                    </div>
                    <div className="flex-1">
                      <Typography variant="xl" weight="bold" color="consciousness" className="mb-3">
                        {type.title}
                      </Typography>
                      <Typography variant="sm" color="secondary" className="mb-4">
                        {type.description}
                      </Typography>
                      <div className="inline-flex items-center px-3 py-1 bg-harmony/20 text-harmony text-xs rounded-full border border-harmony/30">
                        {type.benefits}
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <Typography variant="sm" weight="semibold" color="consciousness" className="mb-3">
                      Key Capabilities:
                    </Typography>
                    {type.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircle className="w-4 h-4 text-harmony flex-shrink-0" />
                        <Typography variant="sm" color="tertiary">
                          {feature}
                        </Typography>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Implementation Process */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography variant="3xl" weight="bold" color="consciousness" className="mb-4">
                Implementation Process
              </Typography>
              <Typography variant="lg" color="secondary" className="max-w-3xl mx-auto">
                Our proven methodology ensures successful automation deployment with minimal disruption
              </Typography>
            </div>

            <div className="max-w-4xl mx-auto">
              {implementationSteps.map((step, index) => (
                <div key={step.step} className="flex items-start space-x-6 mb-8 last:mb-0">
                  <div className="w-12 h-12 rounded-full bg-consciousness/20 flex items-center justify-center border border-consciousness/40 flex-shrink-0">
                    <Typography variant="sm" weight="bold" color="consciousness">
                      {step.step}
                    </Typography>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <Typography variant="lg" weight="bold" color="consciousness">
                        {step.title}
                      </Typography>
                      <span className="px-3 py-1 bg-harmony/20 text-harmony text-xs rounded-full border border-harmony/30">
                        {step.duration}
                      </span>
                    </div>
                    <Typography variant="sm" color="secondary">
                      {step.description}
                    </Typography>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-br from-consciousness/10 via-abyssal-base to-harmony/10">
          <div className="container mx-auto px-6 text-center">
            <Typography variant="3xl" weight="bold" gradient="consciousness" className="mb-6">
              Ready to Automate Your Workflows?
            </Typography>
            
            <Typography variant="lg" color="secondary" className="mb-12 max-w-3xl mx-auto">
              Start your automation journey today and experience the transformative power of intelligent workflows.
            </Typography>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="rounded-[20px]"
                onClick={() => window.location.href = '/request-demo'}
              >
                Schedule Automation Consultation
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="rounded-[20px]"
                onClick={() => window.location.href = '/ai-readiness-audit'}
              >
                Assess Your Automation Readiness
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="rounded-[20px]"
                onClick={() => window.location.href = '/symbioautomate'}
              >
                Back to SymbioAutomate
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default WorkflowAutomation;
