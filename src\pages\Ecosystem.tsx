
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import EcosystemNetwork from '../components/interactive/EcosystemNetwork';
import { Globe, Network, Zap, Users } from 'lucide-react';

const Ecosystem: React.FC = () => {
  const ecosystemNodes = [
    {
      id: 'symbioautomate',
      label: 'SymbioAutomate',
      size: 20,
      color: 'rgba(0, 255, 170, 0.8)',
      connections: ['symbiocore', 'symbiolabs', 'symbioxchange']
    },
    {
      id: 'symbiocore',
      label: 'SymbioCore',
      size: 25,
      color: 'rgba(0, 153, 255, 0.8)',
      connections: ['symbioautomate', 'symbiolabs', 'symbioedge']
    },
    {
      id: 'symbiolabs',
      label: 'SymbioLabs',
      size: 18,
      color: 'rgba(170, 0, 255, 0.8)',
      connections: ['symbiocore', 'symbioautomate', 'symbioventures']
    },
    {
      id: 'symbioxchange',
      label: 'SymbioXchange',
      size: 22,
      color: 'rgba(0, 255, 119, 0.8)',
      connections: ['symbioautomate', 'symbioedge', 'symbioimpact']
    },
    {
      id: 'symbioedge',
      label: 'SymbioEdge',
      size: 19,
      color: 'rgba(255, 170, 0, 0.8)',
      connections: ['symbiocore', 'symbioxchange', 'symbioalliance']
    },
    {
      id: 'symbioimpact',
      label: 'SymbioImpact',
      size: 21,
      color: 'rgba(255, 0, 170, 0.8)',
      connections: ['symbioxchange', 'symbioventures', 'symbioalliance']
    },
    {
      id: 'symbioventures',
      label: 'SymbioVentures',
      size: 17,
      color: 'rgba(0, 255, 255, 0.8)',
      connections: ['symbiolabs', 'symbioimpact', 'symbioalliance']
    },
    {
      id: 'symbioalliance',
      label: 'SymbioAlliance',
      size: 16,
      color: 'rgba(255, 255, 0, 0.8)',
      connections: ['symbioedge', 'symbioimpact', 'symbioventures']
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />

      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              SymbioWave Ecosystem
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              A complete symbiotic platform where intelligence, automation, and human creativity converge.
            </Typography>
          </div>

          {/* Interactive Ecosystem Network */}
          <div className="mb-16">
            <Card variant="quantum" className="p-8 rounded-[32px] border-consciousness/25">
              <div className="text-center mb-8">
                <Typography variant="2xl" weight="semibold" color="consciousness" className="mb-4">
                  Interactive Ecosystem Network
                </Typography>
                <Typography variant="base" color="secondary" className="mb-6">
                  Explore the interconnected relationships between all SymbioWave divisions
                </Typography>
              </div>

              <div className="h-96 rounded-[20px] overflow-hidden">
                <EcosystemNetwork
                  nodes={ecosystemNodes}
                  isActive={true}
                  variant="force"
                />
              </div>
            </Card>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            <Card variant="consciousness" className="p-10 rounded-[32px] border-consciousness/25">
              <Globe className="w-20 h-20 mb-8 text-consciousness" />
              <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                Platform Overview
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Our ecosystem connects 8 specialized divisions, each optimized for specific challenges while 
                maintaining perfect symbiosis with the whole system.
              </Typography>
            </Card>

            <Card variant="creativity" className="p-10 rounded-[32px] border-creativity/25">
              <Network className="w-20 h-20 mb-8 text-creativity" />
              <Typography variant="xl" weight="semibold" color="creativity" className="mb-6">
                Integration Layer
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Seamless connectivity between all divisions ensures that insights from one area 
                enhance capabilities across the entire ecosystem.
              </Typography>
            </Card>

            <Card variant="intuition" className="p-10 rounded-[32px] border-harmony/25">
              <Zap className="w-20 h-20 mb-8 text-harmony" />
              <Typography variant="xl" weight="semibold" color="harmony" className="mb-6">
                Real-time Intelligence
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                Live data flows create continuous optimization loops that adapt to changing 
                conditions without human intervention.
              </Typography>
            </Card>

            <Card variant="neural" className="p-10 rounded-[32px] border-consciousness/25">
              <Users className="w-20 h-20 mb-8 text-consciousness" />
              <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                Network Effects
              </Typography>
              <Typography variant="base" color="secondary" className="leading-relaxed">
                The value of each division multiplies as more participants join, creating 
                exponential benefits for all ecosystem members.
              </Typography>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8 text-center">
              The Power of Symbiosis
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed text-center max-w-4xl mx-auto">
              Unlike isolated solutions, our ecosystem creates emergent intelligence that grows stronger 
              with each interaction. Every division contributes to and benefits from the collective wisdom 
              of the entire network.
            </Typography>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Ecosystem;
