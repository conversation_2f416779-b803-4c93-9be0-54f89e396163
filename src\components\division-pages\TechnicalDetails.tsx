
import React from 'react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import Button from '../atoms/Button';
import { Code, Database, Cpu, Globe, ArrowRight } from 'lucide-react';

const TechnicalDetails: React.FC = () => {
  const technicalSpecs = [
    {
      icon: Code,
      title: "API Integration",
      description: "RESTful APIs and GraphQL endpoints for seamless integration with existing systems.",
      details: ["REST API v2.0", "GraphQL support", "Webhook notifications", "SDK libraries"]
    },
    {
      icon: Database,
      title: "Data Architecture",
      description: "Secure, scalable data processing with real-time analytics and machine learning.",
      details: ["Real-time processing", "ML pipelines", "Data encryption", "GDPR compliant"]
    },
    {
      icon: Cpu,
      title: "ACI Models",
      description: "Powered by SymbioCore's advanced Artificial Cellular Intelligence algorithms.",
      details: ["Physarum optimizer", "Quorum sensing", "Ant colony logic", "Immune detection"]
    },
    {
      icon: Globe,
      title: "Cloud & Edge",
      description: "Hybrid deployment options from cloud-native to edge computing environments.",
      details: ["Multi-cloud support", "Edge deployment", "Kubernetes ready", "Auto-scaling"]
    }
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            gradient="consciousness"
            className="mb-4"
          >
            Technical Foundation
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            Built on cutting-edge technology for enterprise-grade performance
          </Typography>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {technicalSpecs.map((spec, index) => {
            const Icon = spec.icon;
            return (
              <Card key={index} variant="neural" className="border-consciousness/30 p-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-biomorphic bg-consciousness/20 flex items-center justify-center flex-shrink-0">
                    <Icon className="w-6 h-6 text-consciousness" />
                  </div>
                  
                  <div className="flex-1">
                    <Typography 
                      as="h3" 
                      variant="lg" 
                      weight="semibold" 
                      color="consciousness"
                      className="mb-2"
                    >
                      {spec.title}
                    </Typography>
                    
                    <Typography 
                      variant="sm" 
                      color="secondary"
                      className="mb-4"
                    >
                      {spec.description}
                    </Typography>

                    <ul className="space-y-1">
                      {spec.details.map((detail, detailIndex) => (
                        <li key={detailIndex}>
                          <Typography 
                            variant="xs" 
                            color="tertiary"
                          >
                            • {detail}
                          </Typography>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        <div className="text-center">
          <Card variant="quantum" className="border-harmony/30 p-8 inline-block">
            <Typography 
              as="h3" 
              variant="xl" 
              weight="semibold" 
              color="harmony"
              className="mb-4"
            >
              Ready to Transform Your Operations?
            </Typography>
            <Typography 
              variant="sm" 
              color="secondary"
              className="mb-6 max-w-md"
            >
              Get a personalized demo of SymbioAutomate and see how ACI can revolutionize your business processes.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                Request Demo
              </Button>
              <Button 
                variant="outline-quantum" 
                size="lg"
              >
                Technical Documentation
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default TechnicalDetails;
