
import React, { useState } from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import QuantumStateTransition from '../components/interactive/QuantumStateTransition';
import { Cpu, Zap, Network, Brain } from 'lucide-react';

const QuantumProcessing: React.FC = () => {
  const [currentState, setCurrentState] = useState('superposition');

  const quantumStates = [
    {
      id: 'superposition',
      name: 'Superposition',
      color: 'rgba(0, 255, 170, 0.8)',
      pattern: 'wave' as const,
      energy: 1.2
    },
    {
      id: 'entanglement',
      name: 'Entanglement',
      color: 'rgba(0, 153, 255, 0.8)',
      pattern: 'spiral' as const,
      energy: 1.5
    },
    {
      id: 'coherence',
      name: 'Coherence',
      color: 'rgba(170, 0, 255, 0.8)',
      pattern: 'grid' as const,
      energy: 1.0
    },
    {
      id: 'decoherence',
      name: 'Decoherence',
      color: 'rgba(255, 170, 0, 0.8)',
      pattern: 'organic' as const,
      energy: 0.8
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />

      <main className="pt-24">
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Quantum Processing
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto">
              Advanced computational methods that leverage quantum mechanics principles for unprecedented processing power.
            </Typography>
          </div>

          {/* Interactive Quantum State Visualization */}
          <div className="mb-16">
            <Card variant="quantum" className="p-8 rounded-[32px] border-consciousness/25">
              <div className="text-center mb-8">
                <Typography variant="2xl" weight="semibold" color="consciousness" className="mb-4">
                  Quantum State Transitions
                </Typography>
                <Typography variant="base" color="secondary" className="mb-6">
                  Explore different quantum states and their unique processing patterns
                </Typography>
                <div className="flex justify-center gap-4 mb-6 flex-wrap">
                  {quantumStates.map((state) => (
                    <Button
                      key={state.id}
                      variant={currentState === state.id ? 'quantum' : 'outline-quantum'}
                      size="sm"
                      onClick={() => setCurrentState(state.id)}
                    >
                      {state.name}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="h-96 rounded-[20px] overflow-hidden">
                <QuantumStateTransition
                  states={quantumStates}
                  currentState={currentState}
                  isActive={true}
                  transitionDuration={1500}
                />
              </div>
            </Card>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            <Card variant="consciousness" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Cpu className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Quantum Gates
              </Typography>
              <Typography variant="sm" color="secondary">
                Fundamental quantum operations
              </Typography>
            </Card>

            <Card variant="creativity" className="p-8 text-center rounded-[24px] border-creativity/25">
              <Zap className="w-16 h-16 mx-auto mb-6 text-creativity" />
              <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                Superposition
              </Typography>
              <Typography variant="sm" color="secondary">
                Multiple state processing
              </Typography>
            </Card>

            <Card variant="intuition" className="p-8 text-center rounded-[24px] border-harmony/25">
              <Network className="w-16 h-16 mx-auto mb-6 text-harmony" />
              <Typography variant="lg" weight="semibold" color="harmony" className="mb-4">
                Entanglement
              </Typography>
              <Typography variant="sm" color="secondary">
                Interconnected quantum states
              </Typography>
            </Card>

            <Card variant="neural" className="p-8 text-center rounded-[24px] border-consciousness/25">
              <Brain className="w-16 h-16 mx-auto mb-6 text-consciousness" />
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Quantum AI
              </Typography>
              <Typography variant="sm" color="secondary">
                Intelligence acceleration
              </Typography>
            </Card>
          </div>

          <div className="glass-quantum rounded-[32px] p-12 border border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="semibold" color="consciousness" className="mb-8">
              Revolutionary Computing Power
            </Typography>
            <Typography variant="lg" color="secondary" className="leading-relaxed">
              Quantum processing represents the next frontier in computational capability. By harnessing quantum 
              mechanics principles like superposition and entanglement, our systems can process exponentially 
              more information than classical computers, enabling breakthrough advances in artificial intelligence.
            </Typography>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default QuantumProcessing;
