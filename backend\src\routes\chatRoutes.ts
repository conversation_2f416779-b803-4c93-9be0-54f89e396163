import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import chatController from '../controllers/chatController';

const router = Router();

// All chat routes require authentication
router.use(authenticateToken as any);

/**
 * @swagger
 * /chat/sessions:
 *   post:
 *     summary: Create a new chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               context:
 *                 type: object
 *                 description: Optional context for the chat session
 *               initial_message:
 *                 type: string
 *                 description: Optional initial message to start the conversation
 *     responses:
 *       201:
 *         description: Chat session created successfully
 *       401:
 *         description: Unauthorized
 */
router.post('/sessions', chatController.createChatSession as any);

/**
 * @swagger
 * /chat/sessions:
 *   get:
 *     summary: Get user's chat sessions
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, paused, ended]
 *     responses:
 *       200:
 *         description: Chat sessions retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/sessions', chatController.getChatSessions as any);

/**
 * @swagger
 * /chat/sessions/{id}:
 *   get:
 *     summary: Get chat session with messages
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Chat session retrieved successfully
 *       404:
 *         description: Chat session not found
 *       401:
 *         description: Unauthorized
 */
router.get('/sessions/:id', chatController.getChatSession as any);

/**
 * @swagger
 * /chat/sessions/{id}/messages:
 *   post:
 *     summary: Send a message in a chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 description: Message content
 *               attachments:
 *                 type: array
 *                 items:
 *                   type: object
 *                 description: Optional file attachments
 *     responses:
 *       200:
 *         description: Message sent successfully
 *       400:
 *         description: Invalid input
 *       404:
 *         description: Chat session not found
 *       401:
 *         description: Unauthorized
 */
router.post('/sessions/:id/messages', chatController.sendMessage as any);

/**
 * @swagger
 * /chat/sessions/{id}:
 *   put:
 *     summary: Update chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [active, paused, ended]
 *               context:
 *                 type: object
 *                 description: Updated context for the session
 *     responses:
 *       200:
 *         description: Chat session updated successfully
 *       404:
 *         description: Chat session not found
 *       401:
 *         description: Unauthorized
 */
router.put('/sessions/:id', chatController.updateChatSession as any);

/**
 * @swagger
 * /chat/sessions/{id}:
 *   delete:
 *     summary: Delete chat session
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Chat session deleted successfully
 *       404:
 *         description: Chat session not found
 *       401:
 *         description: Unauthorized
 */
router.delete('/sessions/:id', chatController.deleteChatSession as any);

export default router;
