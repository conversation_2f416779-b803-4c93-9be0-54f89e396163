# Button Component - Bio-Quantum Integration

## Overview

The Button component has been fully integrated with the SymbioWave bio-quantum design system, featuring consciousness-driven aesthetics, morphic design patterns, and division-specific variants.

## Key Features

### ✅ Bio-Quantum Design System Integration
- Uses CSS custom properties from `src/styles/base.css`
- Consciousness cyan-teal primary colors
- Quantum timing and easing functions
- Morphic border radius patterns

### ✅ Comprehensive Variant Support
- **Core Variants**: `primary`, `quantum`, `outline-quantum`, `secondary`, `success`, `danger`, `warning`, `outline`, `ghost`, `link`
- **Division-Specific**: `symbioautomate`, `symbiolabs`, `symbioxchange`, `symbioedge`, `symbioimpact`, `symbioventures`, `symbioalliance`

### ✅ Advanced Features
- Loading states with quantum-styled spinners
- Left and right icon support
- Morphic design option
- Full width support
- Comprehensive accessibility features

## Usage Examples

### Basic Usage
```tsx
import { Button } from '@/components/atoms/Button';

// Primary quantum button
<Button variant="quantum" size="lg">
  Transform Your Business
</Button>

// Division-specific button
<Button variant="symbioautomate">
  Automate Processes
</Button>
```

### With Icons
```tsx
import { ArrowRight, Download } from 'lucide-react';

<Button 
  variant="quantum" 
  rightIcon={<ArrowRight className="w-4 h-4" />}
>
  Continue
</Button>

<Button 
  variant="outline-quantum" 
  leftIcon={<Download className="w-4 h-4" />}
>
  Download
</Button>
```

### Loading States
```tsx
<Button 
  variant="quantum" 
  isLoading 
  loadingText="Processing"
>
  Process Data
</Button>
```

### Morphic Design
```tsx
<Button 
  variant="quantum" 
  morphic 
  size="lg"
>
  Organic Shape
</Button>
```

## Variant Guide

### Quantum Variants
- `quantum`: Primary bio-quantum aesthetic with consciousness gradients
- `outline-quantum`: Transparent with quantum borders and hover effects

### Division-Specific Variants
- `symbioautomate`: Efficiency emerald (automation)
- `symbiolabs`: Innovation amber (research)
- `symbioxchange`: Symbiosis violet (marketplace)
- `symbioedge`: Frontier electric blue (edge computing)
- `symbioimpact`: Sustainability forest teal (impact)
- `symbioventures`: Catalyst coral (ventures)
- `symbioalliance`: Global unity sapphire (alliance)

## Size Options
- `xs`: Extra small (28px min-height)
- `sm`: Small (32px min-height)
- `md`: Medium (40px min-height) - Default
- `lg`: Large (44px min-height)
- `xl`: Extra large (52px min-height)

## Props Interface

```tsx
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'quantum' | 'outline-quantum' | 'secondary' | 'success' | 'danger' | 'warning' | 'outline' | 'ghost' | 'link' | 'symbioautomate' | 'symbiolabs' | 'symbioxchange' | 'symbioedge' | 'symbioimpact' | 'symbioventures' | 'symbioalliance';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  loadingText?: string;
  fullWidth?: boolean;
  morphic?: boolean;
  'aria-label'?: string;
  'aria-describedby'?: string;
}
```

## Integration Notes

### CSS Custom Properties
The component uses CSS custom properties defined in `src/styles/base.css`:
- `--consciousness-primary`, `--consciousness-secondary`
- `--symbioautomate-primary`, `--symbioautomate-secondary`, etc.
- `--quantum-medium`, `--quantum-fast` (timing)

### Compatibility
- ✅ Compatible with existing usage patterns
- ✅ Maintains all previous functionality
- ✅ Adds new bio-quantum features
- ✅ TypeScript fully supported

### Import Path
```tsx
import { Button } from '@/components/atoms/Button';
// or
import Button from '@/components/atoms/Button';
```

## Testing

A comprehensive test component is available at `src/components/test/ButtonTest.tsx` that demonstrates all variants, sizes, and features.

## Migration

No migration required - the component maintains backward compatibility with all existing usage patterns while adding new bio-quantum features.
