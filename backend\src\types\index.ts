// Core Types for SymbioWave Backend

export interface User {
  id: string;
  email: string;
  password_hash?: string;
  full_name: string;
  company_name?: string;
  role: UserRole;
  access_level: AccessLevel;
  auth_provider: AuthProvider;
  avatar_url?: string;
  is_verified: boolean;
  is_active: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  ENTERPRISE = 'enterprise',
  DEMO = 'demo'
}

export enum AccessLevel {
  BASIC = 'basic',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
  UNLIMITED = 'unlimited'
}

export enum AuthProvider {
  LOCAL = 'local',
  GOOGLE = 'google',
  MICROSOFT = 'microsoft'
}

export interface WorkflowNode {
  id: string;
  type: NodeType;
  name: string;
  description?: string;
  position: { x: number; y: number };
  config: Record<string, any>;
  connections: {
    input: string[];
    output: string[];
  };
  status?: NodeStatus;
  division?: Division;
}

export enum NodeType {
  TRIGGER = 'trigger',
  ACTION = 'action',
  ACI_DECISION = 'aci-decision',
  ACI_OPTIMIZATION = 'aci-optimization',
  INTEGRATION = 'integration',
  LOGIC = 'logic'
}

export enum NodeStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  SUCCESS = 'success',
  ERROR = 'error',
  OPTIMIZING = 'optimizing'
}

export enum Division {
  SYMBIO_CORE = 'symbiocore',
  SYMBIO_XCHANGE = 'symbioxchange',
  SYMBIO_EDGE = 'symbioedge',
  SYMBIO_IMPACT = 'symbioimpact'
}

export interface Workflow {
  id: string;
  user_id: string;
  name: string;
  description: string;
  version: number;
  nodes: WorkflowNode[];
  connections: WorkflowConnection[];
  status: WorkflowStatus;
  is_active: boolean;
  tags: string[];
  category: string;
  created_at: Date;
  updated_at: Date;
  last_executed?: Date;
}

export interface WorkflowConnection {
  id: string;
  source_node_id: string;
  target_node_id: string;
  source_port: string;
  target_port: string;
  animated?: boolean;
  color?: string;
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ERROR = 'error',
  ARCHIVED = 'archived'
}

export interface WorkflowExecution {
  id: string;
  workflow_id: string;
  status: ExecutionStatus;
  started_at: Date;
  completed_at?: Date;
  duration_ms?: number;
  input_data?: Record<string, any>;
  output_data?: Record<string, any>;
  error_message?: string;
  node_executions: NodeExecution[];
}

export interface NodeExecution {
  id: string;
  execution_id: string;
  node_id: string;
  status: ExecutionStatus;
  started_at: Date;
  completed_at?: Date;
  duration_ms?: number;
  input_data?: Record<string, any>;
  output_data?: Record<string, any>;
  error_message?: string;
}

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  SUCCESS = 'success',
  ERROR = 'error',
  CANCELLED = 'cancelled'
}

export interface WorkflowMetrics {
  workflow_id: string;
  execution_count: number;
  avg_execution_time: number;
  success_rate: number;
  resources_saved: number;
  co2_reduction: number;
  cost_optimization: number;
  aci_optimizations: number;
  last_calculated: Date;
}

export interface AIReadinessAssessment {
  id: string;
  user_id: string;
  responses: Record<string, any>;
  score: number;
  readiness_level: ReadinessLevel;
  recommendations: string[];
  estimated_timeline: string;
  created_at: Date;
}

export enum ReadinessLevel {
  BASIC = 'basic',
  MODERATE = 'moderate',
  GOOD = 'good',
  EXCELLENT = 'excellent'
}

export interface DemoRequest {
  id: string;
  full_name: string;
  email: string;
  company: string;
  role: string;
  phone?: string;
  employees?: string;
  industry: string;
  challenges?: string;
  timeline?: string;
  status: DemoStatus;
  scheduled_at?: Date;
  assigned_to?: string;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export enum DemoStatus {
  PENDING = 'pending',
  CONTACTED = 'contacted',
  SCHEDULED = 'scheduled',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface ChatSession {
  id: string;
  user_id: string;
  title: string;
  context: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface ChatMessage {
  id: string;
  session_id: string;
  type: MessageType;
  content: string;
  metadata?: Record<string, any>;
  created_at: Date;
}

export enum MessageType {
  USER = 'user',
  ACI = 'aci',
  SYSTEM = 'system'
}

export interface Integration {
  id: string;
  user_id: string;
  name: string;
  type: IntegrationType;
  division?: Division;
  config: Record<string, any>;
  credentials: Record<string, any>;
  status: IntegrationStatus;
  last_sync?: Date;
  created_at: Date;
  updated_at: Date;
}

export enum IntegrationType {
  SYMBIOWAVE = 'symbiowave',
  EXTERNAL = 'external',
  N8N = 'n8n',
  ZAPIER = 'zapier',
  WEBHOOK = 'webhook'
}

export enum IntegrationStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error',
  PENDING = 'pending'
}

export interface ApiKey {
  id: string;
  user_id: string;
  name: string;
  key_hash: string;
  permissions: string[];
  last_used?: Date;
  expires_at?: Date;
  is_active: boolean;
  created_at: Date;
}

export interface AuditLog {
  id: string;
  user_id?: string;
  action: string;
  resource_type: string;
  resource_id?: string;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

// Request/Response Types
export interface AuthRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  company_name?: string;
}

export interface AuthResponse {
  user: Omit<User, 'password_hash'>;
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    total_pages?: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  status?: string;
  category?: string;
  date_from?: string;
  date_to?: string;
}

// WebSocket Types
export interface SocketEvent {
  type: string;
  payload: any;
  timestamp: Date;
}

export interface WorkflowExecutionEvent extends SocketEvent {
  type: 'workflow_execution';
  payload: {
    workflow_id: string;
    execution_id: string;
    status: ExecutionStatus;
    progress?: number;
  };
}

export interface NotificationEvent extends SocketEvent {
  type: 'notification';
  payload: {
    title: string;
    message: string;
    level: 'info' | 'warning' | 'error' | 'success';
    action_url?: string;
  };
}

// Error Types
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429);
  }
}

export class InternalServerError extends AppError {
  constructor(message: string = 'Internal server error') {
    super(message, 500);
  }
}
