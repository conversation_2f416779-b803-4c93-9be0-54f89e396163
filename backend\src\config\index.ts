import dotenv from 'dotenv';

dotenv.config();

export const config = {
  // Server Configuration
  server: {
    port: parseInt(process.env.PORT || '3001'),
    host: process.env.HOST || 'localhost',
    env: process.env.NODE_ENV || 'development',
  },

  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    name: process.env.DB_NAME || 'symbiowave_db',
    user: process.env.DB_USER || 'symbiowave_user',
    password: process.env.DB_PASSWORD || 'password',
    url: process.env.DATABASE_URL,
  },

  // Redis Configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
  },

  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your_super_secure_jwt_secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshSecret: process.env.JWT_REFRESH_SECRET || 'your_refresh_secret',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  },

  // Session Configuration
  session: {
    secret: process.env.SESSION_SECRET || 'your_session_secret',
  },

  // Google OAuth Configuration
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackUrl: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3001/auth/google/callback',
  },

  // Email Configuration
  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
    from: {
      email: process.env.FROM_EMAIL || '<EMAIL>',
      name: process.env.FROM_NAME || 'SymbioWave',
    },
  },

  // File Upload Configuration
  upload: {
    path: process.env.UPLOAD_PATH || 'uploads',
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
    allowedTypes: process.env.ALLOWED_FILE_TYPES?.split(',') || [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'text/csv',
    ],
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  },

  // External APIs
  externalApis: {
    n8n: {
      webhookUrl: process.env.N8N_WEBHOOK_URL,
      apiUrl: process.env.N8N_API_URL,
      apiKey: process.env.N8N_API_KEY,
    },
    symbioCore: process.env.SYMBIO_CORE_API,
    symbioXchange: process.env.SYMBIO_XCHANGE_API,
    symbioEdge: process.env.SYMBIO_EDGE_API,
    symbioImpact: process.env.SYMBIO_IMPACT_API,
  },

  // Security
  security: {
    corsOrigin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:5173', 'http://localhost:3000'],
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),
    webhookSecret: process.env.WEBHOOK_SECRET,
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
  },

  // Feature Flags
  features: {
    swagger: process.env.ENABLE_SWAGGER === 'true',
    metrics: process.env.ENABLE_METRICS === 'true',
    websockets: process.env.ENABLE_WEBSOCKETS === 'true',
    cronJobs: process.env.ENABLE_CRON_JOBS === 'true',
  },

  // AI/ML Configuration
  ai: {
    openaiApiKey: process.env.OPENAI_API_KEY,
    anthropicApiKey: process.env.ANTHROPIC_API_KEY,
  },

  // Monitoring
  monitoring: {
    sentryDsn: process.env.SENTRY_DSN,
    analyticsApiKey: process.env.ANALYTICS_API_KEY,
  },
};

export default config;
