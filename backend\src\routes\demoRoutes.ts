import { Router } from 'express';
import { authenticateToken, requireRole } from '../middleware/auth';
import demoController from '../controllers/demoController';

const router = Router();

/**
 * @swagger
 * /demo/request:
 *   post:
 *     summary: Submit a demo request
 *     tags: [Demo]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - company_name
 *               - contact_name
 *               - email
 *             properties:
 *               company_name:
 *                 type: string
 *               contact_name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               phone:
 *                 type: string
 *               company_size:
 *                 type: string
 *               industry:
 *                 type: string
 *               use_case:
 *                 type: string
 *               message:
 *                 type: string
 *               preferred_date:
 *                 type: string
 *                 format: date
 *               preferred_time:
 *                 type: string
 *     responses:
 *       201:
 *         description: Demo request submitted successfully
 *       400:
 *         description: Invalid input
 */
router.post('/request', demoController.requestDemo as any);

/**
 * @swagger
 * /demo/requests:
 *   get:
 *     summary: Get demo requests (Admin only)
 *     tags: [Demo]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, contacted, scheduled, completed, cancelled]
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: Demo requests retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/requests', authenticateToken as any, requireRole(['admin']) as any, demoController.getDemoRequests as any);

/**
 * @swagger
 * /demo/requests/{id}:
 *   get:
 *     summary: Get demo request by ID (Admin only)
 *     tags: [Demo]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Demo request retrieved successfully
 *       404:
 *         description: Demo request not found
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/requests/:id', authenticateToken as any, requireRole(['admin']) as any, demoController.getDemoRequest as any);

/**
 * @swagger
 * /demo/requests/{id}/status:
 *   put:
 *     summary: Update demo request status (Admin only)
 *     tags: [Demo]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [pending, contacted, scheduled, completed, cancelled]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Demo request status updated successfully
 *       404:
 *         description: Demo request not found
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.put('/requests/:id/status', authenticateToken as any, requireRole(['admin']) as any, demoController.updateDemoRequestStatus as any);

/**
 * @swagger
 * /demo/requests/{id}:
 *   delete:
 *     summary: Delete demo request (Admin only)
 *     tags: [Demo]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Demo request deleted successfully
 *       404:
 *         description: Demo request not found
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.delete('/requests/:id', authenticateToken as any, requireRole(['admin']) as any, demoController.deleteDemoRequest as any);

/**
 * @swagger
 * /demo/stats:
 *   get:
 *     summary: Get demo request statistics (Admin only)
 *     tags: [Demo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Demo statistics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin access required
 */
router.get('/stats', authenticateToken as any, requireRole(['admin']) as any, demoController.getDemoStats as any);

export default router;
