import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { 
  ValidationError, 
  NotFoundError,
  AuthorizationError,
  Workflow,
  WorkflowStatus,
  PaginationParams,
  FilterParams
} from '../types';
import db from '../utils/database';
import { AuthenticatedRequest } from '../middleware/auth';

export class WorkflowController {
  async createWorkflow(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { name, description, nodes, connections, category, tags } = req.body;
      const userId = req.user!.id;

      if (!name || !description) {
        throw new ValidationError('Name and description are required');
      }

      const workflowData = {
        id: uuidv4(),
        user_id: userId,
        name,
        description,
        version: 1,
        nodes: JSON.stringify(nodes || []),
        connections: JSON.stringify(connections || []),
        status: WorkflowStatus.DRAFT,
        is_active: false,
        tags: JSON.stringify(tags || []),
        category: category || 'general',
      };

      const [workflow] = await db('workflows')
        .insert(workflowData)
        .returning('*');

      // Parse JSON fields for response
      const responseWorkflow = {
        ...workflow,
        nodes: JSON.parse(workflow.nodes),
        connections: JSON.parse(workflow.connections),
        tags: JSON.parse(workflow.tags),
      };

      res.status(201).json({
        success: true,
        data: responseWorkflow,
        message: 'Workflow created successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async getWorkflows(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const { 
        page = 1, 
        limit = 20, 
        sort_by = 'created_at', 
        sort_order = 'desc',
        search,
        status,
        category 
      }: PaginationParams & FilterParams = req.query;

      let query = db('workflows')
        .where({ user_id: userId })
        .select('*');

      // Apply filters
      if (search) {
        query = query.where(function() {
          this.where('name', 'ilike', `%${search}%`)
              .orWhere('description', 'ilike', `%${search}%`);
        });
      }

      if (status) {
        query = query.where({ status });
      }

      if (category) {
        query = query.where({ category });
      }

      // Get total count
      const totalQuery = query.clone();
      const [{ count }] = await totalQuery.count('* as count');
      const total = parseInt(count as string);

      // Apply pagination and sorting
      const offset = (Number(page) - 1) * Number(limit);
      const workflows = await query
        .orderBy(sort_by, sort_order)
        .limit(Number(limit))
        .offset(offset);

      // Parse JSON fields
      const responseWorkflows = workflows.map(workflow => ({
        ...workflow,
        nodes: JSON.parse(workflow.nodes),
        connections: JSON.parse(workflow.connections),
        tags: JSON.parse(workflow.tags),
      }));

      res.json({
        success: true,
        data: responseWorkflows,
        meta: {
          page: Number(page),
          limit: Number(limit),
          total,
          total_pages: Math.ceil(total / Number(limit)),
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async getWorkflow(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      // Parse JSON fields
      const responseWorkflow = {
        ...workflow,
        nodes: JSON.parse(workflow.nodes),
        connections: JSON.parse(workflow.connections),
        tags: JSON.parse(workflow.tags),
      };

      res.json({
        success: true,
        data: responseWorkflow,
      });
    } catch (error) {
      next(error);
    }
  }

  async updateWorkflow(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const { name, description, nodes, connections, category, tags, status } = req.body;

      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      const updateData: any = {
        updated_at: new Date(),
      };

      if (name) updateData.name = name;
      if (description) updateData.description = description;
      if (nodes) updateData.nodes = JSON.stringify(nodes);
      if (connections) updateData.connections = JSON.stringify(connections);
      if (category) updateData.category = category;
      if (tags) updateData.tags = JSON.stringify(tags);
      if (status) updateData.status = status;

      // Increment version if nodes or connections changed
      if (nodes || connections) {
        updateData.version = workflow.version + 1;
      }

      const [updatedWorkflow] = await db('workflows')
        .where({ id })
        .update(updateData)
        .returning('*');

      // Parse JSON fields
      const responseWorkflow = {
        ...updatedWorkflow,
        nodes: JSON.parse(updatedWorkflow.nodes),
        connections: JSON.parse(updatedWorkflow.connections),
        tags: JSON.parse(updatedWorkflow.tags),
      };

      res.json({
        success: true,
        data: responseWorkflow,
        message: 'Workflow updated successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async deleteWorkflow(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      await db('workflows')
        .where({ id })
        .del();

      res.json({
        success: true,
        message: 'Workflow deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async activateWorkflow(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      const [updatedWorkflow] = await db('workflows')
        .where({ id })
        .update({
          status: WorkflowStatus.ACTIVE,
          is_active: true,
          updated_at: new Date(),
        })
        .returning('*');

      // Parse JSON fields
      const responseWorkflow = {
        ...updatedWorkflow,
        nodes: JSON.parse(updatedWorkflow.nodes),
        connections: JSON.parse(updatedWorkflow.connections),
        tags: JSON.parse(updatedWorkflow.tags),
      };

      res.json({
        success: true,
        data: responseWorkflow,
        message: 'Workflow activated successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async deactivateWorkflow(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      const [updatedWorkflow] = await db('workflows')
        .where({ id })
        .update({
          status: WorkflowStatus.PAUSED,
          is_active: false,
          updated_at: new Date(),
        })
        .returning('*');

      // Parse JSON fields
      const responseWorkflow = {
        ...updatedWorkflow,
        nodes: JSON.parse(updatedWorkflow.nodes),
        connections: JSON.parse(updatedWorkflow.connections),
        tags: JSON.parse(updatedWorkflow.tags),
      };

      res.json({
        success: true,
        data: responseWorkflow,
        message: 'Workflow deactivated successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async getWorkflowMetrics(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      // Verify workflow ownership
      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      // Get metrics
      const metrics = await db('workflow_metrics')
        .where({ workflow_id: id })
        .first();

      if (!metrics) {
        // Return default metrics if none exist
        const defaultMetrics = {
          workflow_id: id,
          execution_count: 0,
          avg_execution_time: 0,
          success_rate: 0,
          resources_saved: 0,
          co2_reduction: 0,
          cost_optimization: 0,
          aci_optimizations: 0,
          last_calculated: new Date(),
        };

        res.json({
          success: true,
          data: defaultMetrics,
        });
        return;
      }

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      next(error);
    }
  }

  async executeWorkflow(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { input_data } = req.body;
      const userId = req.user!.id;

      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      if (!workflow.is_active) {
        throw new ValidationError('Workflow must be active to execute');
      }

      // Create execution record
      const executionData = {
        id: uuidv4(),
        workflow_id: id,
        status: 'pending',
        started_at: new Date(),
        input_data: JSON.stringify(input_data || {}),
      };

      const [execution] = await db('workflow_executions')
        .insert(executionData)
        .returning('*');

      // Here you would trigger the actual workflow execution
      // For now, we'll simulate it
      setTimeout(async () => {
        try {
          await db('workflow_executions')
            .where({ id: execution.id })
            .update({
              status: 'success',
              completed_at: new Date(),
              duration_ms: Math.floor(Math.random() * 5000) + 1000,
              output_data: JSON.stringify({ result: 'success' }),
            });

          // Update workflow last_executed
          await db('workflows')
            .where({ id })
            .update({ last_executed: new Date() });
        } catch (error) {
          console.error('Error updating execution:', error);
        }
      }, 2000);

      res.json({
        success: true,
        data: {
          ...execution,
          input_data: JSON.parse(execution.input_data),
        },
        message: 'Workflow execution started',
      });
    } catch (error) {
      next(error);
    }
  }
}

export default new WorkflowController();
