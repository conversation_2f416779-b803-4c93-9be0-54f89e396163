
import React from 'react';
import Header from '../components/organisms/Header';
import HeroSection from '../components/features/HeroSection';
import SymbioticImperative from '../components/features/SymbioticImperative';
import ACITechnology from '../components/features/ACITechnology';
import EcosystemOverview from '../components/features/EcosystemOverview';
import FlywheelVisualization from '../components/features/FlywheelVisualization';
import GlobalImpact from '../components/features/GlobalImpact';
import CallToAction from '../components/features/CallToAction';
import Footer from '../components/organisms/Footer';

const Index: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main>
        {/* The Genesis Portal */}
        <HeroSection />
        
        {/* The Symbiotic Imperative */}
        <SymbioticImperative />
        
        {/* ACI Technology Introduction */}
        <ACITechnology />
        
        {/* The 8 Division Ecosystem */}
        <EcosystemOverview />
        
        {/* The Synergistic Flywheel */}
        <FlywheelVisualization />
        
        {/* Global Impact & Vision */}
        <GlobalImpact />
        
        {/* Final CTA */}
        <CallToAction />
      </main>

      <Footer />
    </div>
  );
};

export default Index;
