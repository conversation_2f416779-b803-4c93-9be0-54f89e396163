import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import db from '../utils/database';
import { ValidationError, NotFoundError } from '../types';
import { AuthenticatedRequest } from '../middleware/auth';

export class DemoController {
  async requestDemo(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const {
        company_name,
        contact_name,
        email,
        phone,
        company_size,
        industry,
        use_case,
        message,
        preferred_date,
        preferred_time,
      } = req.body;

      // Validate required fields
      if (!company_name || !contact_name || !email) {
        throw new ValidationError('Company name, contact name, and email are required');
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new ValidationError('Invalid email format');
      }

      // Create demo request
      const [demoRequest] = await db('demo_requests')
        .insert({
          id: uuidv4(),
          company_name,
          contact_name,
          email,
          phone: phone || null,
          company_size: company_size || null,
          industry: industry || null,
          use_case: use_case || null,
          message: message || null,
          preferred_date: preferred_date || null,
          preferred_time: preferred_time || null,
          status: 'pending',
          created_at: new Date(),
        })
        .returning('*');

      res.status(201).json({
        success: true,
        data: {
          request_id: demoRequest.id,
          status: demoRequest.status,
          message: 'Demo request submitted successfully. Our team will contact you within 24 hours.',
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async getDemoRequests(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { page = 1, limit = 10, status, search } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      let query = db('demo_requests')
        .select('*')
        .orderBy('created_at', 'desc');

      // Filter by status
      if (status && status !== 'all') {
        query = query.where('status', status as string);
      }

      // Search functionality
      if (search) {
        const searchTerm = `%${search}%`;
        query = query.where(function() {
          this.where('company_name', 'ilike', searchTerm)
            .orWhere('contact_name', 'ilike', searchTerm)
            .orWhere('email', 'ilike', searchTerm)
            .orWhere('industry', 'ilike', searchTerm);
        });
      }

      // Get total count for pagination
      const totalQuery = query.clone();
      const [{ count: total }] = await totalQuery.count('* as count');

      // Apply pagination
      const demoRequests = await query
        .limit(Number(limit))
        .offset(offset);

      res.json({
        success: true,
        data: {
          requests: demoRequests,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: Number(total),
            pages: Math.ceil(Number(total) / Number(limit)),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async getDemoRequest(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const demoRequest = await db('demo_requests')
        .where({ id })
        .first();

      if (!demoRequest) {
        throw new NotFoundError('Demo request not found');
      }

      res.json({
        success: true,
        data: demoRequest,
      });
    } catch (error) {
      next(error);
    }
  }

  async updateDemoRequestStatus(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { status, notes } = req.body;

      const validStatuses = ['pending', 'contacted', 'scheduled', 'completed', 'cancelled'];
      if (!validStatuses.includes(status)) {
        throw new ValidationError('Invalid status');
      }

      const [updatedRequest] = await db('demo_requests')
        .where({ id })
        .update({
          status,
          notes: notes || null,
          updated_at: new Date(),
        })
        .returning('*');

      if (!updatedRequest) {
        throw new NotFoundError('Demo request not found');
      }

      res.json({
        success: true,
        data: updatedRequest,
        message: 'Demo request status updated successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async deleteDemoRequest(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const deletedCount = await db('demo_requests')
        .where({ id })
        .del();

      if (deletedCount === 0) {
        throw new NotFoundError('Demo request not found');
      }

      res.json({
        success: true,
        message: 'Demo request deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async getDemoStats(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const [stats] = await db('demo_requests')
        .select(
          db.raw('COUNT(*) as total_requests'),
          db.raw('COUNT(CASE WHEN status = ? THEN 1 END) as pending', ['pending']),
          db.raw('COUNT(CASE WHEN status = ? THEN 1 END) as contacted', ['contacted']),
          db.raw('COUNT(CASE WHEN status = ? THEN 1 END) as scheduled', ['scheduled']),
          db.raw('COUNT(CASE WHEN status = ? THEN 1 END) as completed', ['completed']),
          db.raw('COUNT(CASE WHEN created_at >= NOW() - INTERVAL \'7 days\' THEN 1 END) as this_week'),
          db.raw('COUNT(CASE WHEN created_at >= NOW() - INTERVAL \'30 days\' THEN 1 END) as this_month')
        );

      // Get industry breakdown
      const industryBreakdown = await db('demo_requests')
        .select('industry')
        .count('* as count')
        .whereNotNull('industry')
        .groupBy('industry')
        .orderBy('count', 'desc')
        .limit(10);

      // Get company size breakdown
      const companySizeBreakdown = await db('demo_requests')
        .select('company_size')
        .count('* as count')
        .whereNotNull('company_size')
        .groupBy('company_size')
        .orderBy('count', 'desc');

      res.json({
        success: true,
        data: {
          overview: {
            total_requests: Number(stats.total_requests),
            pending: Number(stats.pending),
            contacted: Number(stats.contacted),
            scheduled: Number(stats.scheduled),
            completed: Number(stats.completed),
            this_week: Number(stats.this_week),
            this_month: Number(stats.this_month),
          },
          industry_breakdown: industryBreakdown.map(item => ({
            industry: item.industry,
            count: Number(item.count),
          })),
          company_size_breakdown: companySizeBreakdown.map(item => ({
            size: item.company_size,
            count: Number(item.count),
          })),
        },
      });
    } catch (error) {
      next(error);
    }
  }
}

export default new DemoController();
