
import React from 'react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import { Factory, Truck, Zap, Leaf } from 'lucide-react';

const UseCases: React.FC = () => {
  const useCases = [
    {
      icon: Factory,
      title: "Manufacturing",
      subtitle: "Smart Production Lines",
      benefits: [
        "Line optimization and quality control",
        "Predictive maintenance",
        "Resource allocation",
        "Waste reduction"
      ],
      color: "consciousness"
    },
    {
      icon: Truck,
      title: "Logistics",
      subtitle: "Dynamic Operations",
      benefits: [
        "Dynamic routing optimization",
        "Fleet management",
        "Warehouse automation",
        "Load optimization"
      ],
      color: "harmony"
    },
    {
      icon: Zap,
      title: "Energy",
      subtitle: "Grid Intelligence",
      benefits: [
        "Grid optimization",
        "Demand response",
        "Renewable integration",
        "Load balancing"
      ],
      color: "creativity"
    },
    {
      icon: Leaf,
      title: "Agriculture",
      subtitle: "Precision Farming",
      benefits: [
        "Crop monitoring",
        "Resource optimization",
        "Yield prediction",
        "Sustainable practices"
      ],
      color: "harmony"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-surface-void to-surface-void/50">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            gradient="creativity"
            className="mb-4"
          >
            Industry Applications
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            SymbioAutomate transforms operations across diverse industries
          </Typography>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {useCases.map((useCase, index) => {
            const Icon = useCase.icon;
            return (
              <Card key={index} variant="neural" className="border-harmony/30 p-6 text-center">
                <div className={`w-16 h-16 rounded-cellular bg-${useCase.color}/20 flex items-center justify-center mx-auto mb-6`}>
                  <Icon className={`w-8 h-8 text-${useCase.color}`} />
                </div>
                
                <Typography 
                  as="h3" 
                  variant="lg" 
                  weight="semibold" 
                  color={useCase.color as any}
                  className="mb-2"
                >
                  {useCase.title}
                </Typography>
                
                <Typography 
                  variant="sm" 
                  color="harmony"
                  className="mb-4"
                >
                  {useCase.subtitle}
                </Typography>

                <ul className="space-y-2">
                  {useCase.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex}>
                      <Typography 
                        variant="xs" 
                        color="tertiary"
                        className="text-left"
                      >
                        • {benefit}
                      </Typography>
                    </li>
                  ))}
                </ul>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default UseCases;
