import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Heart, 
  Microscope, 
  ArrowRightLeft, 
  Cog, 
  Cpu, 
  Shield, 
  TrendingUp, 
  Globe 
} from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import Button from '../atoms/Button';
import AnticipatoryCursor from '../atoms/AnticipatoryCursor';
import ScrollDrivenText from '../atoms/ScrollDrivenText';

const EcosystemOverview: React.FC = () => {
  const navigate = useNavigate();

  const divisions = [
    {
      name: 'SymbioCore',
      tagline: 'The Heart of Intelligence',
      icon: Heart,
      color: 'consciousness',
      description: 'Cloud-based PaaS hosting the full suite of ACI models with robust API infrastructure. The central nervous system powering all divisions.',
      purpose: 'Democratizes access to dynamic, evolving AI through API subscriptions',
      link: '/symbiocore'
    },
    {
      name: 'SymbioLabs',
      tagline: 'The Engine of Innovation',
      icon: Microscope,
      color: 'creativity',
      description: 'Dedicated R&D division advancing ACI frontiers and developing next-generation nature-inspired AI models.',
      purpose: 'Fundamental research ensuring continuous technological leadership',
      link: '/symbiolabs'
    },
    {
      name: 'SymbioXchange',
      tagline: 'The Symbiosis Marketplace',
      icon: ArrowRightLeft,
      color: 'intuition',
      description: 'AI-driven B2B marketplace facilitating trading of resources, byproducts, and underutilized assets.',
      purpose: 'Transforms waste into tradable assets, creating true circular economy',
      link: '/symbioxchange'
    },
    {
      name: 'SymbioAutomate',
      tagline: 'The Efficiency Engine',
      icon: Cog,
      color: 'harmony',
      description: 'Enterprise-grade intelligent process automation creating dynamic, self-optimizing workflows.',
      purpose: 'Replaces rigid automation with fluid, intelligent adaptation',
      link: '/symbioautomate'
    },
    {
      name: 'SymbioEdge',
      tagline: 'Intelligence at the Frontier',
      icon: Cpu,
      color: 'transcendence',
      description: 'Lightweight ACI models on edge devices for real-time decision-making without cloud dependency.',
      purpose: 'Brings decentralized intelligence to the physical world',
      link: '/symbioedge'
    },
    {
      name: 'SymbioImpact',
      tagline: 'The Conscience of the Ecosystem',
      icon: Shield,
      color: 'consciousness',
      description: 'ESG technology and advisory using ACI to measure, verify, and report sustainability performance.',
      purpose: 'Transforms ESG from marketing to auditable business function',
      link: '/symbioimpact'
    },
    {
      name: 'SymbioVentures',
      tagline: 'The Catalyst for Growth',
      icon: TrendingUp,
      color: 'creativity',
      description: 'Corporate venture capital arm investing in early-stage startups in symbiotic technologies.',
      purpose: 'Self-fueling innovation engine strategically cultivating ecosystem',
      link: '/symbioventures'
    },
    {
      name: 'SymbioAlliance',
      tagline: 'The Global Scaling Engine',
      icon: Globe,
      color: 'intuition',
      description: 'Strategic partnerships for scaling symbiotic economy adoption through policy and industry standards.',
      purpose: 'Shapes entire markets, elevating symbiosis to global economic policy',
      link: '/symbioalliance'
    }
  ];

  const handleDivisionClick = (link: string) => {
    navigate(link);
  };

  return (
    <section id="ecosystem" className="py-32 relative">
      {/* Enhanced Organic Background Elements */}
      <div className="absolute inset-0 opacity-25">
        <div className="absolute top-1/6 left-1/6 w-48 h-48 bg-consciousness/8 rounded-cellular blur-3xl animate-consciousness-wave"></div>
        <div className="absolute bottom-1/6 right-1/6 w-36 h-36 bg-harmony/10 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
        <div className="absolute top-2/3 left-1/3 w-24 h-24 bg-creativity/8 rounded-organic blur-xl animate-quantum-flicker"></div>
        
        {/* Quantum Connection Lines */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/20 to-transparent animate-pulse"></div>
          <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-harmony/15 to-transparent animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Enhanced Section Header */}
        <div className="text-center mb-20">
          <ScrollDrivenText 
            variant="4xl" 
            className="mb-6 font-bold bg-gradient-to-r from-consciousness-500 via-consciousness-secondary to-consciousness-500 bg-clip-text text-transparent"
            effect="quantumReveal"
            delay={200}
          >
            The SymbioWave Ecosystem
          </ScrollDrivenText>
          
          <ScrollDrivenText 
            variant="xl" 
            color="secondary"
            className="mb-6"
            effect="lightTrace"
            delay={600}
          >
            An Engine of Synergistic Value Creation
          </ScrollDrivenText>

          <ScrollDrivenText 
            variant="lg" 
            color="tertiary"
            className="max-w-4xl mx-auto leading-relaxed"
            effect="materialize"
            delay={1000}
            stagger={30}
          >
            SymbioWave is not a single product but a complete ecosystem of eight divisions. Each is a formidable business in its own right, yet their true power is unleashed when they work in synergy, creating a self-reinforcing flywheel of value.
          </ScrollDrivenText>
        </div>

        {/* Enhanced Division Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {divisions.map((division, index) => {
            const IconComponent = division.icon;
            return (
              <AnticipatoryCursor 
                key={division.name}
                intensity="medium"
                glowColor={division.color as any}
              >
                <Card 
                  variant="neural"
                  className={`group cursor-pointer transition-all duration-[600ms] hover:border-${division.color}/60 hover:transform hover:scale-105 hover:-translate-y-3 relative overflow-hidden`}
                  onClick={() => handleDivisionClick(division.link)}
                >
                  {/* Enhanced Background Glow Effect */}
                  <div className={`absolute inset-0 bg-gradient-to-br from-${division.color}/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-[600ms]`}></div>
                  
                  {/* Quantum Flow Lines */}
                  <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-[400ms]"></div>
                  <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-consciousness/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-[400ms] delay-100"></div>
                  
                  <div className="relative z-10">
                    {/* Enhanced Icon Animation */}
                    <div className={`w-20 h-20 mx-auto mb-6 rounded-cellular bg-${division.color}/10 flex items-center justify-center group-hover:bg-${division.color}/20 transition-all duration-[400ms] group-hover:animate-cellular-flow group-hover:rotate-12`}>
                      <IconComponent className={`w-10 h-10 text-${division.color} group-hover:scale-110 transition-all duration-[400ms] group-hover:drop-shadow-lg`} />
                    </div>

                    {/* Division Name */}
                    <Typography 
                      variant="lg" 
                      weight="bold" 
                      className={`mb-2 text-${division.color} text-center group-hover:animate-pulse`}
                    >
                      {division.name}
                    </Typography>

                    {/* Tagline */}
                    <Typography 
                      variant="sm" 
                      color="secondary" 
                      className="mb-4 text-center italic group-hover:text-consciousness transition-colors duration-[400ms]"
                    >
                      {division.tagline}
                    </Typography>

                    {/* Description */}
                    <Typography 
                      variant="xs" 
                      color="tertiary"
                      className="mb-4 leading-relaxed text-center"
                    >
                      {division.description}
                    </Typography>

                    {/* Enhanced Purpose Highlight */}
                    <div className={`p-3 glass-quantum rounded-lg border border-${division.color}/20 group-hover:border-${division.color}/40 transition-all duration-[400ms] group-hover:backdrop-blur-xl`}>
                      <Typography 
                        variant="micro" 
                        weight="medium"
                        className={`text-${division.color} text-center`}
                      >
                        {division.purpose}
                      </Typography>
                    </div>
                  </div>

                  {/* Enhanced Hover Arrow Indicator */}
                  <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-[400ms] group-hover:rotate-45">
                    <div className={`w-6 h-6 rounded-full bg-${division.color}/20 flex items-center justify-center backdrop-blur-sm`}>
                      <div className={`w-3 h-3 border-r-2 border-t-2 border-${division.color} transform rotate-45`}></div>
                    </div>
                  </div>

                  {/* Quantum Particle Trail */}
                  <div className="absolute -inset-2 opacity-0 group-hover:opacity-100 transition-opacity duration-[600ms] pointer-events-none">
                    <div className={`absolute top-1/2 left-1/2 w-1 h-1 bg-${division.color} rounded-full animate-ping`}></div>
                    <div className={`absolute top-1/4 right-1/4 w-1 h-1 bg-${division.color}/60 rounded-full animate-ping`} style={{ animationDelay: '0.5s' }}></div>
                    <div className={`absolute bottom-1/4 left-1/4 w-1 h-1 bg-${division.color}/40 rounded-full animate-ping`} style={{ animationDelay: '1s' }}></div>
                  </div>
                </Card>
              </AnticipatoryCursor>
            );
          })}
        </div>

        {/* Enhanced Ecosystem Integration Message */}
        <AnticipatoryCursor intensity="medium" glowColor="consciousness">
          <Card variant="quantum" className="text-center border-consciousness/30 mb-12 hover:border-consciousness/60 transition-all duration-[600ms] hover:backdrop-blur-xl">
            <ScrollDrivenText 
              variant="xl" 
              className="mb-6 font-bold bg-gradient-to-r from-consciousness-500 via-creativity-500 to-harmony-500 bg-clip-text text-transparent"
              effect="quantumReveal"
              delay={300}
            >
              The Power of Integration
            </ScrollDrivenText>
            
            <ScrollDrivenText 
              variant="lg" 
              color="secondary"
              className="mb-8 max-w-3xl mx-auto leading-relaxed"
              effect="materialize"
              delay={600}
              stagger={40}
            >
              Each division amplifies the others. SymbioCore powers SymbioXchange's matching algorithms. SymbioAutomate orchestrates transactions. SymbioImpact validates sustainability gains. Together, they create an unbeatable competitive moat.
            </ScrollDrivenText>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <AnticipatoryCursor intensity="intense" glowColor="consciousness">
                <Button 
                  variant="quantum" 
                  size="lg"
                  onClick={() => navigate('/flywheel')}
                  className="hover:scale-105 transition-transform duration-[400ms]"
                >
                  See the Synergistic Flywheel
                </Button>
              </AnticipatoryCursor>
              <AnticipatoryCursor intensity="medium" glowColor="harmony">
                <Button 
                  variant="outline-quantum" 
                  size="lg"
                  onClick={() => navigate('/symbioautomate')}
                  className="hover:scale-105 transition-transform duration-[400ms]"
                >
                  Explore SymbioAutomate
                </Button>
              </AnticipatoryCursor>
            </div>
          </Card>
        </AnticipatoryCursor>
      </div>
    </section>
  );
};

export default EcosystemOverview;
