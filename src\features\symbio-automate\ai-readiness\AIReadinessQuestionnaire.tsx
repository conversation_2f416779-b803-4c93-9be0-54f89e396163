
import React, { useState } from 'react';
import Typography from '../../../components/atoms/Typography';
import Card from '../../../components/atoms/Card';
import Button from '../../../components/atoms/Button';
import { ArrowRight, ArrowLeft } from 'lucide-react';

export interface QuestionnaireData {
  businessInfo: {
    industry: string;
    companySize: string;
    departments: string[];
  };
  currentProcesses: {
    marketingAutomation: number;
    salesProcesses: number;
    operationsEfficiency: number;
    hrManagement: number;
  };
  painPoints: {
    manualTasks: number;
    dataManagement: number;
    customerService: number;
    reportingAnalytics: number;
  };
  existingTools: string[];
  aiFamiliarity: number;
  goals: string[];
}

interface AIReadinessQuestionnaireProps {
  onComplete: (data: QuestionnaireData) => void;
}

const AIReadinessQuestionnaire: React.FC<AIReadinessQuestionnaireProps> = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<QuestionnaireData>({
    businessInfo: {
      industry: '',
      companySize: '',
      departments: []
    },
    currentProcesses: {
      marketingAutomation: 1,
      salesProcesses: 1,
      operationsEfficiency: 1,
      hrManagement: 1
    },
    painPoints: {
      manualTasks: 1,
      dataManagement: 1,
      customerService: 1,
      reportingAnalytics: 1
    },
    existingTools: [],
    aiFamiliarity: 1,
    goals: []
  });

  const steps = [
    {
      title: "Business Information",
      description: "Tell us about your organization"
    },
    {
      title: "Current Processes",
      description: "Rate your current automation level"
    },
    {
      title: "Pain Points",
      description: "Identify your biggest challenges"
    },
    {
      title: "Goals & Vision",
      description: "Define your AI transformation objectives"
    }
  ];

  const industries = [
    'Technology', 'Healthcare', 'Finance', 'Manufacturing', 'Retail',
    'Education', 'Real Estate', 'Hospitality', 'Logistics', 'Other'
  ];

  const companySizes = [
    'Startup (1-10)', 'Small (11-50)', 'Medium (51-200)', 
    'Large (201-1000)', 'Enterprise (1000+)'
  ];

  const departments = [
    'Marketing', 'Sales', 'Operations', 'HR', 'Finance', 
    'Customer Service', 'IT', 'Legal', 'R&D'
  ];

  const tools = [
    'CRM (Salesforce, HubSpot)', 'Email Marketing', 'Project Management',
    'Analytics Tools', 'ERP Systems', 'Social Media Management',
    'Document Management', 'Communication Tools', 'None'
  ];

  const goals = [
    'Reduce operational costs', 'Improve customer experience', 'Increase sales',
    'Enhance productivity', 'Better data insights', 'Streamline processes',
    'Reduce manual tasks', 'Improve decision making'
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete(formData);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateFormData = (section: keyof QuestionnaireData, data: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: typeof prev[section] === 'object' && prev[section] !== null 
        ? { ...prev[section], ...data }
        : data
    }));
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div>
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                Industry
              </Typography>
              <select 
                className="w-full p-3 bg-abyssal-base border border-consciousness/30 rounded-lg text-white"
                value={formData.businessInfo.industry}
                onChange={(e) => updateFormData('businessInfo', { industry: e.target.value })}
              >
                <option value="">Select your industry</option>
                {industries.map(industry => (
                  <option key={industry} value={industry}>{industry}</option>
                ))}
              </select>
            </div>

            <div>
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                Company Size
              </Typography>
              <select 
                className="w-full p-3 bg-abyssal-base border border-consciousness/30 rounded-lg text-white"
                value={formData.businessInfo.companySize}
                onChange={(e) => updateFormData('businessInfo', { companySize: e.target.value })}
              >
                <option value="">Select company size</option>
                {companySizes.map(size => (
                  <option key={size} value={size}>{size}</option>
                ))}
              </select>
            </div>

            <div>
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                Key Departments
              </Typography>
              <div className="grid grid-cols-2 gap-2">
                {departments.map(dept => (
                  <label key={dept} className="flex items-center space-x-2">
                    <input 
                      type="checkbox"
                      checked={formData.businessInfo.departments.includes(dept)}
                      onChange={(e) => {
                        const updated = e.target.checked 
                          ? [...formData.businessInfo.departments, dept]
                          : formData.businessInfo.departments.filter(d => d !== dept);
                        updateFormData('businessInfo', { departments: updated });
                      }}
                      className="rounded border-consciousness/30"
                    />
                    <Typography variant="xs" color="secondary">{dept}</Typography>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            {Object.entries(formData.currentProcesses).map(([key, value]) => (
              <div key={key}>
                <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} (1-5)
                </Typography>
                <input 
                  type="range"
                  min="1"
                  max="5"
                  value={value}
                  onChange={(e) => updateFormData('currentProcesses', { [key]: parseInt(e.target.value) })}
                  className="w-full slider"
                />
                <div className="flex justify-between text-xs text-tertiary mt-1">
                  <span>Manual</span>
                  <span>Fully Automated</span>
                </div>
              </div>
            ))}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            {Object.entries(formData.painPoints).map(([key, value]) => (
              <div key={key}>
                <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} Pain Level (1-5)
                </Typography>
                <input 
                  type="range"
                  min="1"
                  max="5"
                  value={value}
                  onChange={(e) => updateFormData('painPoints', { [key]: parseInt(e.target.value) })}
                  className="w-full slider"
                />
                <div className="flex justify-between text-xs text-tertiary mt-1">
                  <span>No Issue</span>
                  <span>Major Problem</span>
                </div>
              </div>
            ))}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                Current Tools (Select all that apply)
              </Typography>
              <div className="grid grid-cols-1 gap-2">
                {tools.map(tool => (
                  <label key={tool} className="flex items-center space-x-2">
                    <input 
                      type="checkbox"
                      checked={formData.existingTools.includes(tool)}
                      onChange={(e) => {
                        const updated = e.target.checked 
                          ? [...formData.existingTools, tool]
                          : formData.existingTools.filter(t => t !== tool);
                        setFormData(prev => ({ ...prev, existingTools: updated }));
                      }}
                      className="rounded border-consciousness/30"
                    />
                    <Typography variant="xs" color="secondary">{tool}</Typography>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                AI Familiarity Level (1-5)
              </Typography>
              <input 
                type="range"
                min="1"
                max="5"
                value={formData.aiFamiliarity}
                onChange={(e) => setFormData(prev => ({ ...prev, aiFamiliarity: parseInt(e.target.value) }))}
                className="w-full slider"
              />
              <div className="flex justify-between text-xs text-tertiary mt-1">
                <span>Complete Beginner</span>
                <span>Expert</span>
              </div>
            </div>

            <div>
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                Primary Goals (Select all that apply)
              </Typography>
              <div className="grid grid-cols-1 gap-2">
                {goals.map(goal => (
                  <label key={goal} className="flex items-center space-x-2">
                    <input 
                      type="checkbox"
                      checked={formData.goals.includes(goal)}
                      onChange={(e) => {
                        const updated = e.target.checked 
                          ? [...formData.goals, goal]
                          : formData.goals.filter(g => g !== goal);
                        setFormData(prev => ({ ...prev, goals: updated }));
                      }}
                      className="rounded border-consciousness/30"
                    />
                    <Typography variant="xs" color="secondary">{goal}</Typography>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card variant="neural" className="max-w-2xl mx-auto p-8">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex justify-between mb-4">
          {steps.map((step, index) => (
            <div key={index} className={`flex-1 ${index < steps.length - 1 ? 'mr-4' : ''}`}>
              <div className={`h-2 rounded-full ${index <= currentStep ? 'bg-consciousness' : 'bg-gray-600'}`}></div>
            </div>
          ))}
        </div>
        <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
          Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
        </Typography>
        <Typography variant="sm" color="secondary">
          {steps[currentStep].description}
        </Typography>
      </div>

      {/* Step Content */}
      <div className="mb-8">
        {renderStep()}
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          variant="outline-quantum" 
          onClick={handlePrevious}
          disabled={currentStep === 0}
          leftIcon={<ArrowLeft className="w-4 h-4" />}
        >
          Previous
        </Button>
        
        <Button 
          variant="quantum" 
          onClick={handleNext}
          rightIcon={<ArrowRight className="w-4 h-4" />}
        >
          {currentStep === steps.length - 1 ? 'Complete Analysis' : 'Next'}
        </Button>
      </div>
    </Card>
  );
};

export default AIReadinessQuestionnaire;
