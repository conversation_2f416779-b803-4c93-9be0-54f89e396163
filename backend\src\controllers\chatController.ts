import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import db from '../utils/database';
import { ValidationError, NotFoundError } from '../types';
import { AuthenticatedRequest } from '../middleware/auth';

export class ChatController {
  async createChatSession(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const { context, initial_message } = req.body;

      const [session] = await db('chat_sessions')
        .insert({
          id: uuidv4(),
          user_id: userId,
          context: context ? JSON.stringify(context) : null,
          status: 'active',
          created_at: new Date(),
        })
        .returning('*');

      // If there's an initial message, create it
      if (initial_message) {
        await db('chat_messages').insert({
          id: uuidv4(),
          session_id: session.id,
          role: 'user',
          content: initial_message,
          created_at: new Date(),
        });

        // Generate ACI response (placeholder - integrate with actual ACI service)
        const aciResponse = await this.generateACIResponse(initial_message, context);
        
        await db('chat_messages').insert({
          id: uuidv4(),
          session_id: session.id,
          role: 'assistant',
          content: aciResponse,
          metadata: JSON.stringify({ 
            model: 'aci-v1',
            confidence: 0.95,
            processing_time: 150 
          }),
          created_at: new Date(),
        });
      }

      res.status(201).json({
        success: true,
        data: {
          session_id: session.id,
          status: session.status,
        },
        message: 'Chat session created successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async getChatSessions(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const { page = 1, limit = 10, status } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      let query = db('chat_sessions')
        .where({ user_id: userId })
        .select('*')
        .orderBy('updated_at', 'desc');

      if (status) {
        query = query.where('status', status as string);
      }

      // Get total count
      const totalQuery = query.clone();
      const [{ count: total }] = await totalQuery.count('* as count');

      // Apply pagination
      const sessions = await query
        .limit(Number(limit))
        .offset(offset);

      // Get message counts for each session
      const sessionIds = sessions.map(s => s.id);
      const messageCounts = await db('chat_messages')
        .whereIn('session_id', sessionIds)
        .select('session_id')
        .count('* as message_count')
        .groupBy('session_id');

      const messageCountMap = messageCounts.reduce((acc, item) => {
        acc[item.session_id] = Number(item.message_count);
        return acc;
      }, {} as Record<string, number>);

      const enrichedSessions = sessions.map(session => ({
        ...session,
        message_count: messageCountMap[session.id] || 0,
        context: session.context ? JSON.parse(session.context) : null,
      }));

      res.json({
        success: true,
        data: {
          sessions: enrichedSessions,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total: Number(total),
            pages: Math.ceil(Number(total) / Number(limit)),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async getChatSession(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      const session = await db('chat_sessions')
        .where({ id, user_id: userId })
        .first();

      if (!session) {
        throw new NotFoundError('Chat session not found');
      }

      // Get messages for this session
      const messages = await db('chat_messages')
        .where({ session_id: id })
        .select('*')
        .orderBy('created_at', 'asc');

      const formattedMessages = messages.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        metadata: msg.metadata ? JSON.parse(msg.metadata) : null,
        created_at: msg.created_at,
      }));

      res.json({
        success: true,
        data: {
          session: {
            ...session,
            context: session.context ? JSON.parse(session.context) : null,
          },
          messages: formattedMessages,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async sendMessage(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const { content, attachments } = req.body;

      if (!content || content.trim().length === 0) {
        throw new ValidationError('Message content is required');
      }

      // Verify session ownership
      const session = await db('chat_sessions')
        .where({ id, user_id: userId })
        .first();

      if (!session) {
        throw new NotFoundError('Chat session not found');
      }

      if (session.status !== 'active') {
        throw new ValidationError('Chat session is not active');
      }

      // Create user message
      const [userMessage] = await db('chat_messages')
        .insert({
          id: uuidv4(),
          session_id: id,
          role: 'user',
          content: content.trim(),
          attachments: attachments ? JSON.stringify(attachments) : null,
          created_at: new Date(),
        })
        .returning('*');

      // Generate ACI response
      const context = session.context ? JSON.parse(session.context) : null;
      const aciResponse = await this.generateACIResponse(content, context);

      // Create assistant message
      const [assistantMessage] = await db('chat_messages')
        .insert({
          id: uuidv4(),
          session_id: id,
          role: 'assistant',
          content: aciResponse,
          metadata: JSON.stringify({
            model: 'aci-v1',
            confidence: 0.95,
            processing_time: 200,
            context_used: !!context,
          }),
          created_at: new Date(),
        })
        .returning('*');

      // Update session timestamp
      await db('chat_sessions')
        .where({ id })
        .update({ updated_at: new Date() });

      // Emit real-time update if socket.io is available
      const io = req.app.get('io');
      if (io) {
        io.to(`user_${userId}`).emit('new_message', {
          session_id: id,
          messages: [
            {
              id: userMessage.id,
              role: 'user',
              content: userMessage.content,
              created_at: userMessage.created_at,
            },
            {
              id: assistantMessage.id,
              role: 'assistant',
              content: assistantMessage.content,
              metadata: JSON.parse(assistantMessage.metadata),
              created_at: assistantMessage.created_at,
            },
          ],
        });
      }

      res.json({
        success: true,
        data: {
          user_message: {
            id: userMessage.id,
            role: 'user',
            content: userMessage.content,
            created_at: userMessage.created_at,
          },
          assistant_message: {
            id: assistantMessage.id,
            role: 'assistant',
            content: assistantMessage.content,
            metadata: JSON.parse(assistantMessage.metadata),
            created_at: assistantMessage.created_at,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  async updateChatSession(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;
      const { status, context } = req.body;

      const validStatuses = ['active', 'paused', 'ended'];
      if (status && !validStatuses.includes(status)) {
        throw new ValidationError('Invalid status');
      }

      const updateData: any = { updated_at: new Date() };
      if (status) updateData.status = status;
      if (context) updateData.context = JSON.stringify(context);

      const [updatedSession] = await db('chat_sessions')
        .where({ id, user_id: userId })
        .update(updateData)
        .returning('*');

      if (!updatedSession) {
        throw new NotFoundError('Chat session not found');
      }

      res.json({
        success: true,
        data: {
          ...updatedSession,
          context: updatedSession.context ? JSON.parse(updatedSession.context) : null,
        },
        message: 'Chat session updated successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  async deleteChatSession(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      // Delete messages first (cascade)
      await db('chat_messages')
        .whereIn('session_id', 
          db('chat_sessions')
            .select('id')
            .where({ id, user_id: userId })
        )
        .del();

      // Delete session
      const deletedCount = await db('chat_sessions')
        .where({ id, user_id: userId })
        .del();

      if (deletedCount === 0) {
        throw new NotFoundError('Chat session not found');
      }

      res.json({
        success: true,
        message: 'Chat session deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }

  private async generateACIResponse(userMessage: string, context: any): Promise<string> {
    // This is a placeholder for ACI integration
    // In a real implementation, you would integrate with your ACI service
    
    const responses = [
      "I understand your question about workflow automation. Let me help you optimize your processes using SymbioWave's ACI technology.",
      "Based on your query, I can suggest several AI-powered solutions that would benefit your organization.",
      "That's an excellent question about AI transformation. Our platform can help you implement these changes efficiently.",
      "I can help you design a workflow that addresses your specific needs. Would you like me to create a custom automation for you?",
      "Your use case is perfect for our biomimetic AI approach. Let me explain how we can optimize this process.",
    ];

    // Simple response selection based on message content
    if (userMessage.toLowerCase().includes('workflow')) {
      return responses[0];
    } else if (userMessage.toLowerCase().includes('ai') || userMessage.toLowerCase().includes('automation')) {
      return responses[1];
    } else if (userMessage.toLowerCase().includes('help') || userMessage.toLowerCase().includes('how')) {
      return responses[3];
    } else {
      return responses[Math.floor(Math.random() * responses.length)];
    }
  }
}

export default new ChatController();
