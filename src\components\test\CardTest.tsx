import React, { useState } from 'react';
import Card from '@/components/atoms/Card';
import Typography from '@/components/atoms/Typography';
import { Zap, Brain, Atom, Eye, Lightbulb, Heart, Cog, Beaker, ArrowRightLeft, Database, Leaf, TrendingUp, Globe } from 'lucide-react';

const CardTest: React.FC = () => {
  const [selectedVariant, setSelectedVariant] = useState<string>('neural');
  const [selectedSize, setSelectedSize] = useState<string>('md');
  const [isMorphic, setIsMorphic] = useState(false);
  const [isInteractive, setIsInteractive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const coreVariants = [
    { name: 'neural', icon: <Brain className="w-5 h-5" />, label: 'Neural' },
    { name: 'quantum', icon: <Atom className="w-5 h-5" />, label: 'Quantum' },
    { name: 'cellular', icon: <Leaf className="w-5 h-5" />, label: 'Cellular' },
    { name: 'consciousness', icon: <Eye className="w-5 h-5" />, label: 'Consciousness' },
    { name: 'creativity', icon: <Lightbulb className="w-5 h-5" />, label: 'Creativity' },
    { name: 'intuition', icon: <Heart className="w-5 h-5" />, label: 'Intuition' }
  ];

  const divisionVariants = [
    { name: 'symbioautomate', icon: <Cog className="w-5 h-5" />, label: 'SymbioAutomate', color: 'symbioautomate' },
    { name: 'symbiolabs', icon: <Beaker className="w-5 h-5" />, label: 'SymbioLabs', color: 'symbiolabs' },
    { name: 'symbioxchange', icon: <ArrowRightLeft className="w-5 h-5" />, label: 'SymbioXchange', color: 'symbioxchange' },
    { name: 'symbioedge', icon: <Database className="w-5 h-5" />, label: 'SymbioEdge', color: 'symbioedge' },
    { name: 'symbioimpact', icon: <Leaf className="w-5 h-5" />, label: 'SymbioImpact', color: 'symbioimpact' },
    { name: 'symbioventures', icon: <TrendingUp className="w-5 h-5" />, label: 'SymbioVentures', color: 'symbioventures' },
    { name: 'symbioalliance', icon: <Globe className="w-5 h-5" />, label: 'SymbioAlliance', color: 'symbioalliance' }
  ];

  const sizes = ['xs', 'sm', 'md', 'lg', 'xl'];

  return (
    <div className="min-h-screen bg-abyssal-void p-8">
      <div className="max-w-7xl mx-auto">
        <Typography variant="3xl" weight="bold" gradient="consciousness" className="text-center mb-8">
          Card Component - Bio-Quantum Integration Test
        </Typography>

        {/* Controls */}
        <Card variant="consciousness" className="p-6 mb-8">
          <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
            Test Controls
          </Typography>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <Typography variant="sm" color="secondary" className="mb-2">Size</Typography>
              <select 
                value={selectedSize} 
                onChange={(e) => setSelectedSize(e.target.value)}
                className="w-full p-2 bg-abyssal-elevated border border-consciousness/20 rounded-lg text-text-primary"
              >
                {sizes.map(size => (
                  <option key={size} value={size}>{size.toUpperCase()}</option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  checked={isMorphic} 
                  onChange={(e) => setIsMorphic(e.target.checked)}
                  className="rounded border-consciousness/20"
                />
                <Typography variant="sm" color="secondary">Morphic</Typography>
              </label>
              
              <label className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  checked={isInteractive} 
                  onChange={(e) => setIsInteractive(e.target.checked)}
                  className="rounded border-consciousness/20"
                />
                <Typography variant="sm" color="secondary">Interactive</Typography>
              </label>
            </div>
            
            <div>
              <label className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  checked={isLoading} 
                  onChange={(e) => setIsLoading(e.target.checked)}
                  className="rounded border-consciousness/20"
                />
                <Typography variant="sm" color="secondary">Loading State</Typography>
              </label>
            </div>
          </div>
        </Card>

        {/* Core Variants */}
        <Card variant="neural" className="p-6 mb-8">
          <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
            Core Bio-Quantum Variants
          </Typography>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            {coreVariants.map((variant) => (
              <Card
                key={variant.name}
                variant={variant.name as any}
                size={selectedSize as any}
                morphic={isMorphic}
                interactive={isInteractive}
                loading={isLoading}
                onClick={() => setSelectedVariant(variant.name)}
                className={`p-4 cursor-pointer transition-all duration-300 ${
                  selectedVariant === variant.name ? 'ring-2 ring-consciousness' : ''
                }`}
              >
                <div className="flex items-center space-x-3 mb-2">
                  {variant.icon}
                  <Typography variant="md" weight="semibold" color="consciousness">
                    {variant.label}
                  </Typography>
                </div>
                <Typography variant="sm" color="secondary">
                  Bio-quantum {variant.label.toLowerCase()} processing with consciousness-driven aesthetics
                </Typography>
              </Card>
            ))}
          </div>
        </Card>

        {/* Division Variants */}
        <Card variant="quantum" className="p-6 mb-8">
          <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
            SymbioWave Division Variants
          </Typography>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {divisionVariants.map((variant) => (
              <Card
                key={variant.name}
                variant={variant.name as any}
                size={selectedSize as any}
                morphic={isMorphic}
                interactive={isInteractive}
                loading={isLoading}
                onClick={() => setSelectedVariant(variant.name)}
                className={`p-4 cursor-pointer transition-all duration-300 ${
                  selectedVariant === variant.name ? 'ring-2 ring-consciousness' : ''
                }`}
              >
                <div className="flex items-center space-x-3 mb-2">
                  {variant.icon}
                  <Typography variant="sm" weight="semibold" color={variant.color as any}>
                    {variant.label}
                  </Typography>
                </div>
                <Typography variant="xs" color="secondary">
                  Division-specific bio-quantum interface
                </Typography>
              </Card>
            ))}
          </div>
        </Card>

        {/* Size Demonstration */}
        <Card variant="cellular" className="p-6 mb-8">
          <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
            Size Variations
          </Typography>
          <div className="flex flex-wrap items-end gap-4">
            {sizes.map((size) => (
              <Card
                key={size}
                variant="consciousness"
                size={size as any}
                morphic={isMorphic}
                className="text-center"
              >
                <Typography variant="sm" weight="semibold" color="consciousness">
                  {size.toUpperCase()}
                </Typography>
                <Typography variant="xs" color="secondary">
                  Size demo
                </Typography>
              </Card>
            ))}
          </div>
        </Card>

        {/* Interactive Features */}
        <Card variant="creativity" className="p-6">
          <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
            Interactive Features Demo
          </Typography>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card
              variant="quantum"
              size="md"
              interactive
              onClick={() => alert('Quantum interface activated!')}
              onHover={(isHovered) => console.log('Quantum hover:', isHovered)}
            >
              <Zap className="w-8 h-8 text-consciousness mb-2" />
              <Typography variant="md" weight="semibold" color="consciousness">
                Interactive Card
              </Typography>
              <Typography variant="sm" color="secondary">
                Click me for quantum activation
              </Typography>
            </Card>

            <Card
              variant="symbioautomate"
              size="md"
              morphic
              interactive
            >
              <Cog className="w-8 h-8 text-symbioautomate mb-2" />
              <Typography variant="md" weight="semibold" color="symbioautomate">
                Morphic Design
              </Typography>
              <Typography variant="sm" color="secondary">
                Organic bio-quantum shapes
              </Typography>
            </Card>

            <Card
              variant="consciousness"
              size="md"
              loading
              disabled
            >
              <Eye className="w-8 h-8 text-consciousness mb-2" />
              <Typography variant="md" weight="semibold" color="consciousness">
                Loading State
              </Typography>
              <Typography variant="sm" color="secondary">
                Bio-quantum processing...
              </Typography>
            </Card>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default CardTest;
