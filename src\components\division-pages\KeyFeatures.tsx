
import React from 'react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import { Network, Brain, Zap, Shield, BarChart3, Cpu } from 'lucide-react';

const KeyFeatures: React.FC = () => {
  const features = [
    {
      icon: Network,
      title: "ACI-Powered Workflow Design",
      description: "Intuitive workflow creation with AI suggesting optimizations and identifying bottlenecks automatically.",
      color: "consciousness"
    },
    {
      icon: Brain,
      title: "Self-Optimizing Processes",
      description: "Workflows that learn and improve over time, adapting to new conditions without manual intervention.",
      color: "harmony"
    },
    {
      icon: Zap,
      title: "Real-time Adaptation",
      description: "Dynamic response to changing conditions, traffic, weather, and external data sources.",
      color: "creativity"
    },
    {
      icon: BarChart3,
      title: "SymbioXchange Integration",
      description: "Automatic identification and execution of resource trading opportunities from the marketplace.",
      color: "harmony"
    },
    {
      icon: Shield,
      title: "Predictive Maintenance",
      description: "AI-powered anomaly detection and predictive maintenance to prevent downtime.",
      color: "consciousness"
    },
    {
      icon: Cpu,
      title: "Resource Management",
      description: "Intelligent allocation and optimization of resources to minimize waste and maximize efficiency.",
      color: "creativity"
    }
  ];

  return (
    <section className="py-20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            gradient="neural"
            className="mb-4"
          >
            Key Features & Capabilities
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            Revolutionary automation that goes beyond simple rule-based systems
          </Typography>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <Card key={index} variant="neural" className="border-harmony/30 p-6">
                <div className={`w-12 h-12 rounded-biomorphic bg-${feature.color}/20 flex items-center justify-center mb-4`}>
                  <Icon className={`w-6 h-6 text-${feature.color}`} />
                </div>
                
                <Typography 
                  as="h3" 
                  variant="lg" 
                  weight="semibold" 
                  color={feature.color as any}
                  className="mb-3"
                >
                  {feature.title}
                </Typography>
                
                <Typography 
                  variant="sm" 
                  color="secondary"
                  className="leading-relaxed"
                >
                  {feature.description}
                </Typography>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default KeyFeatures;
