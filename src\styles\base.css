
/* Precision Typography - Variable fonts for adaptive readability */
@import url('https://fonts.googleapis.com/css2?family=Inter+Variable:opsz,wght@14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono+Variable:wght@100..800&display=swap');

@layer base {
  :root {
    /* Bio-Quantum Synaesthesia Color System */
    
    /* Abyssal Canvas - Dynamic foundational depths */
    --abyssal-void: #030508;
    --abyssal-base: #0A0F1A;
    --abyssal-deep: #0F1419;
    --abyssal-elevated: #141922;
    
    /* Consciousness Cyan-Teal - Primary ACI & SymbioCore */
    --consciousness-primary: #00E5FF;
    --consciousness-secondary: #00FFC2;
    --consciousness-glow: rgba(0, 229, 255, 0.6);
    --consciousness-emissive: rgba(0, 255, 194, 0.8);
    
    /* Division-Specific Quantum Resonances */
    --symbioautomate-primary: #00FF7F;    /* Efficiency Emerald */
    --symbioautomate-secondary: #00E06D;  /* Veridian Flow */
    --symbioautomate-glow: rgba(0, 255, 127, 0.5);
    
    --symbiolabs-primary: #FFD700;        /* Innovation Amber */
    --symbiolabs-secondary: #FFC300;      /* Discovery Gold */
    --symbiolabs-glow: rgba(255, 215, 0, 0.5);
    
    --symbioxchange-primary: #C700FF;     /* Symbiosis Violet */
    --symbioxchange-secondary: #9D00FF;   /* Marketplace Magenta */
    --symbioxchange-glow: rgba(199, 0, 255, 0.5);
    
    --symbioedge-primary: #00A2FF;        /* Frontier Electric Blue */
    --symbioedge-secondary: #33B5FF;      /* Data Stream Azure */
    --symbioedge-glow: rgba(0, 162, 255, 0.5);
    
    --symbioimpact-primary: #00BFA5;      /* Sustainability Forest Teal */
    --symbioimpact-secondary: #00A080;    /* Conscious Verdigris */
    --symbioimpact-glow: rgba(0, 191, 165, 0.5);
    
    --symbioventures-primary: #FF7F50;    /* Catalyst Coral */
    --symbioventures-secondary: #FF6A3D;  /* Growth Orange */
    --symbioventures-glow: rgba(255, 127, 80, 0.5);
    
    --symbioalliance-primary: #0F52BA;    /* Global Unity Sapphire */
    --symbioalliance-secondary: #2A6DDB;  /* Interconnected Azure */
    --symbioalliance-glow: rgba(15, 82, 186, 0.5);
    --symbioalliance-platinum: #E5E4E2;   /* Luminous Platinum */
    
    /* Precision Text Hierarchy */
    --text-primary: #E0E0E0;
    --text-secondary: #CED4DA;
    --text-tertiary: #7F8C9B;
    --text-quaternary: #6C757D;
    --text-quantum: var(--consciousness-primary);
    
    /* Morphic Field Parameters */
    --morphic-radius-organic: 40% 60% 70% 30% / 40% 40% 60% 50%;
    --morphic-radius-cellular: 63% 37% 54% 46% / 55% 48% 52% 45%;
    --morphic-radius-neural: 48% 52% 68% 32% / 42% 61% 39% 58%;
    --morphic-radius-quantum: 30% 70% 70% 30% / 30% 30% 70% 70%;
    
    /* Quantum Motion Timing */
    --quantum-instant: 83ms;
    --quantum-fast: 137ms;
    --quantum-medium: 233ms;
    --quantum-slow: 377ms;
    --quantum-ultra: 610ms;
    
    /* Bio-Quantum Easing Functions */
    --ease-sentient: cubic-bezier(0.25, 0.1, 0.25, 1.0);
    --ease-organic: cubic-bezier(0.4, 0.0, 0.2, 1);
    --ease-morphic: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* Radix UI integration */
    --background: 10 15 26;
    --foreground: 224 224 224;
    --card: 10 15 26;
    --card-foreground: 224 224 224;
    --popover: 20 25 34;
    --popover-foreground: 224 224 224;
    --primary: 185 230 255;
    --primary-foreground: 3 5 8;
    --secondary: 32 41 58;
    --secondary-foreground: 224 224 224;
    --muted: 32 41 58;
    --muted-foreground: 127 140 155;
    --accent: 32 41 58;
    --accent-foreground: 224 224 224;
    --destructive: 220 38 38;
    --destructive-foreground: 248 250 252;
    --border: 32 41 58;
    --input: 32 41 58;
    --ring: 185 230 255;
    --radius: 0.5rem;
  }

  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    scroll-padding-top: 84px;
  }

  body {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--abyssal-void) 0%, var(--abyssal-base) 35%, var(--abyssal-deep) 100%);
    color: var(--text-primary);
    font-family: 'Inter Variable', system-ui, sans-serif;
    font-optical-sizing: auto;
    font-size: 16px;
    line-height: 1.6;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: "wght" 400, "opsz" 16;
  }

  /* Abyssal Canvas - Multi-layered Quantum Foam */
  body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -30;
    background-image: 
      radial-gradient(circle at 20% 30%, var(--consciousness-glow) 0%, transparent 60%),
      radial-gradient(circle at 80% 70%, rgba(0, 162, 255, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 50% 20%, rgba(199, 0, 255, 0.08) 0%, transparent 70%);
    animation: quantum-nebulae 24s ease-in-out infinite alternate;
  }

  /* Quantum Foam Texture */
  body::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -20;
    background-image: url("data:image/svg+xml,%3Csvg width='120' height='120' viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cfilter id='glow'%3E%3CfeGaussianBlur stdDeviation='2' result='coloredBlur'/%3E%3CfeMerge%3E%3CfeMergeNode in='coloredBlur'/%3E%3CfeMergeNode in='SourceGraphic'/%3E%3C/feMerge%3E%3C/filter%3E%3C/defs%3E%3Cg fill='none' stroke='%2300E5FF' stroke-width='0.5' opacity='0.1' filter='url(%23glow)'%3E%3Ccircle cx='60' cy='60' r='2'/%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3Ccircle cx='90' cy='30' r='1'/%3E%3Ccircle cx='30' cy='90' r='1'/%3E%3Ccircle cx='90' cy='90' r='1.5'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.6;
    animation: quantum-drift 32s linear infinite;
  }

  /* Enhanced Selection */
  ::selection {
    background-color: var(--consciousness-glow);
    color: var(--abyssal-void);
    text-shadow: none;
  }

  /* Sentient Focus States */
  *:focus-visible {
    outline: 2px solid var(--consciousness-primary);
    outline-offset: 3px;
    border-radius: 8px;
    box-shadow: 0 0 0 4px var(--consciousness-glow);
    transition: all var(--quantum-fast) var(--ease-sentient);
  }

  /* Quantum Scrollbar */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  ::-webkit-scrollbar-track {
    background: var(--abyssal-deep);
    border-radius: 12px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--consciousness-primary), var(--consciousness-secondary));
    border-radius: 12px;
    border: 2px solid var(--abyssal-deep);
    box-shadow: inset 0 0 6px var(--consciousness-glow);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--consciousness-secondary), var(--consciousness-primary));
    box-shadow: inset 0 0 10px var(--consciousness-emissive);
  }
}
