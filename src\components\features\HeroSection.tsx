import React, { useEffect, useRef, useState } from 'react';
import { ArrowRight, Brain, Network, Zap, Globe, Target } from 'lucide-react';
import Button from '../atoms/Button';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import ACIParticleSystem from '../interactive/ACIParticleSystem';

/**
 * CellularNetworkOverlay - Advanced mouse-following neural network
 * Represents the convergence of biological and digital intelligence
 */
const CellularNetworkOverlay: React.FC<{ mousePosition: { x: number; y: number } }> = ({ mousePosition }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const mousePositionRef = useRef(mousePosition);
  const nodesRef = useRef<Array<{
    x: number;
    y: number;
    vx: number;
    vy: number;
    targetX: number;
    targetY: number;
    opacity: number;
    size: number;
    type: 'primary' | 'secondary' | 'tertiary';
  }>>([]);

  // Update mouse position ref when prop changes
  useEffect(() => {
    mousePositionRef.current = mousePosition;
  }, [mousePosition]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let canvasWidth = 0;
    let canvasHeight = 0;

    // Initialize nodes with sophisticated distribution
    const initializeNodes = () => {
      const nodes = [];
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      
      // Primary nodes - core intelligence hubs
      for (let i = 0; i < 8; i++) {
        const angle = (i / 8) * Math.PI * 2;
        const distance = 120 + Math.random() * 80;
        nodes.push({
          x: centerX + Math.cos(angle) * distance,
          y: centerY + Math.sin(angle) * distance,
          vx: 0,
          vy: 0,
          targetX: centerX + Math.cos(angle) * distance,
          targetY: centerY + Math.sin(angle) * distance,
          opacity: 0.8,
          size: 4 + Math.random() * 3,
          type: 'primary' as const
        });
      }

      // Secondary nodes - connection points
      for (let i = 0; i < 16; i++) {
        const angle = (i / 16) * Math.PI * 2;
        const distance = 200 + Math.random() * 150;
        nodes.push({
          x: centerX + Math.cos(angle) * distance,
          y: centerY + Math.sin(angle) * distance,
          vx: 0,
          vy: 0,
          targetX: centerX + Math.cos(angle) * distance,
          targetY: centerY + Math.sin(angle) * distance,
          opacity: 0.6,
          size: 2 + Math.random() * 2,
          type: 'secondary' as const
        });
      }

      // Tertiary nodes - ambient intelligence
      for (let i = 0; i < 32; i++) {
        nodes.push({
          x: Math.random() * canvasWidth,
          y: Math.random() * canvasHeight,
          vx: 0,
          vy: 0,
          targetX: Math.random() * canvasWidth,
          targetY: Math.random() * canvasHeight,
          opacity: 0.3,
          size: 1 + Math.random(),
          type: 'tertiary' as const
        });
      }

      nodesRef.current = nodes;
    };

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvasWidth = rect.width;
      canvasHeight = rect.height;
      canvas.width = rect.width * window.devicePixelRatio;
      canvas.height = rect.height * window.devicePixelRatio;
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
      
      // Force re-initialization after resize
      if (canvasWidth > 0 && canvasHeight > 0) {
        initializeNodes();
      }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Ultra-slow animation loop with mouse interaction
    const animate = () => {
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      const mouseX = mousePositionRef.current.x * canvasWidth;
      const mouseY = mousePositionRef.current.y * canvasHeight;

      // Update node positions with extremely slow mouse attraction
      nodesRef.current.forEach((node, index) => {
        const mouseDistance = Math.sqrt(
          Math.pow(mouseX - node.x, 2) + Math.pow(mouseY - node.y, 2)
        );

        // Ultra-slow mouse influence based on distance and node type
        const influence = node.type === 'primary' ? 0.008 : 
                         node.type === 'secondary' ? 0.005 : 0.003;
        
        if (mouseDistance < 300) {
          const attractionForce = (300 - mouseDistance) / 300;
          node.vx += (mouseX - node.x) * influence * attractionForce;
          node.vy += (mouseY - node.y) * influence * attractionForce;
        }

        // Ultra-slow return to target position
        node.vx += (node.targetX - node.x) * 0.002;
        node.vy += (node.targetY - node.y) * 0.002;

        // Apply velocity with stronger damping for slower movement
        node.vx *= 0.9;
        node.vy *= 0.9;
        node.x += node.vx;
        node.y += node.vy;

        // Dynamic opacity based on mouse proximity
        const proximityOpacity = mouseDistance < 200 ? 
          (200 - mouseDistance) / 200 * 0.5 : 0;
        node.opacity = Math.min(1, node.opacity + proximityOpacity);
      });

      // Draw connections with advanced styling
      nodesRef.current.forEach((node, i) => {
        nodesRef.current.slice(i + 1).forEach((otherNode, j) => {
          const distance = Math.sqrt(
            Math.pow(node.x - otherNode.x, 2) + Math.pow(node.y - otherNode.y, 2)
          );

          if (distance < 150) {
            const opacity = (150 - distance) / 150 * 0.3;
            const gradient = ctx.createLinearGradient(node.x, node.y, otherNode.x, otherNode.y);
            
            if (node.type === 'primary' && otherNode.type === 'primary') {
              gradient.addColorStop(0, `rgba(47, 0, 255, ${opacity})`);
              gradient.addColorStop(0.5, `rgba(0, 255, 213, ${opacity * 0.8})`);
              gradient.addColorStop(1, `rgba(255, 0, 204, ${opacity})`);
            } else if (node.type === 'secondary' || otherNode.type === 'secondary') {
              gradient.addColorStop(0, `rgba(0, 255, 213, ${opacity * 0.6})`);
              gradient.addColorStop(1, `rgba(255, 0, 204, ${opacity * 0.4})`);
            } else {
              gradient.addColorStop(0, `rgba(255, 255, 255, ${opacity * 0.2})`);
              gradient.addColorStop(1, `rgba(255, 255, 255, ${opacity * 0.1})`);
            }

            ctx.strokeStyle = gradient;
            ctx.lineWidth = node.type === 'primary' && otherNode.type === 'primary' ? 2 : 1;
            ctx.beginPath();
            ctx.moveTo(node.x, node.y);
            ctx.lineTo(otherNode.x, otherNode.y);
            ctx.stroke();
          }
        });
      });

      // Draw nodes with sophisticated styling
      nodesRef.current.forEach(node => {
        const gradient = ctx.createRadialGradient(
          node.x, node.y, 0,
          node.x, node.y, node.size * 3
        );

        if (node.type === 'primary') {
          gradient.addColorStop(0, `rgba(47, 0, 255, ${node.opacity})`);
          gradient.addColorStop(0.5, `rgba(0, 255, 213, ${node.opacity * 0.7})`);
          gradient.addColorStop(1, `rgba(255, 0, 204, 0)`);
        } else if (node.type === 'secondary') {
          gradient.addColorStop(0, `rgba(0, 255, 213, ${node.opacity * 0.8})`);
          gradient.addColorStop(0.7, `rgba(255, 0, 204, ${node.opacity * 0.4})`);
          gradient.addColorStop(1, `rgba(255, 255, 255, 0)`);
        } else {
          gradient.addColorStop(0, `rgba(255, 255, 255, ${node.opacity * 0.6})`);
          gradient.addColorStop(1, `rgba(255, 255, 255, 0)`);
        }

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(node.x, node.y, node.size, 0, Math.PI * 2);
        ctx.fill();

        // Core node highlight
        if (node.type === 'primary') {
          ctx.fillStyle = `rgba(255, 255, 255, ${node.opacity * 0.9})`;
          ctx.beginPath();
          ctx.arc(node.x, node.y, node.size * 0.3, 0, Math.PI * 2);
          ctx.fill();
        }
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    // Initialize after canvas is properly sized
    const initTimeout = setTimeout(() => {
      if (canvasWidth > 0 && canvasHeight > 0) {
        initializeNodes();
        animate();
      }
    }, 100);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      clearTimeout(initTimeout);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = undefined;
      }
    };
  }, []); // Empty dependency array - runs only once!

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ mixBlendMode: 'screen' }}
    />
  );
};

/**
 * HeroSection - The Genesis Portal (2698 Professional Edition)
 * Designed with the sophistication of humanity's greatest technological minds
 */
const HeroSection: React.FC = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const [textIndex, setTextIndex] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [quoteFadeIn, setQuoteFadeIn] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const heroRef = useRef<HTMLElement>(null);

  const catchyTexts = [
    "ACI for a Symbiotic Future",
    "Where Intelligence Meets Evolution",
    "The Future of Artificial Consciousness",
    "Revolutionizing Global Intelligence"
  ];

  useEffect(() => {
    // Force re-initialization when component mounts
    setIsInitialized(false);
    setTimeout(() => setIsInitialized(true), 200);
  }, []);

  useEffect(() => {
    // Ultra-slow text rotation - 12 seconds per change
    const interval = setInterval(() => {
      setTextIndex((prev) => (prev + 1) % catchyTexts.length);
    }, 12000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Ultra-slow quote fade-in after component mount
    const timer = setTimeout(() => setQuoteFadeIn(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (heroRef.current) {
        const rect = heroRef.current.getBoundingClientRect();
        setMousePosition({
          x: Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width)),
          y: Math.max(0, Math.min(1, (e.clientY - rect.top) / rect.height)),
        });
      }
    };

    const hero = heroRef.current;
    if (hero) {
      hero.addEventListener('mousemove', handleMouseMove);
      return () => hero.removeEventListener('mousemove', handleMouseMove);
    }
  }, []);

  return (
    <section 
      ref={heroRef}
      className="relative w-full min-h-screen flex items-center justify-center overflow-hidden pt-20 pb-8 z-10"
    >
      {/* Ultra-Slow Quantum Field Background */}
      <div className="absolute inset-0 opacity-12 z-0">
        <div 
          className="absolute w-[1200px] h-[1200px] bg-gradient-to-br from-[#2f00ff44] via-[#00ffd522] to-[#ff00cc11] rounded-full blur-[120px]"
          style={{
            top: '20%',
            left: '20%',
            transform: 'translate(-50%, -50%)',
            animation: 'ultraSlowPulse 30s ease-in-out infinite'
          }}
        />
        <div 
          className="absolute w-[800px] h-[800px] bg-gradient-to-br from-[#00ffd522] via-[#ff00cc11] to-[#2f00ff44] rounded-full blur-[80px]"
          style={{
            bottom: '30%',
            right: '30%',
            transform: 'translate(50%, 50%)',
            animation: 'ultraSlowPulse 40s ease-in-out infinite',
            animationDelay: '15s'
          }}
        />
      </div>

      {/* Custom CSS for ultra-slow animations */}
      <style>{`
        @keyframes ultraSlowPulse {
          0%, 100% { opacity: 0.3; transform: scale(1); }
          50% { opacity: 0.5; transform: scale(1.1); }
        }
        
        @keyframes ultraSlowFloat {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        
        .ultra-slow-float {
          animation: ultraSlowFloat 25s ease-in-out infinite;
        }
      `}</style>

      {/* Advanced Particle System */}
      <div className="absolute inset-0 pointer-events-none z-5 opacity-70">
        {isInitialized && <ACIParticleSystem mousePosition={mousePosition} isHovering={isHovering} />}
      </div>

      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-20">
        <div className="text-center">
          {/* Logo with Advanced Neural Network */}
          <div className="mb-6 flex justify-center w-full">
            <div 
              className="relative w-full max-w-4xl group ultra-slow-float"
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
            >
              {/* Ultra-Slow Quantum Glow Background */}
              <div 
                className="absolute inset-0 bg-gradient-to-br from-[#2f00ff33] via-[#00ffd522] to-[#ff00cc11] rounded-full blur-[100px] opacity-50 scale-150"
                style={{ animation: 'ultraSlowPulse 35s ease-in-out infinite' }}
              />
              
              {/* Neural Network Overlay - Always Active */}
              <div className="absolute inset-0 pointer-events-none">
                {isInitialized && <CellularNetworkOverlay mousePosition={mousePosition} />}
              </div>
              
              {/* Main Logo - Made Even Bigger */}
              <img 
                src="/lovable-uploads/69b2f8ba-ee9a-4a51-954a-923e57771f4d.png" 
                alt="SymbioWave - AI for a Symbiotic Future" 
                className="w-full h-auto max-h-[420px] sm:max-h-[520px] lg:max-h-[650px] xl:max-h-[750px] 2xl:max-h-[850px] object-contain drop-shadow-2xl relative z-10 transition-all duration-[3000ms] group-hover:drop-shadow-[0_0_50px_rgba(47,0,255,0.3)]"
              />
            </div>
          </div>

          {/* Professional Geoffrey Hinton Quote - Better Design */}
          <div className={`mb-8 max-w-5xl mx-auto transition-all duration-[5000ms] ${quoteFadeIn ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="relative group">
              {/* Enhanced Quantum Border Effect */}
              <div 
                className="absolute inset-0 bg-gradient-to-r from-[#2f00ff15] via-[#00ffd508] to-[#ff00cc15] rounded-[28px] blur-xl opacity-60 transition-opacity duration-[2000ms] group-hover:opacity-80"
                style={{ animation: 'ultraSlowPulse 45s ease-in-out infinite' }}
              />
              <div className="absolute inset-0 bg-gradient-to-br from-[#ffffff06] to-[#ffffff02] rounded-[28px] backdrop-blur-xl border border-white/8" />
              
              {/* Quote Container - Improved Design */}
              <div className="relative p-5 sm:p-6 lg:p-8">
                {/* Elegant Quote Marks */}
                <div className="absolute top-1 left-1 sm:top-2 sm:left-2 text-2xl sm:text-3xl font-serif text-white/15 leading-none select-none">"</div>
                
                {/* Main Quote - Premium Quote Typography */}
                <blockquote className="relative z-10">
                  <p className="font-normal text-xs sm:text-sm lg:text-base xl:text-lg text-white leading-relaxed tracking-wide mb-3 font-['Crimson_Text',_'Lora',_'Merriweather',_'Georgia',_serif] antialiased italic">
                    <span className="bg-gradient-to-r from-white/95 via-white/90 to-white/85 bg-clip-text text-transparent">
                      There's still a chance that we can figure out how to develop AI that won't want to take over from us. And because there's a chance, we should put 
                    </span>
                    <span className="bg-gradient-to-r from-[#00ffd5] via-[#2f00ff] to-[#ff00cc] bg-clip-text text-transparent font-semibold not-italic"> enormous resources </span>
                    <span className="bg-gradient-to-r from-white/95 via-white/90 to-white/85 bg-clip-text text-transparent">
                      into trying to figure that out. Because if we don't, it's gonna take over.
                    </span>
                  </p>
                </blockquote>
                
                {/* Attribution - Refined */}
                <footer className="relative z-10 flex flex-col sm:flex-row items-center justify-center gap-2 text-white/80">
                  <div className="w-8 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
                  <cite className="not-italic">
                    <span className="text-xs sm:text-sm font-semibold tracking-wide font-['Inter',_'SF_Pro_Display',_'Segoe_UI',_sans-serif]">
                      Geoffrey Hinton
                    </span>
                    <span className="block sm:inline sm:ml-1.5 text-[10px] sm:text-xs text-white/60 font-light tracking-wider">
                      The Godfather of AI • Diary of a CEO • June 16, 2025
                    </span>
                  </cite>
                  <div className="w-8 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent" />
                </footer>
                
                {/* Closing Quote Mark */}
                <div className="absolute bottom-1 right-1 sm:bottom-2 sm:right-2 text-2xl sm:text-3xl font-serif text-white/15 leading-none transform rotate-180 select-none">"</div>
              </div>
            </div>
          </div>

          {/* Dynamic Headlines */}
          <div className="space-y-8 mb-12">
            <div className="relative transition-all duration-[8000ms] ease-out">
              <Typography 
                as="h1" 
                variant="cosmic" 
                weight="bold" 
                align="center"
                className="mb-6 leading-[0.85] text-3xl sm:text-5xl lg:text-7xl xl:text-8xl transition-all duration-[8000ms] bg-gradient-to-r from-[#2f00ff] via-[#00ffd5] to-[#ff00cc] bg-clip-text text-transparent font-extrabold tracking-tight ultra-slow-float"
                key={textIndex}
              >
                <span className="relative inline-block font-['Inter',_'SF_Pro_Display',_'Helvetica_Neue',_sans-serif] tracking-tight drop-shadow-lg">
                  {catchyTexts[textIndex]}
                </span>
              </Typography>
            </div>
          </div>

          {/* Core Problem Statement */}
          <div className="mb-12" style={{ animationDelay: '3s' }}>
            <div className="p-6 glass-quantum rounded-[24px] border border-white/10 backdrop-blur-xl max-w-3xl mx-auto group hover:border-white/20 transition-all duration-[3000ms]">
              <Typography 
                as="h2" 
                variant="xl" 
                weight="semibold" 
                color="consciousness"
                align="center"
                className="mb-3 text-lg sm:text-xl font-['Inter',_'SF_Pro_Display',_'Helvetica_Neue',_sans-serif] tracking-wide"
              >
                $12 Trillion Lost Annually to Inefficient Systems
              </Typography>
              <Typography 
                as="p" 
                variant="lg" 
                color="secondary" 
                align="center"
                className="leading-relaxed text-base text-white/80 font-light"
              >
                While traditional AI operates in isolation, ACI creates a living, evolving intelligence 
                that adapts and collaborates in real-time across entire ecosystems.
              </Typography>
            </div>
          </div>

          {/* Ecosystem Gateway */}
          <div className="mb-12" style={{ animationDelay: '6s' }}>
            <Typography 
              as="h2" 
              variant="2xl" 
              weight="semibold" 
              color="consciousness"
              align="center"
              className="mb-6 text-xl sm:text-2xl font-['Inter',_'SF_Pro_Display',_'Helvetica_Neue',_sans-serif] tracking-wide"
            >
              Enter the Symbiotic Ecosystem
            </Typography>
            
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Card 
                variant="consciousness" 
                className="group text-center border-white/10 hover:border-consciousness/40 transition-all duration-[2000ms] hover:scale-105 rounded-[20px] p-5 backdrop-blur-xl cursor-pointer"
                onClick={() => window.location.href = '/the-imperative'}
              >
                <Target className="w-10 h-10 mx-auto mb-3 text-consciousness group-hover:scale-110 transition-transform duration-[1500ms]" />
                <Typography variant="lg" weight="bold" color="consciousness" className="mb-2 font-['Inter',_'SF_Pro_Display',_'Helvetica_Neue',_sans-serif]">
                  The Imperative
                </Typography>
                <Typography variant="sm" color="secondary">
                  Why transformation is needed now
                </Typography>
              </Card>

              <Card 
                variant="creativity" 
                className="group text-center border-white/10 hover:border-creativity/40 transition-all duration-[2000ms] hover:scale-105 rounded-[20px] p-5 backdrop-blur-xl cursor-pointer"
                onClick={() => window.location.href = '/aci-technology'}
              >
                <Brain className="w-10 h-10 mx-auto mb-3 text-creativity group-hover:scale-110 transition-transform duration-[1500ms]" />
                <Typography variant="lg" weight="bold" color="creativity" className="mb-2 font-['Inter',_'SF_Pro_Display',_'Helvetica_Neue',_sans-serif]">
                  ACI Technology
                </Typography>
                <Typography variant="sm" color="secondary">
                  The evolution beyond AI
                </Typography>
              </Card>

              <Card 
                variant="intuition" 
                className="group text-center border-white/10 hover:border-harmony/40 transition-all duration-[2000ms] hover:scale-105 rounded-[20px] p-5 backdrop-blur-xl cursor-pointer"
                onClick={() => window.location.href = '/ecosystem-flywheel'}
              >
                <Network className="w-10 h-10 mx-auto mb-3 text-harmony group-hover:scale-110 transition-transform duration-[1500ms]" />
                <Typography variant="lg" weight="bold" color="harmony" className="mb-2 font-['Inter',_'SF_Pro_Display',_'Helvetica_Neue',_sans-serif]">
                  Ecosystem Flywheel
                </Typography>
                <Typography variant="sm" color="secondary">
                  Synergistic value creation
                </Typography>
              </Card>

              <Card 
                variant="consciousness" 
                className="group text-center border-white/10 hover:border-consciousness/40 transition-all duration-[2000ms] hover:scale-105 rounded-[20px] p-5 backdrop-blur-xl cursor-pointer"
                onClick={() => window.location.href = '/symbioautomate'}
              >
                <Zap className="w-10 h-10 mx-auto mb-3 text-consciousness group-hover:scale-110 transition-transform duration-[1500ms]" />
                <Typography variant="lg" weight="bold" color="consciousness" className="mb-2 font-['Inter',_'SF_Pro_Display',_'Helvetica_Neue',_sans-serif]">
                  SymbioAutomate
                </Typography>
                <Typography variant="sm" color="secondary">
                  Live automation platform
                </Typography>
              </Card>
            </div>
          </div>

          {/* Call to Action */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center" style={{ animationDelay: '9s' }}>
            <Button 
              variant="quantum" 
              size="lg"
              rightIcon={<ArrowRight className="w-4 h-4" />}
              className="group relative overflow-hidden px-6 py-3 text-base rounded-[20px] border border-white/20 hover:scale-110 transition-all duration-[2000ms] backdrop-blur-xl"
              onClick={() => window.location.href = '/ecosystem'}
            >
              <span className="relative z-10 tracking-wide font-medium">Explore the Ecosystem</span>
              <div className="absolute inset-0 bg-gradient-to-r from-[#2f00ff44] to-[#ff00cc33] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-[2000ms] origin-left" />
            </Button>
            
            <Button 
              variant="outline-quantum" 
              size="lg"
              className="group px-6 py-3 text-base rounded-[20px] border border-white/20 hover:scale-110 transition-all duration-[2000ms] backdrop-blur-xl"
              onClick={() => window.location.href = '/vision'}
            >
              <span className="group-hover:text-consciousness transition-colors duration-[1500ms] tracking-wide font-medium">Experience the Vision</span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;