
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface QuantumState {
  id: string;
  name: string;
  color: string;
  pattern: 'wave' | 'spiral' | 'grid' | 'organic';
  energy: number;
}

interface QuantumStateTransitionProps {
  states: QuantumState[];
  currentState: string;
  isActive: boolean;
  transitionDuration?: number;
  className?: string;
}

const QuantumStateTransition: React.FC<QuantumStateTransitionProps> = ({
  states,
  currentState,
  isActive,
  transitionDuration = 2000,
  className
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [transitioning, setTransitioning] = useState(false);
  const [transitionProgress, setTransitionProgress] = useState(0);
  const previousStateRef = useRef<string>(currentState);

  useEffect(() => {
    if (previousStateRef.current !== currentState) {
      setTransitioning(true);
      setTransitionProgress(0);
      
      const startTime = Date.now();
      const transitionTimer = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / transitionDuration, 1);
        setTransitionProgress(progress);
        
        if (progress >= 1) {
          setTransitioning(false);
          clearInterval(transitionTimer);
        }
      }, 16);
      
      previousStateRef.current = currentState;
      
      return () => clearInterval(transitionTimer);
    }
  }, [currentState, transitionDuration]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * 2;
      canvas.height = rect.height * 2;
      ctx.scale(2, 2);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    let time = 0;
    const centerX = canvas.width / 4;
    const centerY = canvas.height / 4;

    const currentStateData = states.find(s => s.id === currentState);
    const previousStateData = states.find(s => s.id === previousStateRef.current);

    const animate = () => {
      if (!isActive) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      ctx.clearRect(0, 0, canvas.width / 2, canvas.height / 2);
      
      time += 0.016;

      if (transitioning && previousStateData && currentStateData) {
        // Render transition between states
        renderTransition(ctx, previousStateData, currentStateData, transitionProgress, time, centerX, centerY);
      } else if (currentStateData) {
        // Render current state
        renderState(ctx, currentStateData, time, centerX, centerY);
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    const renderState = (ctx: CanvasRenderingContext2D, state: QuantumState, time: number, centerX: number, centerY: number) => {
      const radius = 100 + Math.sin(time * state.energy) * 20;
      
      switch (state.pattern) {
        case 'wave':
          renderWavePattern(ctx, state.color, time, centerX, centerY, radius);
          break;
        case 'spiral':
          renderSpiralPattern(ctx, state.color, time, centerX, centerY, radius);
          break;
        case 'grid':
          renderGridPattern(ctx, state.color, time, centerX, centerY, radius);
          break;
        case 'organic':
          renderOrganicPattern(ctx, state.color, time, centerX, centerY, radius);
          break;
      }
    };

    const renderTransition = (ctx: CanvasRenderingContext2D, fromState: QuantumState, toState: QuantumState, progress: number, time: number, centerX: number, centerY: number) => {
      // Create quantum tunneling effect
      const morphProgress = easeInOutCubic(progress);
      
      // Render both states with blending
      ctx.globalAlpha = 1 - morphProgress;
      renderState(ctx, fromState, time, centerX, centerY);
      
      ctx.globalAlpha = morphProgress;
      renderState(ctx, toState, time, centerX, centerY);
      
      // Quantum interference pattern
      ctx.globalAlpha = Math.sin(progress * Math.PI) * 0.5;
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.lineWidth = 2;
      ctx.shadowBlur = 20;
      ctx.shadowColor = 'rgba(255, 255, 255, 0.5)';
      
      for (let i = 0; i < 8; i++) {
        const angle = (i / 8) * Math.PI * 2;
        const waveRadius = 120 + Math.sin(time * 3 + progress * Math.PI * 4) * 30;
        const x = centerX + Math.cos(angle) * waveRadius;
        const y = centerY + Math.sin(angle) * waveRadius;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.stroke();
      }
      
      ctx.globalAlpha = 1;
      ctx.shadowBlur = 0;
    };

    const renderWavePattern = (ctx: CanvasRenderingContext2D, color: string, time: number, centerX: number, centerY: number, radius: number) => {
      ctx.strokeStyle = color;
      ctx.lineWidth = 3;
      ctx.shadowBlur = 10;
      ctx.shadowColor = color;
      
      for (let ring = 0; ring < 5; ring++) {
        ctx.beginPath();
        for (let angle = 0; angle <= Math.PI * 2; angle += 0.1) {
          const waveRadius = radius + ring * 20 + Math.sin(angle * 4 + time * 2) * 15;
          const x = centerX + Math.cos(angle) * waveRadius;
          const y = centerY + Math.sin(angle) * waveRadius;
          
          if (angle === 0) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }
        ctx.closePath();
        ctx.stroke();
      }
    };

    const renderSpiralPattern = (ctx: CanvasRenderingContext2D, color: string, time: number, centerX: number, centerY: number, radius: number) => {
      ctx.strokeStyle = color;
      ctx.lineWidth = 2;
      ctx.shadowBlur = 8;
      ctx.shadowColor = color;
      
      ctx.beginPath();
      for (let angle = 0; angle <= Math.PI * 8; angle += 0.1) {
        const spiralRadius = (angle / (Math.PI * 8)) * radius + Math.sin(time * 2) * 20;
        const x = centerX + Math.cos(angle + time) * spiralRadius;
        const y = centerY + Math.sin(angle + time) * spiralRadius;
        
        if (angle === 0) ctx.moveTo(x, y);
        else ctx.lineTo(x, y);
      }
      ctx.stroke();
    };

    const renderGridPattern = (ctx: CanvasRenderingContext2D, color: string, time: number, centerX: number, centerY: number, radius: number) => {
      ctx.strokeStyle = color;
      ctx.lineWidth = 1;
      ctx.shadowBlur = 6;
      ctx.shadowColor = color;
      
      const gridSize = 30;
      const oscillation = Math.sin(time * 2) * 10;
      
      for (let x = -radius; x <= radius; x += gridSize) {
        for (let y = -radius; y <= radius; y += gridSize) {
          const distance = Math.sqrt(x * x + y * y);
          if (distance <= radius) {
            const nodeX = centerX + x + oscillation;
            const nodeY = centerY + y + oscillation;
            
            ctx.beginPath();
            ctx.arc(nodeX, nodeY, 2, 0, Math.PI * 2);
            ctx.stroke();
            
            // Connect nearby nodes
            if (x + gridSize <= radius) {
              ctx.beginPath();
              ctx.moveTo(nodeX, nodeY);
              ctx.lineTo(nodeX + gridSize, nodeY);
              ctx.stroke();
            }
            if (y + gridSize <= radius) {
              ctx.beginPath();
              ctx.moveTo(nodeX, nodeY);
              ctx.lineTo(nodeX, nodeY + gridSize);
              ctx.stroke();
            }
          }
        }
      }
    };

    const renderOrganicPattern = (ctx: CanvasRenderingContext2D, color: string, time: number, centerX: number, centerY: number, radius: number) => {
      ctx.fillStyle = color;
      ctx.shadowBlur = 15;
      ctx.shadowColor = color;
      
      for (let blob = 0; blob < 8; blob++) {
        const angle = (blob / 8) * Math.PI * 2 + time * 0.5;
        const blobRadius = radius * 0.3 + Math.sin(time * 3 + blob) * 20;
        const x = centerX + Math.cos(angle) * (radius * 0.6);
        const y = centerY + Math.sin(angle) * (radius * 0.6);
        
        ctx.beginPath();
        ctx.arc(x, y, blobRadius, 0, Math.PI * 2);
        ctx.fill();
      }
    };

    const easeInOutCubic = (t: number): number => {
      return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [states, currentState, isActive, transitioning, transitionProgress]);

  return (
    <div className={cn("relative w-full h-full overflow-hidden", className)}>
      <canvas ref={canvasRef} className="w-full h-full" />
      
      {/* State indicator */}
      <div className="absolute top-4 left-4 glass-quantum rounded-[12px] px-3 py-2 border border-consciousness/25">
        <div className="text-xs text-consciousness font-mono">
          {states.find(s => s.id === currentState)?.name || 'Unknown State'}
        </div>
        {transitioning && (
          <div className="w-16 h-1 bg-consciousness/20 rounded-full mt-1 overflow-hidden">
            <div 
              className="h-full bg-consciousness transition-all duration-75"
              style={{ width: `${transitionProgress * 100}%` }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default QuantumStateTransition;
