import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Button from '../components/atoms/Button';
import Card from '../components/atoms/Card';
import { ArrowRight, Factory, Truck, Leaf, Zap, Building, Microscope, Shield, TrendingUp } from 'lucide-react';

const UseCases: React.FC = () => {
  const useCases = [
    {
      category: "Manufacturing",
      icon: <Factory className="w-8 h-8" />,
      color: "consciousness",
      cases: [
        {
          title: "Predictive Quality Control",
          description: "AI-powered vision systems that detect defects before they occur, reducing waste by 92%",
          roi: "92% defect reduction",
          timeline: "2-4 weeks implementation",
          features: ["Real-time quality monitoring", "Predictive defect analysis", "Automated corrective actions"]
        },
        {
          title: "Dynamic Production Optimization",
          description: "Self-adjusting production lines that adapt to demand fluctuations and material variations",
          roi: "34% efficiency increase",
          timeline: "4-6 weeks implementation",
          features: ["Demand-responsive scheduling", "Material optimization", "Energy consumption reduction"]
        }
      ]
    },
    {
      category: "Logistics & Supply Chain",
      icon: <Truck className="w-8 h-8" />,
      color: "creativity",
      cases: [
        {
          title: "Intelligent Route Optimization",
          description: "Dynamic routing that adapts to traffic, weather, and real-time delivery requirements",
          roi: "28% fuel savings",
          timeline: "1-2 weeks implementation",
          features: ["Real-time route adjustment", "Multi-modal optimization", "Carbon footprint tracking"]
        },
        {
          title: "Predictive Inventory Management",
          description: "AI-driven demand forecasting that prevents stockouts while minimizing carrying costs",
          roi: "45% inventory reduction",
          timeline: "3-5 weeks implementation",
          features: ["Demand pattern recognition", "Automated reordering", "Supplier integration"]
        }
      ]
    },
    {
      category: "Energy & Utilities",
      icon: <Zap className="w-8 h-8" />,
      color: "harmony",
      cases: [
        {
          title: "Smart Grid Optimization",
          description: "Intelligent energy distribution that balances supply and demand in real-time",
          roi: "23% energy savings",
          timeline: "6-8 weeks implementation",
          features: ["Load balancing", "Renewable integration", "Peak demand management"]
        },
        {
          title: "Predictive Maintenance",
          description: "AI-powered equipment monitoring that prevents failures before they occur",
          roi: "67% downtime reduction",
          timeline: "4-6 weeks implementation",
          features: ["Equipment health monitoring", "Failure prediction", "Maintenance scheduling"]
        }
      ]
    },
    {
      category: "Agriculture",
      icon: <Leaf className="w-8 h-8" />,
      color: "consciousness",
      cases: [
        {
          title: "Precision Farming",
          description: "IoT sensors and AI analytics for optimal crop management and resource utilization",
          roi: "35% yield increase",
          timeline: "2-3 weeks implementation",
          features: ["Soil monitoring", "Irrigation optimization", "Pest detection"]
        },
        {
          title: "Supply Chain Traceability",
          description: "End-to-end tracking from farm to table with quality assurance",
          roi: "89% traceability improvement",
          timeline: "3-4 weeks implementation",
          features: ["Blockchain tracking", "Quality verification", "Compliance monitoring"]
        }
      ]
    }
  ];

  const benefits = [
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Proven ROI",
      description: "Average 40% efficiency improvement across all implementations"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Enterprise Security",
      description: "Bank-grade encryption with quantum-resistant protocols"
    },
    {
      icon: <Microscope className="w-6 h-6" />,
      title: "Continuous Learning",
      description: "AI that evolves and improves with your business"
    },
    {
      icon: <Building className="w-6 h-6" />,
      title: "Scalable Architecture",
      description: "From pilot projects to enterprise-wide deployment"
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-32 pb-20">
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-6 text-center">
            <Typography variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Real-World Use Cases
            </Typography>
            
            <Typography variant="xl" color="secondary" className="mb-12 max-w-4xl mx-auto">
              See how industry leaders are transforming their operations with SymbioAutomate's 
              intelligent automation solutions across diverse sectors.
            </Typography>

            <div className="grid md:grid-cols-4 gap-6 mb-16">
              {benefits.map((benefit, index) => (
                <Card key={benefit.title} variant="neural" className="p-6 text-center border-consciousness/20">
                  <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-consciousness/15 flex items-center justify-center">
                    {benefit.icon}
                  </div>
                  <Typography variant="sm" weight="bold" color="consciousness" className="mb-2">
                    {benefit.title}
                  </Typography>
                  <Typography variant="xs" color="secondary">
                    {benefit.description}
                  </Typography>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Use Cases by Industry */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="space-y-20">
              {useCases.map((industry, index) => (
                <div key={industry.category} className="max-w-7xl mx-auto">
                  <div className="text-center mb-12">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-${industry.color}/15 flex items-center justify-center border border-${industry.color}/30`}>
                      {industry.icon}
                    </div>
                    <Typography variant="2xl" weight="bold" color={industry.color as any} className="mb-4">
                      {industry.category}
                    </Typography>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-8">
                    {industry.cases.map((useCase, caseIndex) => (
                      <Card key={useCase.title} variant="quantum" className={`p-8 border-${industry.color}/20 hover:border-${industry.color}/40 transition-all duration-500 rounded-[24px]`}>
                        <Typography variant="xl" weight="bold" color={industry.color as any} className="mb-4">
                          {useCase.title}
                        </Typography>
                        
                        <Typography variant="sm" color="secondary" className="mb-6 leading-relaxed">
                          {useCase.description}
                        </Typography>

                        <div className="grid md:grid-cols-2 gap-4 mb-6">
                          <div className={`p-4 bg-${industry.color}/10 rounded-[16px] border border-${industry.color}/20`}>
                            <Typography variant="xs" color="tertiary" className="mb-1">
                              ROI Impact
                            </Typography>
                            <Typography variant="sm" weight="bold" color={industry.color as any}>
                              {useCase.roi}
                            </Typography>
                          </div>
                          
                          <div className={`p-4 bg-${industry.color}/10 rounded-[16px] border border-${industry.color}/20`}>
                            <Typography variant="xs" color="tertiary" className="mb-1">
                              Implementation
                            </Typography>
                            <Typography variant="sm" weight="bold" color={industry.color as any}>
                              {useCase.timeline}
                            </Typography>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Typography variant="sm" weight="semibold" color={industry.color as any} className="mb-3">
                            Key Features:
                          </Typography>
                          {useCase.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center space-x-3">
                              <div className={`w-2 h-2 rounded-full bg-${industry.color}`} />
                              <Typography variant="xs" color="secondary">
                                {feature}
                              </Typography>
                            </div>
                          ))}
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-br from-consciousness/10 via-abyssal-base to-harmony/10">
          <div className="container mx-auto px-6 text-center">
            <Typography variant="3xl" weight="bold" gradient="consciousness" className="mb-6">
              Ready to Transform Your Industry?
            </Typography>
            
            <Typography variant="lg" color="secondary" className="mb-12 max-w-3xl mx-auto">
              Join the companies already experiencing breakthrough results with SymbioAutomate. 
              Let's discuss how we can optimize your specific use case.
            </Typography>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="rounded-[20px]"
                onClick={() => window.location.href = '/request-demo'}
              >
                Schedule Custom Demo
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="rounded-[20px]"
                onClick={() => window.location.href = '/ai-readiness-audit'}
              >
                Start AI Readiness Assessment
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="rounded-[20px]"
                onClick={() => window.location.href = '/symbioautomate'}
              >
                Back to SymbioAutomate
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default UseCases;
