
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import SymbioticImperative from '../components/features/SymbioticImperative';
import { AlertTriangle, TrendingDown, Zap, ArrowRight, Shield, Target } from 'lucide-react';

const TheImperative: React.FC = () => {
  const crisisPoints = [
    {
      icon: AlertTriangle,
      title: "The Innovation Bottleneck",
      description: "Traditional AI systems are hitting fundamental limitations - rigid, centralized, and unable to adapt to complex real-world dynamics.",
      urgency: "Critical"
    },
    {
      icon: TrendingDown,
      title: "Economic Inefficiency",
      description: "Organizations lose $3.1 trillion annually to process inefficiencies that current automation can't address.",
      urgency: "Immediate"
    },
    {
      icon: Shield,
      title: "Systemic Fragility",
      description: "Centralized AI systems create single points of failure, making entire ecosystems vulnerable to cascading failures.",
      urgency: "Escalating"
    }
  ];

  const solutions = [
    {
      icon: Zap,
      title: "Bio-Inspired Intelligence",
      description: "Nature has perfected intelligent systems over billions of years. We're finally ready to learn from cellular intelligence patterns.",
      benefit: "Adaptive resilience"
    },
    {
      icon: Target,
      title: "Decentralized Processing",
      description: "Distributed intelligence that can't be shut down, corrupted, or controlled by any single entity.",
      benefit: "Systemic stability"
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="pt-32 pb-20 px-6">
          <div className="container mx-auto text-center">
            <Typography 
              as="h1" 
              variant="4xl" 
              weight="bold" 
              gradient="consciousness"
              className="mb-6"
            >
              The Symbiotic Imperative
            </Typography>
            <Typography 
              variant="xl" 
              color="secondary"
              className="max-w-3xl mx-auto mb-8"
            >
              We stand at a critical inflection point. The next phase of intelligence requires 
              a fundamental shift from artificial to symbiotic systems.
            </Typography>
            <Typography 
              variant="lg" 
              color="consciousness"
              className="max-w-2xl mx-auto"
            >
              Current AI architectures are reaching their limits. The future belongs to 
              bio-inspired, decentralized intelligence that works with nature, not against it.
            </Typography>
          </div>
        </section>

        {/* Crisis Points */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                The Current Crisis
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Why the status quo is no longer sustainable and action is required now
              </Typography>
            </div>

            <div className="grid lg:grid-cols-3 gap-8 mb-16">
              {crisisPoints.map((crisis, index) => {
                const Icon = crisis.icon;
                return (
                  <Card key={index} variant="neural" className="p-6 border-red-500/20 hover:border-red-500/40">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 rounded-biomorphic bg-red-500/20 flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-red-400" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <Typography 
                            as="h3" 
                            variant="lg" 
                            weight="semibold" 
                            color="consciousness"
                          >
                            {crisis.title}
                          </Typography>
                          <span className="px-2 py-1 bg-red-500/20 border border-red-500/30 rounded-full text-xs text-red-400">
                            {crisis.urgency}
                          </span>
                        </div>
                        <Typography 
                          variant="sm" 
                          color="secondary"
                          className="leading-relaxed"
                        >
                          {crisis.description}
                        </Typography>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            <div className="text-center">
              <Card variant="quantum" className="p-8 inline-block max-w-2xl">
                <Typography 
                  as="h3" 
                  variant="xl" 
                  weight="semibold" 
                  color="consciousness"
                  className="mb-4"
                >
                  The Cost of Inaction
                </Typography>
                <Typography 
                  variant="sm" 
                  color="secondary"
                  className="mb-4"
                >
                  Every day we delay the transition to symbiotic intelligence systems, 
                  organizations lose competitive advantage and society misses opportunities 
                  for breakthrough solutions to global challenges.
                </Typography>
                <Typography 
                  variant="xs" 
                  color="tertiary"
                  className="italic"
                >
                  "The best time to plant a tree was 20 years ago. The second best time is now." 
                  - The same principle applies to intelligent systems.
                </Typography>
              </Card>
            </div>
          </div>
        </section>

        {/* Interactive Visualization */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <SymbioticImperative />
          </div>
        </section>

        {/* The Solution */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                gradient="neural"
                className="mb-4"
              >
                The SymbioWave Solution
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                A new paradigm that transcends the limitations of current AI architectures
              </Typography>
            </div>

            <div className="grid lg:grid-cols-2 gap-8 mb-16">
              {solutions.map((solution, index) => {
                const Icon = solution.icon;
                return (
                  <Card key={index} variant="quantum" className="p-8">
                    <div className="flex items-start space-x-4">
                      <div className="w-16 h-16 rounded-cellular bg-consciousness/20 flex items-center justify-center flex-shrink-0">
                        <Icon className="w-8 h-8 text-consciousness" />
                      </div>
                      <div className="flex-1">
                        <Typography 
                          as="h3" 
                          variant="xl" 
                          weight="semibold" 
                          color="consciousness"
                          className="mb-3"
                        >
                          {solution.title}
                        </Typography>
                        <Typography 
                          variant="sm" 
                          color="secondary"
                          className="mb-4 leading-relaxed"
                        >
                          {solution.description}
                        </Typography>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 rounded-full bg-green-400"></div>
                          <Typography 
                            variant="xs" 
                            color="creativity"
                            weight="medium"
                          >
                            Key Benefit: {solution.benefit}
                          </Typography>
                        </div>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>

            <div className="text-center">
              <Typography 
                as="h3" 
                variant="2xl" 
                weight="bold" 
                color="consciousness"
                className="mb-6"
              >
                The Time for Symbiotic Intelligence is Now
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-3xl mx-auto mb-8"
              >
                Every breakthrough in biology, quantum mechanics, and distributed systems 
                has prepared us for this moment. The convergence is here.
              </Typography>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  variant="quantum" 
                  size="lg"
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                >
                  Join the Evolution
                </Button>
                <Button 
                  variant="outline-quantum" 
                  size="lg"
                >
                  Explore the Technology
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default TheImperative;
