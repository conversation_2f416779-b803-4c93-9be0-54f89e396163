
import React, { useState } from 'react';
import { 
  Brain, 
  Zap, 
  Network, 
  Shield, 
  Cpu, 
  Target,
  GitBranch,
  Activity 
} from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import Button from '../atoms/Button';

const ACITechnology: React.FC = () => {
  const [activeModel, setActiveModel] = useState(0);

  const characteristics = [
    {
      icon: Network,
      title: "Decentralized Intelligence",
      description: "Like cells in an organism, ACI agents operate with local autonomy while contributing to emergent system-wide intelligence.",
      color: "consciousness"
    },
    {
      icon: Zap,
      title: "Self-Optimization", 
      description: "Continuously adapt parameters and connections based on live data streams, learning without human intervention.",
      color: "creativity"
    },
    {
      icon: Brain,
      title: "Emergent Behavior",
      description: "Complex strategies arise from simple interaction rules, solving problems in ways not explicitly programmed.",
      color: "intuition"
    },
    {
      icon: Cpu,
      title: "Radical Scalability",
      description: "Lightweight, decentralized nature scales from IoT sensors to global supply chains with unparalleled efficiency.",
      color: "harmony"
    }
  ];

  const aciModels = [
    {
      name: "Physarum-Inspired Network Optimizer",
      description: "Mimics slime mold intelligence to design the most efficient and resilient logistics networks, data routes, and energy grids.",
      use_cases: ["Global supply chain optimization", "Smart city traffic management", "Energy grid distribution"],
      color: "consciousness"
    },
    {
      name: "Quorum-Sensing Consensus Engine", 
      description: "Uses bacterial communication principles for decentralized agreement among autonomous agents.",
      use_cases: ["Drone fleet coordination", "Smart warehouse management", "Distributed energy resources"],
      color: "creativity"
    },
    {
      name: "Immune-Inspired Anomaly Detector",
      description: "Functions like a biological immune system to identify and neutralize threats in real-time.",
      use_cases: ["Cybersecurity defense", "Financial fraud detection", "Predictive maintenance"],
      color: "intuition"
    },
    {
      name: "Ant Colony-Inspired Optimizer",
      description: "Solves complex combinatorial problems by simulating emergent ant foraging intelligence.",
      use_cases: ["Dynamic job scheduling", "Route optimization", "Resource allocation"],
      color: "harmony"
    },
    {
      name: "Stem Cell-Inspired Role Allocator",
      description: "Dynamically assigns tasks and resources based on real-time needs, mirroring cellular differentiation.",
      use_cases: ["Adaptive workforce management", "Dynamic pricing", "Capacity planning"],
      color: "transcendence"
    },
    {
      name: "Cancer-Inspired Explorer",
      description: "Leverages aggressive adaptation strategies to identify and exploit new opportunities.",
      use_cases: ["Market opportunity discovery", "Competitive landscape navigation", "Innovation scouting"],
      color: "consciousness"
    }
  ];

  return (
    <section id="aci" className="py-32 relative">
      {/* Quantum Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/3 left-1/5 w-40 h-40 bg-consciousness/8 rounded-cellular blur-2xl animate-consciousness-wave"></div>
        <div className="absolute bottom-1/3 right-1/5 w-32 h-32 bg-creativity/10 rounded-biomorphic blur-xl animate-neural-pulse"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Typography 
            as="h2" 
            variant="4xl" 
            weight="bold" 
            gradient="holographic"
            align="center"
            className="mb-6"
          >
            Artificial Cellular Intelligence
          </Typography>
          
          <Typography 
            as="p" 
            variant="xl" 
            color="secondary" 
            align="center"
            className="mb-6"
          >
            Beyond Conventional AI: A New Paradigm
          </Typography>

          <Typography 
            as="p" 
            variant="lg" 
            color="tertiary" 
            align="center"
            className="max-w-4xl mx-auto leading-relaxed"
          >
            To power the symbiotic paradigm, a new form of intelligence is required. SymbioWave's 
            foundational asset is its proprietary Artificial Cellular Intelligence (ACI).
          </Typography>
        </div>

        {/* ACI vs Traditional AI Comparison */}
        <Card variant="quantum" className="mb-20 overflow-hidden border-consciousness/20">
          <div className="grid lg:grid-cols-2 gap-8">
            <div className="p-8">
              <Typography variant="xl" weight="bold" color="creativity" className="mb-6">
                Traditional AI
              </Typography>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Centralized, monolithic, pre-trained models</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Static learning requiring costly retraining</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Brittle, vulnerable to novel data</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-creativity/60 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="tertiary">Extremely high computational cost</Typography>
                </div>
              </div>
            </div>

            <div className="p-8 bg-consciousness/5 rounded-r-lg">
              <Typography variant="xl" weight="bold" color="consciousness" className="mb-6">
                Artificial Cellular Intelligence
              </Typography>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Decentralized ecosystem of autonomous cells</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Dynamic evolution, continuous real-time adaptation</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Anti-fragile, self-healing and robust</Typography>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-consciousness/80 rounded-full mt-2 flex-shrink-0"></div>
                  <Typography variant="sm" color="secondary">Lightweight, edge-to-cloud scalability</Typography>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Key Characteristics */}
        <div className="mb-20">
          <Typography 
            as="h3" 
            variant="2xl" 
            weight="bold" 
            color="consciousness"
            align="center"
            className="mb-12"
          >
            Key Characteristics of ACI
          </Typography>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {characteristics.map((char, index) => {
              const IconComponent = char.icon;
              return (
                <Card 
                  key={char.title}
                  variant="neural"
                  className={`group text-center hover:border-${char.color}/50 transition-all duration-bio hover:transform hover:scale-105`}
                >
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-cellular bg-${char.color}/10 flex items-center justify-center group-hover:animate-cellular-flow`}>
                    <IconComponent className={`w-8 h-8 text-${char.color}`} />
                  </div>
                  <Typography variant="lg" weight="semibold" className={`mb-3 text-${char.color}`}>
                    {char.title}
                  </Typography>
                  <Typography variant="xs" color="secondary" className="leading-relaxed">
                    {char.description}
                  </Typography>
                </Card>
              );
            })}
          </div>
        </div>

        {/* ACI Model Suite */}
        <div>
          <Typography 
            as="h3" 
            variant="2xl" 
            weight="bold" 
            gradient="neural"
            align="center"
            className="mb-12"
          >
            The ACI Model Suite: Nature's Algorithms for Business
          </Typography>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Model Navigation */}
            <div className="lg:col-span-1">
              <Card variant="neural" className="p-6">
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                  Select ACI Model
                </Typography>
                <div className="space-y-2">
                  {aciModels.map((model, index) => (
                    <button
                      key={model.name}
                      onClick={() => setActiveModel(index)}
                      className={`w-full text-left p-3 rounded-lg transition-all duration-quantum ${
                        activeModel === index 
                          ? `bg-${model.color}/20 border border-${model.color}/40` 
                          : 'hover:bg-white/5'
                      }`}
                    >
                      <Typography 
                        variant="sm" 
                        weight="medium" 
                        className={activeModel === index ? `text-${model.color}` : 'text-secondary'}
                      >
                        {model.name}
                      </Typography>
                    </button>
                  ))}
                </div>
              </Card>
            </div>

            {/* Active Model Details */}
            <div className="lg:col-span-2">
              <Card variant="quantum" className={`border-${aciModels[activeModel].color}/30`}>
                <Typography 
                  variant="xl" 
                  weight="bold" 
                  className={`mb-4 text-${aciModels[activeModel].color}`}
                >
                  {aciModels[activeModel].name}
                </Typography>

                <Typography 
                  variant="lg" 
                  color="secondary" 
                  className="mb-6 leading-relaxed"
                >
                  {aciModels[activeModel].description}
                </Typography>

                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                  Use Cases:
                </Typography>

                <div className="grid md:grid-cols-1 gap-3 mb-8">
                  {aciModels[activeModel].use_cases.map((useCase, index) => (
                    <div key={useCase} className="flex items-center space-x-3">
                      <div className={`w-2 h-2 bg-${aciModels[activeModel].color}/70 rounded-full flex-shrink-0`}></div>
                      <Typography variant="sm" color="secondary">{useCase}</Typography>
                    </div>
                  ))}
                </div>

                <Button 
                  variant="outline-quantum" 
                  size="sm"
                  className="group"
                >
                  <span className="group-hover:text-consciousness transition-colors duration-quantum">
                    Learn More About This Model
                  </span>
                </Button>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ACITechnology;
