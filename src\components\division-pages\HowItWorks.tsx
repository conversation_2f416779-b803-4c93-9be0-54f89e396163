
import React from 'react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import { <PERSON>R<PERSON>, Brain, Zap, RefreshCw } from 'lucide-react';

const HowItWorks: React.FC = () => {
  const steps = [
    {
      icon: Brain,
      title: "ACI Analysis",
      description: "Our Artificial Cellular Intelligence analyzes your operational data to identify optimization opportunities and symbiotic connections.",
      color: "consciousness"
    },
    {
      icon: Zap,
      title: "Dynamic Workflows",
      description: "Create adaptive automation workflows that evolve with changing conditions rather than breaking when parameters shift.",
      color: "harmony"
    },
    {
      icon: RefreshCw,
      title: "Continuous Optimization",
      description: "The system continuously learns and improves, automatically adjusting processes for maximum efficiency and resource utilization.",
      color: "creativity"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            gradient="consciousness"
            className="mb-4"
          >
            How SymbioAutomate Works
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            Beyond rigid automation - intelligent, adaptive workflows powered by ACI
          </Typography>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {steps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="relative">
                <Card variant="neural" className="border-harmony/30 p-8 text-center h-full">
                  <div className={`w-16 h-16 rounded-cellular bg-${step.color}/20 flex items-center justify-center mx-auto mb-6`}>
                    <Icon className={`w-8 h-8 text-${step.color}`} />
                  </div>
                  
                  <Typography 
                    as="h3" 
                    variant="xl" 
                    weight="semibold" 
                    color={step.color as any}
                    className="mb-4"
                  >
                    {step.title}
                  </Typography>
                  
                  <Typography 
                    variant="sm" 
                    color="secondary"
                    className="leading-relaxed"
                  >
                    {step.description}
                  </Typography>
                </Card>

                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                    <ArrowRight className="w-6 h-6 text-harmony/50" />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
