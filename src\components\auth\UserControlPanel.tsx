import React, { useState } from 'react';
import { 
  User, 
  Settings, 
  BarChart3, 
  FileText, 
  Zap, 
  Database, 
  Shield, 
  LogOut,
  ChevronLeft,
  ChevronRight,
  Activity,
  Briefcase,
  Calendar,
  Bell
} from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import Button from '../atoms/Button';

interface UserControlPanelProps {
  user: any;
  onLogout: () => void;
}

const UserControlPanel: React.FC<UserControlPanelProps> = ({ user, onLogout }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');

  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3, color: 'consciousness' },
    { id: 'projects', label: 'Projects', icon: Briefcase, color: 'creativity' },
    { id: 'analytics', label: 'Analytics', icon: Activity, color: 'harmony' },
    { id: 'workflows', label: 'Workflows', icon: Zap, color: 'consciousness' },
    { id: 'data', label: 'Data Hub', icon: Database, color: 'creativity' },
    { id: 'reports', label: 'Reports', icon: FileText, color: 'harmony' },
    { id: 'notifications', label: 'Notifications', icon: Bell, color: 'consciousness' },
    { id: 'settings', label: 'Settings', icon: Settings, color: 'creativity' }
  ];

  const renderDashboard = () => (
    <div className="space-y-6">
      <div>
        <Typography variant="lg" weight="bold" color="consciousness" className="mb-4">
          Welcome back, {user.fullName}
        </Typography>
        <Typography variant="sm" color="secondary">
          {user.companyName} • {user.accessLevel} Access
        </Typography>
      </div>

      <div className="grid grid-cols-1 gap-4">
        <Card variant="neural" className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="sm" weight="semibold" color="consciousness">
              Active Workflows
            </Typography>
            <div className="w-2 h-2 bg-harmony rounded-full animate-pulse" />
          </div>
          <Typography variant="2xl" weight="bold" color="harmony">
            12
          </Typography>
          <Typography variant="xs" color="tertiary">
            Running processes
          </Typography>
        </Card>

        <Card variant="neural" className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="sm" weight="semibold" color="consciousness">
              Data Processed
            </Typography>
            <Activity className="w-4 h-4 text-creativity" />
          </div>
          <Typography variant="2xl" weight="bold" color="creativity">
            2.4TB
          </Typography>
          <Typography variant="xs" color="tertiary">
            This month
          </Typography>
        </Card>

        <Card variant="neural" className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="sm" weight="semibold" color="consciousness">
              Efficiency Gain
            </Typography>
            <div className="w-2 h-2 bg-consciousness rounded-full animate-pulse" />
          </div>
          <Typography variant="2xl" weight="bold" color="consciousness">
            +34%
          </Typography>
          <Typography variant="xs" color="tertiary">
            vs last quarter
          </Typography>
        </Card>
      </div>

      <div>
        <Typography variant="sm" weight="semibold" color="consciousness" className="mb-3">
          Quick Actions
        </Typography>
        <div className="space-y-2">
          <Button variant="outline-quantum" size="sm" className="w-full justify-start">
            <Zap className="w-4 h-4 mr-2" />
            Create New Workflow
          </Button>
          <Button variant="outline-quantum" size="sm" className="w-full justify-start">
            <FileText className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
          <Button variant="outline-quantum" size="sm" className="w-full justify-start">
            <Database className="w-4 h-4 mr-2" />
            Access Data Hub
          </Button>
        </div>
      </div>
    </div>
  );

  const renderProjects = () => (
    <div className="space-y-4">
      <Typography variant="lg" weight="bold" color="consciousness">
        Active Projects
      </Typography>
      
      {[
        { name: 'Supply Chain Optimization', status: 'Active', progress: 78 },
        { name: 'Energy Efficiency Analysis', status: 'In Progress', progress: 45 },
        { name: 'Waste Stream Mapping', status: 'Planning', progress: 12 }
      ].map((project, index) => (
        <Card key={index} variant="neural" className="p-4">
          <div className="flex items-center justify-between mb-2">
            <Typography variant="sm" weight="semibold" color="consciousness">
              {project.name}
            </Typography>
            <span className={`px-2 py-1 text-xs rounded-full ${
              project.status === 'Active' ? 'bg-harmony/30 text-harmony' :
              project.status === 'In Progress' ? 'bg-creativity/30 text-creativity' :
              'bg-consciousness/30 text-consciousness'
            }`}>
              {project.status}
            </span>
          </div>
          <div className="w-full bg-abyssal-base rounded-full h-2 mb-2">
            <div 
              className="bg-consciousness h-2 rounded-full transition-all duration-500"
              style={{ width: `${project.progress}%` }}
            />
          </div>
          <Typography variant="xs" color="tertiary">
            {project.progress}% Complete
          </Typography>
        </Card>
      ))}
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'projects':
        return renderProjects();
      case 'analytics':
        return (
          <div className="space-y-4">
            <Typography variant="lg" weight="bold" color="consciousness">
              Analytics Overview
            </Typography>
            <Typography variant="sm" color="secondary">
              Detailed analytics and insights coming soon...
            </Typography>
          </div>
        );
      default:
        return (
          <div className="space-y-4">
            <Typography variant="lg" weight="bold" color="consciousness">
              {menuItems.find(item => item.id === activeTab)?.label}
            </Typography>
            <Typography variant="sm" color="secondary">
              This section is under development...
            </Typography>
          </div>
        );
    }
  };

  return (
    <div className={`fixed left-0 top-0 h-full z-[150000] transition-all duration-500 ${
      isExpanded ? 'w-80' : 'w-16'
    }`}>
      {/* Main Panel */}
      <div className="h-full bg-abyssal-base/95 backdrop-blur-xl border-r border-consciousness/30 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-consciousness/20">
          <div className="flex items-center justify-between">
            {isExpanded && (
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-full bg-consciousness/20 flex items-center justify-center">
                  <User className="w-4 h-4 text-consciousness" />
                </div>
                <div>
                  <Typography variant="sm" weight="semibold" color="consciousness">
                    {user.fullName}
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    {user.accessLevel}
                  </Typography>
                </div>
              </div>
            )}
            
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 rounded-full hover:bg-consciousness/20 transition-colors"
            >
              {isExpanded ? (
                <ChevronLeft className="w-4 h-4 text-consciousness" />
              ) : (
                <ChevronRight className="w-4 h-4 text-consciousness" />
              )}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 p-2 overflow-y-auto">
          <div className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeTab === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 p-3 rounded-[12px] transition-all duration-300 ${
                    isActive 
                      ? 'bg-consciousness/20 border border-consciousness/30' 
                      : 'hover:bg-consciousness/10 border border-transparent'
                  }`}
                >
                  <Icon className={`w-5 h-5 ${isActive ? 'text-consciousness' : 'text-secondary'}`} />
                  {isExpanded && (
                    <Typography 
                      variant="sm" 
                      weight={isActive ? 'semibold' : 'normal'}
                      color={isActive ? 'consciousness' : 'secondary'}
                    >
                      {item.label}
                    </Typography>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-consciousness/20">
          <button
            onClick={onLogout}
            className="w-full flex items-center space-x-3 p-3 rounded-[12px] hover:bg-red-500/20 transition-colors group"
          >
            <LogOut className="w-5 h-5 text-red-400 group-hover:text-red-300" />
            {isExpanded && (
              <Typography variant="sm" className="text-red-400 group-hover:text-red-300">
                Logout
              </Typography>
            )}
          </button>
        </div>
      </div>

      {/* Content Panel */}
      {isExpanded && (
        <div className="absolute left-full top-0 w-80 h-full bg-abyssal-base/90 backdrop-blur-xl border-r border-consciousness/20 p-6 overflow-y-auto">
          {renderContent()}
        </div>
      )}
    </div>
  );
};

export default UserControlPanel;
