import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Button from '../components/atoms/Button';
import Card from '../components/atoms/Card';
import { ArrowRight, Rocket, Brain, Target, Zap, TrendingUp, Users, Shield, CheckCircle } from 'lucide-react';

const AITransformation: React.FC = () => {
  const transformationPhases = [
    {
      phase: "Discovery",
      duration: "2-4 weeks",
      icon: <Target className="w-8 h-8" />,
      description: "Comprehensive assessment of your current state and AI readiness",
      activities: [
        "Business process analysis",
        "Technology infrastructure audit",
        "Team capability assessment",
        "ROI opportunity identification"
      ]
    },
    {
      phase: "Strategy",
      duration: "3-6 weeks",
      icon: <Brain className="w-8 h-8" />,
      description: "Develop a customized AI transformation roadmap aligned with your goals",
      activities: [
        "AI strategy development",
        "Technology architecture design",
        "Implementation timeline planning",
        "Change management strategy"
      ]
    },
    {
      phase: "Implementation",
      duration: "8-16 weeks",
      icon: <Zap className="w-8 h-8" />,
      description: "Deploy AI solutions with minimal disruption to your operations",
      activities: [
        "Pilot project execution",
        "System integration",
        "Team training and onboarding",
        "Performance monitoring setup"
      ]
    },
    {
      phase: "Optimization",
      duration: "Ongoing",
      icon: <TrendingUp className="w-8 h-8" />,
      description: "Continuous improvement and scaling of AI capabilities",
      activities: [
        "Performance optimization",
        "Feature enhancement",
        "Scale expansion",
        "Advanced analytics"
      ]
    }
  ];

  const transformationAreas = [
    {
      title: "Operations Excellence",
      description: "Streamline processes and eliminate inefficiencies with intelligent automation",
      benefits: [
        "40% reduction in operational costs",
        "75% faster process execution",
        "95% accuracy improvement",
        "24/7 automated operations"
      ],
      color: "consciousness"
    },
    {
      title: "Customer Experience",
      description: "Deliver personalized, responsive customer interactions at scale",
      benefits: [
        "60% improvement in satisfaction",
        "3x faster response times",
        "Personalized recommendations",
        "Predictive customer support"
      ],
      color: "creativity"
    },
    {
      title: "Data Intelligence",
      description: "Transform raw data into actionable insights and predictive capabilities",
      benefits: [
        "Real-time decision making",
        "Predictive analytics",
        "Automated reporting",
        "Risk mitigation"
      ],
      color: "harmony"
    },
    {
      title: "Innovation Acceleration",
      description: "Accelerate product development and market responsiveness",
      benefits: [
        "50% faster time-to-market",
        "Enhanced product quality",
        "Market trend prediction",
        "Competitive advantage"
      ],
      color: "consciousness"
    }
  ];

  const successMetrics = [
    {
      metric: "ROI",
      value: "300%+",
      description: "Average return on investment within 18 months"
    },
    {
      metric: "Efficiency",
      value: "65%",
      description: "Average improvement in operational efficiency"
    },
    {
      metric: "Time Savings",
      value: "80%",
      description: "Reduction in manual processing time"
    },
    {
      metric: "Accuracy",
      value: "99%",
      description: "Improvement in process accuracy and quality"
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-32 pb-20">
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-6 text-center">
            <div className="w-20 h-20 mx-auto mb-8 rounded-full bg-gradient-to-br from-consciousness/20 to-creativity/20 flex items-center justify-center border border-consciousness/30">
              <Rocket className="w-10 h-10 text-consciousness" />
            </div>
            
            <Typography variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Begin Your AI Transformation Journey
            </Typography>
            
            <Typography variant="xl" color="secondary" className="mb-12 max-w-4xl mx-auto">
              Embark on a comprehensive transformation that will revolutionize how your organization 
              operates, innovates, and competes in the digital age.
            </Typography>

            <div className="grid md:grid-cols-4 gap-6 mb-16">
              {successMetrics.map((metric, index) => (
                <Card key={metric.metric} variant="neural" className="p-6 text-center border-consciousness/20">
                  <Typography variant="3xl" weight="bold" color="consciousness" className="mb-2">
                    {metric.value}
                  </Typography>
                  <Typography variant="sm" weight="bold" color="consciousness" className="mb-1">
                    {metric.metric}
                  </Typography>
                  <Typography variant="xs" color="secondary">
                    {metric.description}
                  </Typography>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Transformation Phases */}
        <section className="py-20 bg-abyssal-base/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography variant="3xl" weight="bold" color="consciousness" className="mb-4">
                Your Transformation Roadmap
              </Typography>
              <Typography variant="lg" color="secondary" className="max-w-3xl mx-auto">
                A proven methodology that ensures successful AI adoption with measurable results at every stage
              </Typography>
            </div>

            <div className="max-w-6xl mx-auto">
              {transformationPhases.map((phase, index) => (
                <div key={phase.phase} className="flex items-start space-x-8 mb-12 last:mb-0">
                  <div className="flex flex-col items-center flex-shrink-0">
                    <div className="w-16 h-16 rounded-full bg-consciousness/20 flex items-center justify-center border border-consciousness/40 mb-4">
                      {phase.icon}
                    </div>
                    {index < transformationPhases.length - 1 && (
                      <div className="w-px h-24 bg-consciousness/30"></div>
                    )}
                  </div>
                  
                  <Card variant="quantum" className="flex-1 p-8 border-consciousness/20 rounded-[24px]">
                    <div className="flex items-center justify-between mb-4">
                      <Typography variant="xl" weight="bold" color="consciousness">
                        {phase.phase}
                      </Typography>
                      <span className="px-3 py-1 bg-harmony/20 text-harmony text-sm rounded-full border border-harmony/30">
                        {phase.duration}
                      </span>
                    </div>
                    
                    <Typography variant="sm" color="secondary" className="mb-6">
                      {phase.description}
                    </Typography>

                    <div className="grid md:grid-cols-2 gap-4">
                      {phase.activities.map((activity, activityIndex) => (
                        <div key={activityIndex} className="flex items-center space-x-3">
                          <CheckCircle className="w-4 h-4 text-harmony flex-shrink-0" />
                          <Typography variant="xs" color="tertiary">
                            {activity}
                          </Typography>
                        </div>
                      ))}
                    </div>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Transformation Areas */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography variant="3xl" weight="bold" color="consciousness" className="mb-4">
                Areas of Transformation
              </Typography>
              <Typography variant="lg" color="secondary" className="max-w-3xl mx-auto">
                Comprehensive AI integration across all aspects of your business
              </Typography>
            </div>

            <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {transformationAreas.map((area, index) => (
                <Card key={area.title} variant="quantum" className={`p-8 border-${area.color}/20 hover:border-${area.color}/40 transition-all duration-500 rounded-[24px]`}>
                  <Typography variant="xl" weight="bold" color={area.color as any} className="mb-4">
                    {area.title}
                  </Typography>
                  
                  <Typography variant="sm" color="secondary" className="mb-6">
                    {area.description}
                  </Typography>

                  <div className="space-y-3">
                    <Typography variant="sm" weight="semibold" color={area.color as any} className="mb-3">
                      Key Benefits:
                    </Typography>
                    {area.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full bg-${area.color}`} />
                        <Typography variant="sm" color="tertiary">
                          {benefit}
                        </Typography>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-br from-consciousness/10 via-abyssal-base to-harmony/10">
          <div className="container mx-auto px-6 text-center">
            <Typography variant="3xl" weight="bold" gradient="consciousness" className="mb-6">
              Start Your Transformation Today
            </Typography>
            
            <Typography variant="lg" color="secondary" className="mb-12 max-w-3xl mx-auto">
              Join the organizations already experiencing breakthrough results with AI transformation. 
              Let's discuss your unique transformation journey.
            </Typography>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<Rocket className="w-5 h-5" />}
                className="rounded-[20px]"
                onClick={() => window.location.href = '/request-demo'}
              >
                Begin Transformation Assessment
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="rounded-[20px]"
                onClick={() => window.location.href = '/ai-readiness-audit'}
              >
                Check Your AI Readiness
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="rounded-[20px]"
                onClick={() => window.location.href = '/symbioautomate'}
              >
                Explore SymbioAutomate
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default AITransformation;
