
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface DataPoint {
  value: number;
  label: string;
  color: string;
  category?: string;
}

interface DataSculptureProps {
  data: DataPoint[];
  variant?: 'bars' | 'spiral' | 'constellation' | 'wave';
  isActive: boolean;
  className?: string;
  height?: number;
}

const DataSculpture: React.FC<DataSculptureProps> = ({
  data,
  variant = 'constellation',
  isActive,
  className,
  height = 300
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * 2;
      canvas.height = rect.height * 2;
      ctx.scale(2, 2);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    let animationProgress = 0;
    const animate = () => {
      if (!isActive) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      ctx.clearRect(0, 0, canvas.width / 2, canvas.height / 2);
      
      animationProgress += 0.02;
      const currentProgress = Math.min(animationProgress, 1);
      setProgress(currentProgress);

      const centerX = canvas.width / 4;
      const centerY = canvas.height / 4;
      const maxRadius = Math.min(centerX, centerY) * 0.8;

      switch (variant) {
        case 'constellation':
          renderConstellation(ctx, data, currentProgress, centerX, centerY, maxRadius);
          break;
        case 'spiral':
          renderSpiral(ctx, data, currentProgress, centerX, centerY, maxRadius);
          break;
        case 'wave':
          renderWave(ctx, data, currentProgress, canvas.width / 2, canvas.height / 2);
          break;
        case 'bars':
          renderBars(ctx, data, currentProgress, canvas.width / 2, canvas.height / 2);
          break;
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    const renderConstellation = (ctx: CanvasRenderingContext2D, points: DataPoint[], progress: number, centerX: number, centerY: number, maxRadius: number) => {
      const maxValue = Math.max(...points.map(p => p.value));
      
      points.forEach((point, index) => {
        const angle = (index / points.length) * Math.PI * 2;
        const radius = (point.value / maxValue) * maxRadius * progress;
        const x = centerX + Math.cos(angle) * radius;
        const y = centerY + Math.sin(angle) * radius;
        const size = (point.value / maxValue) * 15 + 5;

        // Holographic glow
        ctx.shadowBlur = size * 2;
        ctx.shadowColor = point.color;
        
        // Main point
        ctx.fillStyle = point.color;
        ctx.beginPath();
        ctx.arc(x, y, size * progress, 0, Math.PI * 2);
        ctx.fill();
        
        // Core light
        ctx.shadowBlur = 0;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.arc(x, y, size * 0.3 * progress, 0, Math.PI * 2);
        ctx.fill();

        // Connection lines to center
        if (progress > 0.5) {
          ctx.strokeStyle = point.color.replace('rgb', 'rgba').replace(')', ', 0.3)');
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.moveTo(centerX, centerY);
          ctx.lineTo(x, y);
          ctx.stroke();
        }
      });
    };

    const renderSpiral = (ctx: CanvasRenderingContext2D, points: DataPoint[], progress: number, centerX: number, centerY: number, maxRadius: number) => {
      const maxValue = Math.max(...points.map(p => p.value));
      
      points.forEach((point, index) => {
        const spiralProgress = (index / points.length) * progress;
        const angle = spiralProgress * Math.PI * 6; // Multiple rotations
        const radius = spiralProgress * maxRadius;
        const x = centerX + Math.cos(angle) * radius;
        const y = centerY + Math.sin(angle) * radius;
        const size = (point.value / maxValue) * 12 + 3;

        if (spiralProgress <= progress) {
          ctx.shadowBlur = size * 1.5;
          ctx.shadowColor = point.color;
          
          ctx.fillStyle = point.color;
          ctx.beginPath();
          ctx.arc(x, y, size, 0, Math.PI * 2);
          ctx.fill();
        }
      });
    };

    const renderWave = (ctx: CanvasRenderingContext2D, points: DataPoint[], progress: number, width: number, height: number) => {
      const maxValue = Math.max(...points.map(p => p.value));
      const segmentWidth = width / points.length;
      
      ctx.strokeStyle = 'rgba(0, 255, 170, 0.8)';
      ctx.lineWidth = 3;
      ctx.beginPath();
      
      points.forEach((point, index) => {
        const x = index * segmentWidth;
        const normalizedValue = point.value / maxValue;
        const y = height - (normalizedValue * height * 0.8 * progress);
        
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }

        // Data points
        if (index * segmentWidth <= width * progress) {
          ctx.save();
          ctx.shadowBlur = 10;
          ctx.shadowColor = point.color;
          ctx.fillStyle = point.color;
          ctx.beginPath();
          ctx.arc(x, y, 4, 0, Math.PI * 2);
          ctx.fill();
          ctx.restore();
        }
      });
      
      ctx.stroke();
    };

    const renderBars = (ctx: CanvasRenderingContext2D, points: DataPoint[], progress: number, width: number, height: number) => {
      const maxValue = Math.max(...points.map(p => p.value));
      const barWidth = (width * 0.8) / points.length;
      const spacing = barWidth * 0.2;
      
      points.forEach((point, index) => {
        const x = (width * 0.1) + index * (barWidth + spacing);
        const normalizedValue = point.value / maxValue;
        const barHeight = normalizedValue * height * 0.8 * progress;
        const y = height - barHeight;
        
        // Bar with gradient
        const gradient = ctx.createLinearGradient(x, y, x, y + barHeight);
        gradient.addColorStop(0, point.color);
        gradient.addColorStop(1, point.color.replace('rgb', 'rgba').replace(')', ', 0.3)'));
        
        ctx.fillStyle = gradient;
        ctx.shadowBlur = 8;
        ctx.shadowColor = point.color;
        ctx.fillRect(x, y, barWidth, barHeight);
        ctx.shadowBlur = 0;
      });
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [data, variant, isActive, height]);

  return (
    <div className={cn("relative w-full overflow-hidden rounded-[20px] glass-quantum border border-consciousness/25", className)} style={{ height }}>
      <canvas ref={canvasRef} className="w-full h-full" />
      
      {/* Progress indicator */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className="h-1 bg-consciousness/20 rounded-full overflow-hidden">
          <div 
            className="h-full bg-gradient-to-r from-consciousness to-creativity transition-all duration-300 ease-out"
            style={{ width: `${progress * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default DataSculpture;
