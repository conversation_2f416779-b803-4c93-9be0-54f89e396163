
import React from 'react';
import { useIsMobile } from '../../../hooks/use-mobile';
import MorphicContainer from '../../../components/atoms/MorphicContainer';
import Typography from '../../../components/atoms/Typography';
import { Brain, Lightbulb, TrendingUp } from 'lucide-react';

const ACIInsights: React.FC = () => {
  const isMobile = useIsMobile();
  
  const insights = [
    {
      icon: Brain,
      title: 'Optimization Opportunity',
      description: 'Supply chain workflow could benefit from Physarum Network Optimizer',
      impact: 'High',
      color: 'consciousness'
    },
    {
      icon: Lightbulb,
      title: 'Pattern Detection',
      description: 'Energy consumption spike detected in manufacturing process',
      impact: 'Medium',
      color: 'symbioautomate'
    },
    {
      icon: TrendingUp,
      title: 'Performance Trend',
      description: 'ACI optimizations showing 23% efficiency improvement',
      impact: 'High',
      color: 'symbioimpact'
    }
  ];

  return (
    <MorphicContainer
      variant="card"
      division="symbioautomate"
      interactive="hover"
      className="p-4 sm:p-6"
    >
      <div className="flex items-center gap-3 mb-4 sm:mb-6">
        <Brain className="text-consciousness-500 animate-quantum-flicker" size={isMobile ? 18 : 20} />
        <Typography 
          variant={isMobile ? "base" : "lg"} 
          weight="semibold" 
          color="consciousness"
        >
          ACI Insights
        </Typography>
      </div>

      <div className="space-y-3 sm:space-y-4">
        {insights.map((insight, index) => (
          <div
            key={index}
            className="p-3 sm:p-4 rounded-cellular bg-consciousness-500/5 border border-consciousness-500/20 hover:border-consciousness-500/40 transition-all duration-medium"
          >
            <div className="flex items-start gap-3">
              <insight.icon 
                size={isMobile ? 14 : 16} 
                className={`text-${insight.color}-500 mt-1 flex-shrink-0`}
              />
              <div className="flex-1 space-y-1">
                <Typography 
                  variant="sm" 
                  weight="medium" 
                  color={insight.color as any}
                >
                  {insight.title}
                </Typography>
                <Typography 
                  variant="xs" 
                  color="secondary" 
                  className="leading-relaxed"
                >
                  {insight.description}
                </Typography>
                <div className="flex items-center gap-2 mt-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium bg-${insight.color}-500/20 text-${insight.color}-500`}>
                    {insight.impact} Impact
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-consciousness-500/20">
        <Typography variant="xs" color="tertiary" className="text-center">
          ACI continuously analyzes your workflows for optimization opportunities
        </Typography>
      </div>
    </MorphicContainer>
  );
};

export default ACIInsights;
