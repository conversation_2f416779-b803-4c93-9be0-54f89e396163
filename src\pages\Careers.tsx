
import React from 'react';
import { MapPin, Clock, Users, Briefcase } from 'lucide-react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';

const Careers: React.FC = () => {
  const openings = [
    {
      title: 'Senior AI Engineer',
      department: 'SymbioCore',
      location: 'San Francisco, CA',
      type: 'Full-time',
      description: 'Build the next generation of Artificial Cellular Intelligence systems.'
    },
    {
      title: 'Automation Specialist',
      department: 'SymbioAutomate',
      location: 'Remote',
      type: 'Full-time',
      description: 'Design and implement intelligent automation workflows for enterprise clients.'
    },
    {
      title: 'Quantum Computing Researcher',
      department: 'SymbioLabs',
      location: 'Boston, MA',
      type: 'Full-time',
      description: 'Research and develop quantum algorithms for next-generation computing systems.'
    }
  ];

  const benefits = [
    'Competitive salary and equity',
    'Comprehensive health benefits',
    'Flexible work arrangements',
    'Professional development budget',
    'Cutting-edge technology access',
    'Innovation time (20% projects)'
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="py-20">
          <div className="container mx-auto px-8">
            <div className="text-center mb-16">
              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="consciousness"
                className="mb-6"
              >
                Join the Future
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Help us build the symbiotic intelligence that will shape tomorrow's world
              </Typography>
            </div>

            {/* Open Positions */}
            <div className="mb-16">
              <Typography 
                as="h2" 
                variant="2xl" 
                weight="bold" 
                color="consciousness"
                className="mb-8"
              >
                Open Positions
              </Typography>
              
              <div className="space-y-6">
                {openings.map((job, index) => (
                  <Card 
                    key={job.title}
                    variant="neural" 
                    className="p-6 border-consciousness/20 hover:border-consciousness/40 transition-all duration-500 rounded-[20px]"
                  >
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between">
                      <div className="flex-1">
                        <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                          {job.title}
                        </Typography>
                        <Typography variant="sm" color="secondary" className="mb-4">
                          {job.description}
                        </Typography>
                        <div className="flex flex-wrap gap-4">
                          <div className="flex items-center space-x-2">
                            <Briefcase className="w-4 h-4 text-tertiary" />
                            <Typography variant="sm" color="tertiary">
                              {job.department}
                            </Typography>
                          </div>
                          <div className="flex items-center space-x-2">
                            <MapPin className="w-4 h-4 text-tertiary" />
                            <Typography variant="sm" color="tertiary">
                              {job.location}
                            </Typography>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Clock className="w-4 h-4 text-tertiary" />
                            <Typography variant="sm" color="tertiary">
                              {job.type}
                            </Typography>
                          </div>
                        </div>
                      </div>
                      <div className="mt-4 lg:mt-0">
                        <Button variant="outline-quantum" size="sm" className="rounded-[12px]">
                          Apply Now
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Benefits */}
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <Typography 
                  as="h2" 
                  variant="2xl" 
                  weight="bold" 
                  color="consciousness"
                  className="mb-6"
                >
                  Why SymbioWave?
                </Typography>
                <div className="space-y-3">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-harmony rounded-full"></div>
                      <Typography variant="sm" color="secondary">
                        {benefit}
                      </Typography>
                    </div>
                  ))}
                </div>
              </div>
              
              <Card variant="quantum" className="p-8 border-consciousness/30 rounded-[24px]">
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                  Don't See a Perfect Match?
                </Typography>
                <Typography variant="sm" color="secondary" className="mb-6">
                  We're always looking for talented individuals to join our mission. 
                  Send us your resume and tell us how you'd like to contribute.
                </Typography>
                <Button variant="quantum" size="sm" className="rounded-[12px]">
                  Send Resume
                </Button>
              </Card>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Careers;
