import React, { forwardRef } from 'react';

// Bio-Quantum Enhanced TypeScript interfaces
interface CardVariantConfig {
  base: string;
  hover: string;
  focus: string;
  shadow: string;
  border: string;
  glow: string;
}

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'neural' | 'quantum' | 'cellular' | 'consciousness' | 'creativity' | 'intuition' |
           'symbioautomate' | 'symbiolabs' | 'symbioxchange' | 'symbioedge' | 'symbioimpact' |
           'symbioventures' | 'symbioalliance';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  elevation?: 'none' | 'subtle' | 'medium' | 'high' | 'dramatic';
  interactive?: boolean;
  disabled?: boolean;
  loading?: boolean;
  morphic?: boolean;
  customGradient?: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  onHover?: (isHovered: boolean) => void;
}

// Bio-Quantum Design System Configuration
const CARD_VARIANTS: Record<string, CardVariantConfig> = {
  // Core Bio-Quantum Variants
  neural: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-consciousness-50/5',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-consciousness-100/8',
    focus: 'focus-visible:ring-2 focus-visible:ring-consciousness/50 focus-visible:ring-offset-2',
    shadow: 'shadow-quantum-primary',
    border: 'border border-consciousness/20',
    glow: 'var(--consciousness-glow)'
  },
  quantum: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-consciousness-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-consciousness-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-consciousness/60 focus-visible:ring-offset-2',
    shadow: 'shadow-quantum-intense',
    border: 'border border-consciousness/30',
    glow: 'var(--consciousness-glow)'
  },
  cellular: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioimpact-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioimpact-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioimpact/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioimpact-500/20',
    border: 'border border-symbioimpact/20',
    glow: 'var(--symbioimpact-glow)'
  },
  consciousness: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-consciousness-500/15',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-consciousness-400/20',
    focus: 'focus-visible:ring-2 focus-visible:ring-consciousness/60 focus-visible:ring-offset-2',
    shadow: 'shadow-sentient-glow',
    border: 'border border-consciousness/40',
    glow: 'var(--consciousness-emissive)'
  },
  creativity: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioedge-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioedge-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioedge/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioedge-500/20',
    border: 'border border-symbioedge/20',
    glow: 'var(--symbioedge-glow)'
  },
  intuition: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioventures-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioventures-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioventures/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioventures-500/20',
    border: 'border border-symbioventures/20',
    glow: 'var(--symbioventures-glow)'
  },

  // SymbioWave Division-Specific Variants
  symbioautomate: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioautomate-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioautomate-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioautomate/50 focus-visible:ring-offset-2',
    shadow: 'shadow-division-auto',
    border: 'border border-symbioautomate/20',
    glow: 'var(--symbioautomate-glow)'
  },
  symbiolabs: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbiolabs-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbiolabs-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbiolabs/50 focus-visible:ring-offset-2',
    shadow: 'shadow-division-labs',
    border: 'border border-symbiolabs/20',
    glow: 'var(--symbiolabs-glow)'
  },
  symbioxchange: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioxchange-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioxchange-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioxchange/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioxchange-500/20',
    border: 'border border-symbioxchange/20',
    glow: 'var(--symbioxchange-glow)'
  },
  symbioedge: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioedge-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioedge-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioedge/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioedge-500/20',
    border: 'border border-symbioedge/20',
    glow: 'var(--symbioedge-glow)'
  },
  symbioimpact: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioimpact-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioimpact-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioimpact/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioimpact-500/20',
    border: 'border border-symbioimpact/20',
    glow: 'var(--symbioimpact-glow)'
  },
  symbioventures: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioventures-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioventures-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioventures/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioventures-500/20',
    border: 'border border-symbioventures/20',
    glow: 'var(--symbioventures-glow)'
  },
  symbioalliance: {
    base: 'bg-gradient-to-br from-abyssal-elevated/90 via-abyssal-deep/95 to-symbioalliance-500/10',
    hover: 'hover:from-abyssal-elevated/95 hover:via-abyssal-deep/98 hover:to-symbioalliance-400/15',
    focus: 'focus-visible:ring-2 focus-visible:ring-symbioalliance/50 focus-visible:ring-offset-2',
    shadow: 'shadow-lg shadow-symbioalliance-500/20',
    border: 'border border-symbioalliance/20',
    glow: 'var(--symbioalliance-glow)'
  }
};

// Bio-Quantum Size System with Morphic Options
const CARD_SIZES = {
  xs: 'p-3',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
  xl: 'p-10'
};

const CARD_BORDER_RADIUS = {
  standard: {
    xs: 'rounded-lg',
    sm: 'rounded-lg',
    md: 'rounded-xl',
    lg: 'rounded-xl',
    xl: 'rounded-2xl'
  },
  morphic: {
    xs: 'rounded-morphic-subtle',
    sm: 'rounded-organic',
    md: 'rounded-cellular',
    lg: 'rounded-neural',
    xl: 'rounded-biomorphic'
  }
};

const CARD_ELEVATIONS = {
  none: '',
  subtle: 'shadow-sm',
  medium: 'shadow-md',
  high: 'shadow-xl',
  dramatic: 'shadow-2xl'
};

// Utility function to combine class names (enhanced for bio-quantum patterns)
const cn = (...classes: (string | string[] | undefined | null | false)[]): string => {
  return classes
    .flat()
    .filter(Boolean)
    .join(' ');
};

const Card = forwardRef<HTMLDivElement, CardProps>(({
  variant = 'neural',
  size = 'md',
  elevation = 'medium',
  interactive = false,
  disabled = false,
  loading = false,
  morphic = false,
  customGradient,
  children,
  className,
  onClick,
  onHover,
  style,
  ...props
}, ref) => {
  const [isHovered, setIsHovered] = React.useState(false);

  const variantConfig = CARD_VARIANTS[variant] || CARD_VARIANTS.neural;

  const handleMouseEnter = () => {
    if (!disabled) {
      setIsHovered(true);
      onHover?.(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    onHover?.(false);
  };

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && !disabled && !loading && onClick) {
      e.preventDefault();
      onClick();
    }
  };

  // Bio-quantum base styles with morphic design support
  const baseStyles = cn(
    'relative overflow-hidden',
    'transition-all duration-[var(--quantum-medium,_300ms)] ease-[var(--quantum-ease,_cubic-bezier(0.68,-0.55,0.265,1.55))]',
    'backdrop-blur-[24px] backdrop-saturate-150 backdrop-brightness-110',
    CARD_SIZES[size],
    morphic ? CARD_BORDER_RADIUS.morphic[size] : CARD_BORDER_RADIUS.standard[size],
    CARD_ELEVATIONS[elevation]
  );

  // Variant-specific styles
  const variantStyles = cn(
    variantConfig.base,
    variantConfig.border,
    variantConfig.shadow,
    !disabled && variantConfig.hover,
    !disabled && variantConfig.focus
  );

  // Interactive styles
  const interactiveStyles = cn(
    (interactive || onClick) && !disabled && [
      'cursor-pointer',
      'transform-gpu',
      'hover:scale-[1.02]',
      'hover:-translate-y-0.5',
      'active:scale-[0.98]',
      'active:translate-y-0'
    ]
  );

  // State-specific styles
  const stateStyles = cn(
    disabled && 'opacity-60 cursor-not-allowed',
    loading && 'pointer-events-none'
  );

  const finalStyle = customGradient 
    ? { ...style, background: customGradient }
    : style;

  return (
    <div
      ref={ref}
      className={cn(
        baseStyles,
        variantStyles,
        interactiveStyles,
        stateStyles,
        className
      )}
      style={finalStyle}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onKeyDown={(interactive || onClick) ? handleKeyDown : undefined}
      tabIndex={(interactive || onClick) && !disabled ? 0 : undefined}
      role={(interactive || onClick) ? 'button' : undefined}
      aria-disabled={disabled}
      aria-busy={loading}
      {...props}
    >
      {/* Bio-quantum shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-consciousness/5 to-transparent -translate-x-full hover:translate-x-full transition-transform duration-[var(--quantum-slow,_1000ms)] ease-out" />

      {/* Loading state with bio-quantum spinner */}
      {loading && (
        <div className="absolute inset-0 bg-abyssal-void/40 flex items-center justify-center backdrop-blur-sm rounded-inherit">
          <div className="w-6 h-6 border-2 border-consciousness border-t-transparent rounded-full animate-spin opacity-80 shadow-quantum-primary" />
        </div>
      )}

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Bio-quantum glow effect on hover */}
      {isHovered && !disabled && (
        <div
          className="absolute inset-0 rounded-inherit opacity-20 animate-pulse"
          style={{
            background: `radial-gradient(circle at center, ${variantConfig.glow} 0%, transparent 70%)`,
            filter: 'blur(8px)'
          }}
        />
      )}

      {/* Morphic field enhancement for morphic cards */}
      {morphic && (
        <div className="absolute inset-0 bg-gradient-to-br from-consciousness/5 via-transparent to-consciousness/5 rounded-inherit opacity-60" />
      )}
    </div>
  );
});

Card.displayName = 'Card';


export default Card;
