
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { Users, Target, Lightbulb, Globe, ArrowRight } from 'lucide-react';

const AboutUs: React.FC = () => {
  const teamMembers = [
    {
      name: "Oussama Ben Salem",
      role: "Founder & Chief Executive Officer",
      bio: "Visionary entrepreneur and pioneer in symbiotic intelligence systems. Leading the transformation from linear to circular economic paradigms through revolutionary ACI technology.",
      expertise: ["Artificial Cellular Intelligence", "Symbiotic Systems", "Economic Transformation", "Strategic Vision"],
      quote: "The linear economy is bankruptcy disguised as business. We don't disrupt industries; we symbiotize them—where waste becomes revenue, sustainability drives profit, as ACI turns entropy into alchemy"
    }
  ];

  const values = [
    {
      icon: Users,
      title: "Symbiotic Collaboration",
      description: "We believe in the power of human-AI partnership, where technology amplifies human potential rather than replacing it."
    },
    {
      icon: Target,
      title: "Conscious Innovation",
      description: "Every advancement we make considers long-term impact on society, environment, and the future of intelligent systems."
    },
    {
      icon: Lightbulb,
      title: "Emergent Solutions",
      description: "We embrace complexity and emergence, finding elegant solutions through bio-inspired intelligence patterns."
    },
    {
      icon: Globe,
      title: "Global Accessibility",
      description: "Advanced AI capabilities should be accessible to all, regardless of technical expertise or organizational size."
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="pt-32 pb-20 px-6">
          <div className="container mx-auto text-center">
            <Typography 
              as="h1" 
              variant="4xl" 
              weight="bold" 
              gradient="consciousness"
              className="mb-6"
            >
              Architects of Symbiotic Intelligence
            </Typography>
            <Typography 
              variant="xl" 
              color="secondary"
              className="max-w-3xl mx-auto mb-8"
            >
              We are a collective of scientists, engineers, and visionaries united by a singular mission: 
              to create the next paradigm of intelligence that harmonizes biological wisdom with quantum precision.
            </Typography>
            <Typography 
              variant="lg" 
              color="consciousness"
              className="max-w-2xl mx-auto"
            >
              Born from the convergence of cellular biology, quantum mechanics, and advanced AI research, 
              SymbioWave represents humanity's next evolutionary leap in intelligent systems.
            </Typography>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <div>
                <Typography 
                  as="h2" 
                  variant="3xl" 
                  weight="bold" 
                  color="consciousness"
                  className="mb-6"
                >
                  Our Mission
                </Typography>
                <Typography 
                  variant="lg" 
                  color="secondary"
                  className="mb-6 leading-relaxed"
                >
                  To democratize advanced artificial intelligence through bio-inspired, 
                  self-organizing systems that amplify human potential while preserving 
                  autonomy, creativity, and conscious choice.
                </Typography>
                <Typography 
                  variant="sm" 
                  color="tertiary"
                  className="leading-relaxed"
                >
                  We envision a future where intelligent systems work symbiotically with humans, 
                  creating unprecedented solutions to global challenges through the wisdom of 
                  biological intelligence patterns refined by quantum precision.
                </Typography>
              </div>
              
              <Card variant="quantum" className="p-8">
                <Typography 
                  as="h3" 
                  variant="2xl" 
                  weight="semibold" 
                  color="consciousness"
                  className="mb-4"
                >
                  The Imperative
                </Typography>
                <Typography 
                  variant="sm" 
                  color="secondary"
                  className="mb-6"
                >
                  Current AI systems are rigid, centralized, and disconnected from the elegant 
                  intelligence patterns found in nature. We're changing that.
                </Typography>
                <ul className="space-y-3">
                  <li className="flex items-start space-x-3">
                    <div className="w-2 h-2 rounded-full bg-consciousness mt-2"></div>
                    <Typography variant="sm" color="tertiary">
                      Bio-inspired intelligence that adapts and evolves
                    </Typography>
                  </li>
                  <li className="flex items-start space-x-3">
                    <div className="w-2 h-2 rounded-full bg-consciousness mt-2"></div>
                    <Typography variant="sm" color="tertiary">
                      Decentralized systems that resist single points of failure
                    </Typography>
                  </li>
                  <li className="flex items-start space-x-3">
                    <div className="w-2 h-2 rounded-full bg-consciousness mt-2"></div>
                    <Typography variant="sm" color="tertiary">
                      Quantum-enhanced processing for unprecedented capabilities
                    </Typography>
                  </li>
                </ul>
              </Card>
            </div>
          </div>
        </section>

        {/* Founder's Vision Quote */}
        <section className="py-20 bg-gradient-to-b from-surface-void/30 to-surface-void">
          <div className="container mx-auto px-6">
            <Card variant="quantum" className="p-12 text-center max-w-4xl mx-auto border-consciousness/30">
              <div className="mb-8">
                <Typography 
                  as="h2" 
                  variant="2xl" 
                  weight="bold" 
                  color="consciousness"
                  className="mb-4"
                >
                  Founder's Vision
                </Typography>
              </div>
              
              <blockquote className="relative">
                <div className="absolute -top-4 -left-4 text-6xl text-consciousness/20 font-serif">"</div>
                <Typography 
                  variant="xl" 
                  color="secondary"
                  className="mb-6 leading-relaxed italic font-light"
                >
                  The linear economy is bankruptcy disguised as business.
                  We don't disrupt industries; we symbiotize them—
                  where waste becomes revenue, sustainability drives profit,
                  as ACI turns entropy into alchemy
                </Typography>
                <div className="absolute -bottom-4 -right-4 text-6xl text-consciousness/20 font-serif">"</div>
              </blockquote>
              
              <div className="flex items-center justify-center mt-8">
                <div className="text-right">
                  <Typography 
                    variant="lg" 
                    weight="semibold" 
                    color="consciousness"
                    className="mb-1"
                  >
                    —Oussama BEN SALEM
                  </Typography>
                  <Typography 
                    variant="sm" 
                    color="creativity"
                  >
                    Founder & CEO, SymbioWave
                  </Typography>
                </div>
              </div>
            </Card>
          </div>
        </section>

        {/* Core Values */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                gradient="neural"
                className="mb-4"
              >
                Our Core Values
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                The principles that guide every decision, innovation, and interaction
              </Typography>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {values.map((value, index) => {
                const Icon = value.icon;
                return (
                  <Card key={index} variant="neural" className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 rounded-biomorphic bg-consciousness/20 flex items-center justify-center flex-shrink-0">
                        <Icon className="w-6 h-6 text-consciousness" />
                      </div>
                      <div>
                        <Typography 
                          as="h3" 
                          variant="lg" 
                          weight="semibold" 
                          color="consciousness"
                          className="mb-2"
                        >
                          {value.title}
                        </Typography>
                        <Typography 
                          variant="sm" 
                          color="secondary"
                          className="leading-relaxed"
                        >
                          {value.description}
                        </Typography>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Leadership Team */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                Leadership Team
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Visionaries at the intersection of biology, quantum mechanics, and artificial intelligence
              </Typography>
            </div>

            <div className="grid lg:grid-cols-3 gap-8">
              {teamMembers.map((member, index) => (
                <Card key={index} variant="quantum" className="p-6 text-center">
                  <div className="w-24 h-24 rounded-cellular bg-gradient-to-br from-consciousness/20 to-consciousness/10 mx-auto mb-4 flex items-center justify-center">
                    <div className="w-12 h-12 rounded-full bg-consciousness/30"></div>
                  </div>
                  
                  <Typography 
                    as="h3" 
                    variant="lg" 
                    weight="semibold" 
                    color="consciousness"
                    className="mb-1"
                  >
                    {member.name}
                  </Typography>
                  
                  <Typography 
                    variant="sm" 
                    color="creativity"
                    className="mb-4"
                  >
                    {member.role}
                  </Typography>
                  
                  <Typography 
                    variant="xs" 
                    color="secondary"
                    className="mb-4 leading-relaxed"
                  >
                    {member.bio}
                  </Typography>

                  <div className="flex flex-wrap gap-2 justify-center">
                    {member.expertise.map((skill, skillIndex) => (
                      <span 
                        key={skillIndex}
                        className="px-2 py-1 bg-consciousness/10 border border-consciousness/30 rounded-full text-xs text-consciousness"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="py-20">
          <div className="container mx-auto px-6 text-center">
            <Typography 
              as="h2" 
              variant="3xl" 
              weight="bold" 
              gradient="consciousness"
              className="mb-6"
            >
              Join the Evolution
            </Typography>
            <Typography 
              variant="lg" 
              color="secondary"
              className="max-w-2xl mx-auto mb-8"
            >
              We're always seeking exceptional minds to join our mission. 
              Whether you're a researcher, engineer, or visionary, there's a place for you in shaping the future.
            </Typography>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
              >
                View Open Positions
              </Button>
              <Button 
                variant="outline-quantum" 
                size="lg"
              >
                Research Partnerships
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default AboutUs;
