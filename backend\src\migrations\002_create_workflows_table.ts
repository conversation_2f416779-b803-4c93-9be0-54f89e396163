import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('workflows', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('name').notNullable();
    table.text('description');
    table.integer('version').defaultTo(1);
    table.jsonb('nodes').defaultTo('[]');
    table.jsonb('connections').defaultTo('[]');
    table.enum('status', ['draft', 'active', 'paused', 'error', 'archived']).defaultTo('draft');
    table.boolean('is_active').defaultTo(false);
    table.jsonb('tags').defaultTo('[]');
    table.string('category');
    table.timestamp('last_executed');
    table.timestamps(true, true);

    // Indexes
    table.index(['user_id']);
    table.index(['status']);
    table.index(['is_active']);
    table.index(['category']);
    table.index(['created_at']);
    table.index(['last_executed']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workflows');
}
