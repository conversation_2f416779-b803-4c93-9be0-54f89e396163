
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import { Globe, Handshake, Building, Scale } from 'lucide-react';

const SymbioAlliance: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-consciousness/10 rounded-cellular blur-3xl animate-consciousness-wave"></div>
            <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-consciousness/15 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
          </div>

          <div className="container mx-auto px-6 py-20 relative z-10">
            <div className="text-center space-y-8 max-w-4xl mx-auto">
              <div className="inline-flex items-center space-x-3 px-4 py-2 glass-quantum rounded-full border border-consciousness/30">
                <Globe className="w-5 h-5 text-consciousness animate-pulse" />
                <Typography variant="sm" weight="medium" color="consciousness">
                  Division VIII
                </Typography>
              </div>

              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="consciousness"
                className="mb-4"
              >
                SymbioAlliance
              </Typography>
              
              <Typography 
                as="p" 
                variant="xl" 
                color="consciousness" 
                weight="medium"
                className="mb-6"
              >
                The Global Scaling Engine
              </Typography>

              <Typography 
                as="p" 
                variant="lg" 
                color="secondary"
                className="leading-relaxed max-w-2xl mx-auto"
              >
                A strategic partnership and global policy unit dedicated to scaling the adoption of the symbiotic economy worldwide.
              </Typography>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <Handshake className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Strategic Partnerships
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Global alliances
                  </Typography>
                </Card>

                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <Building className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Government Relations
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Policy advocacy
                  </Typography>
                </Card>

                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <Scale className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Industry Standards
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Market leadership
                  </Typography>
                </Card>

                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <Globe className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Global Expansion
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Worldwide adoption
                  </Typography>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default SymbioAlliance;
