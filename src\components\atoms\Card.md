# Card Component - Bio-Quantum Integration

## Overview

The Card component has been fully integrated with the SymbioWave bio-quantum design system, featuring consciousness-driven aesthetics, morphic design patterns, and division-specific variants that align perfectly with the website's bio-quantum theme.

## Key Features

### ✅ Bio-Quantum Design System Integration
- Uses CSS custom properties from `src/styles/base.css`
- Consciousness cyan-teal primary colors with abyssal canvas backgrounds
- Quantum timing and easing functions (`var(--quantum-medium)`, `var(--quantum-ease)`)
- Enhanced backdrop blur and saturation effects

### ✅ Comprehensive Variant Support
- **Core Variants**: `neural`, `quantum`, `cellular`, `consciousness`, `creativity`, `intuition`
- **Division-Specific**: `symbioautomate`, `symbiolabs`, `symbioxchange`, `symbioedge`, `symbioimpact`, `symbioventures`, `symbioalliance`

### ✅ Advanced Bio-Quantum Features
- Morphic design patterns with organic border radius
- Bio-quantum glow effects using CSS custom properties
- Enhanced shimmer animations with consciousness colors
- Loading states with quantum-styled spinners
- Comprehensive accessibility features

## Usage Examples

### Basic Usage
```tsx
import Card from '@/components/atoms/Card';

// Neural variant (default)
<Card variant="neural" size="md">
  <h3>Neural Processing</h3>
  <p>Advanced AI capabilities</p>
</Card>

// Quantum variant with enhanced effects
<Card variant="quantum" size="lg" interactive>
  <h3>Quantum Computing</h3>
  <p>Next-generation processing power</p>
</Card>
```

### Division-Specific Variants
```tsx
// SymbioAutomate - Efficiency Emerald
<Card variant="symbioautomate" size="md">
  <h3>Automation Solutions</h3>
  <p>Streamline your workflows</p>
</Card>

// SymbioLabs - Innovation Amber
<Card variant="symbiolabs" size="lg" morphic>
  <h3>Research & Development</h3>
  <p>Cutting-edge innovations</p>
</Card>

// SymbioXchange - Symbiosis Violet
<Card variant="symbioxchange" size="md" interactive>
  <h3>Marketplace</h3>
  <p>Exchange and collaborate</p>
</Card>
```

### Morphic Design
```tsx
// Organic shapes with bio-quantum aesthetics
<Card 
  variant="consciousness" 
  size="lg" 
  morphic 
  interactive
  onHover={(isHovered) => console.log('Hover state:', isHovered)}
>
  <h3>Consciousness Interface</h3>
  <p>Bio-quantum morphic design</p>
</Card>
```

### Interactive Cards
```tsx
<Card 
  variant="quantum" 
  size="md" 
  interactive 
  onClick={() => navigate('/quantum-interface')}
  className="hover:scale-105 transition-transform"
>
  <h3>Quantum Interface</h3>
  <p>Click to explore</p>
</Card>
```

### Loading States
```tsx
<Card 
  variant="consciousness" 
  size="lg" 
  loading 
  disabled
>
  <h3>Processing...</h3>
  <p>Bio-quantum computation in progress</p>
</Card>
```

## Variant Guide

### Core Bio-Quantum Variants
- `neural`: Default variant with consciousness accents and neural aesthetics
- `quantum`: Enhanced quantum effects with intense consciousness glow
- `cellular`: Bio-inspired with symbioimpact green tones
- `consciousness`: Primary consciousness variant with emissive effects
- `creativity`: Creative flow with symbioedge blue aesthetics
- `intuition`: Intuitive design with symbioventures coral tones

### SymbioWave Division Variants
- `symbioautomate`: Efficiency Emerald - Automation and workflow optimization
- `symbiolabs`: Innovation Amber - Research and development
- `symbioxchange`: Symbiosis Violet - Marketplace and collaboration
- `symbioedge`: Frontier Electric Blue - Data and analytics
- `symbioimpact`: Sustainability Forest Teal - Environmental impact
- `symbioventures`: Catalyst Coral - Investment and growth
- `symbioalliance`: Global Unity Sapphire - Partnerships and alliances

## Props Interface

```tsx
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'neural' | 'quantum' | 'cellular' | 'consciousness' | 'creativity' | 'intuition' | 
           'symbioautomate' | 'symbiolabs' | 'symbioxchange' | 'symbioedge' | 'symbioimpact' | 
           'symbioventures' | 'symbioalliance';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  elevation?: 'none' | 'subtle' | 'medium' | 'high' | 'dramatic';
  interactive?: boolean;
  disabled?: boolean;
  loading?: boolean;
  morphic?: boolean;
  customGradient?: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  onHover?: (isHovered: boolean) => void;
}
```

## Size Guide

- `xs`: Compact cards (p-3) - For small content blocks
- `sm`: Small cards (p-4) - For list items or compact features
- `md`: Medium cards (p-6) - Default size for most content
- `lg`: Large cards (p-8) - For featured content or detailed information
- `xl`: Extra large cards (p-10) - For hero sections or major features

## Morphic Design

When `morphic={true}`, cards use organic border radius patterns:
- `xs`: `rounded-morphic-subtle` - Subtle organic curves
- `sm`: `rounded-organic` - Natural organic shapes
- `md`: `rounded-cellular` - Cell-inspired patterns
- `lg`: `rounded-neural` - Neural network aesthetics
- `xl`: `rounded-biomorphic` - Complex bio-inspired forms

## Integration Notes

### CSS Custom Properties
The component uses bio-quantum CSS custom properties:
- `--consciousness-primary`, `--consciousness-glow`, `--consciousness-emissive`
- `--symbioautomate-primary`, `--symbioautomate-glow`, etc.
- `--quantum-medium`, `--quantum-ease` (timing functions)
- `--abyssal-void`, `--abyssal-elevated` (background depths)

### Compatibility
- ✅ Fully backward compatible with existing usage
- ✅ Enhanced with new bio-quantum features
- ✅ TypeScript fully supported
- ✅ Accessibility compliant (WCAG 2.1 AA)

### Import Path
```tsx
import Card from '@/components/atoms/Card';
```

## Migration Guide

Existing cards will continue to work without changes. To leverage new features:

1. **Add division variants**: Replace generic variants with division-specific ones
2. **Enable morphic design**: Add `morphic={true}` for organic shapes
3. **Use interactive features**: Add `interactive={true}` and `onClick` handlers
4. **Leverage new variants**: Use `consciousness`, `symbioautomate`, etc.

## Performance

- Optimized animations using `transform-gpu`
- Efficient CSS custom properties
- Minimal re-renders with React.memo patterns
- Hardware-accelerated effects

## Accessibility

- Full keyboard navigation support
- ARIA attributes for interactive states
- Focus management and visual indicators
- Screen reader compatible
- High contrast support

## Testing

Test all variants and features using the component in different contexts:
- Different viewport sizes
- Light/dark mode compatibility
- Interactive state management
- Loading state behavior
- Accessibility compliance
