import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import FlywheelVisualization from '../components/features/FlywheelVisualization';
import { ArrowRight, Zap, RefreshCw, TrendingUp, Network } from 'lucide-react';

const EcosystemFlywheel: React.FC = () => {
  const flywheelComponents = [
    {
      icon: Zap,
      title: "Data Generation",
      description: "Every interaction across the SymbioWave ecosystem generates valuable data patterns, feeding the collective intelligence.",
      color: "consciousness"
    },
    {
      icon: RefreshCw,
      title: "ACI Learning",
      description: "Artificial Cellular Intelligence processes this data, identifying optimization opportunities and emergent patterns.",
      color: "creativity"
    },
    {
      icon: TrendingUp,
      title: "Value Creation",
      description: "Enhanced capabilities translate into measurable value for users - efficiency gains, cost reductions, new opportunities.",
      color: "consciousness"
    },
    {
      icon: Network,
      title: "Network Effects",
      description: "More users create more data, better AI, and increased value - attracting even more participants to the ecosystem.",
      color: "creativity"
    }
  ];

  const benefits = [
    {
      metric: "47%",
      label: "Average Efficiency Gain",
      description: "Across all SymbioAutomate implementations"
    },
    {
      metric: "23x",
      label: "ROI Multiplier",
      description: "Through ecosystem synergies and optimizations"
    },
    {
      metric: "89%",
      label: "User Retention",
      description: "Due to continuous value acceleration"
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="pt-32 pb-20 px-6">
          <div className="container mx-auto text-center">
            <Typography 
              as="h1" 
              variant="4xl" 
              weight="bold" 
              gradient="consciousness"
              className="mb-6"
            >
              The SymbioWave Ecosystem Flywheel
            </Typography>
            <Typography 
              variant="xl" 
              color="secondary"
              className="max-w-3xl mx-auto mb-8"
            >
              A self-reinforcing system where each interaction amplifies the value for all participants, 
              creating exponential growth in capability and intelligence.
            </Typography>
            <Typography 
              variant="lg" 
              color="consciousness"
              className="max-w-2xl mx-auto"
            >
              The more you use SymbioWave, the smarter it becomes. The smarter it becomes, 
              the more value it creates. The more value it creates, the more it grows.
            </Typography>
          </div>
        </section>

        {/* Enhanced Flywheel Visualization */}
        <section className="py-20 bg-gradient-to-b from-surface-void/50 to-surface-void">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                color="consciousness"
                className="mb-4"
              >
                How the Flywheel Works
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Each revolution of the flywheel increases momentum, creating a virtuous cycle of growth and improvement
              </Typography>
            </div>

            <div className="mb-16">
              <FlywheelVisualization />
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {flywheelComponents.map((component, index) => {
                const Icon = component.icon;
                return (
                  <Card key={index} variant="neural" className="p-6 text-center">
                    <div className={`w-16 h-16 rounded-cellular bg-${component.color}/20 flex items-center justify-center mx-auto mb-4`}>
                      <Icon className={`w-8 h-8 text-${component.color}`} />
                    </div>
                    
                    <Typography 
                      as="h3" 
                      variant="lg" 
                      weight="semibold" 
                      color={component.color as any}
                      className="mb-3"
                    >
                      {component.title}
                    </Typography>
                    
                    <Typography 
                      variant="sm" 
                      color="secondary"
                      className="leading-relaxed"
                    >
                      {component.description}
                    </Typography>
                  </Card>
                );
              })}
            </div>
          </div>
        </section>

        {/* Proven Results */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <Typography 
                as="h2" 
                variant="3xl" 
                weight="bold" 
                gradient="neural"
                className="mb-4"
              >
                Proven Flywheel Results
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Real metrics from organizations already benefiting from the SymbioWave ecosystem
              </Typography>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {benefits.map((benefit, index) => (
                <Card key={index} variant="quantum" className="p-8 text-center">
                  <Typography 
                    as="div" 
                    variant="4xl" 
                    weight="bold" 
                    color="consciousness"
                    className="mb-2"
                  >
                    {benefit.metric}
                  </Typography>
                  <Typography 
                    as="h3" 
                    variant="lg" 
                    weight="semibold" 
                    color="creativity"
                    className="mb-2"
                  >
                    {benefit.label}
                  </Typography>
                  <Typography 
                    variant="sm" 
                    color="secondary"
                  >
                    {benefit.description}
                  </Typography>
                </Card>
              ))}
            </div>

            <div className="text-center">
              <Card variant="neural" className="p-8 inline-block max-w-2xl">
                <Typography 
                  as="h3" 
                  variant="xl" 
                  weight="semibold" 
                  color="consciousness"
                  className="mb-4"
                >
                  Ready to Accelerate Your Flywheel?
                </Typography>
                <Typography 
                  variant="sm" 
                  color="secondary"
                  className="mb-6"
                >
                  Join the growing ecosystem of organizations leveraging ACI for exponential value creation.
                </Typography>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button 
                    variant="quantum" 
                    size="lg"
                    rightIcon={<ArrowRight className="w-5 h-5" />}
                  >
                    Start Your Journey
                  </Button>
                  <Button 
                    variant="outline-quantum" 
                    size="lg"
                  >
                    View Case Studies
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default EcosystemFlywheel;