
import React from 'react';
import { Link } from 'react-router-dom';
import Typography from '../../../components/atoms/Typography';
import Card from '../../../components/atoms/Card';
import Button from '../../../components/atoms/Button';
import { Brain, Target, TrendingUp, ArrowRight, CheckCircle, Zap } from 'lucide-react';

const AIReadinessLanding: React.FC = () => {
  const auditBenefits = [
    {
      icon: Brain,
      title: "AI Strategy Assessment",
      description: "Understand your current AI maturity and identify strategic opportunities for intelligent automation."
    },
    {
      icon: Target,
      title: "Personalized Recommendations",
      description: "Receive curated AI tool suggestions and automation workflows tailored to your specific industry and needs."
    },
    {
      icon: TrendingUp,
      title: "ROI Projections",
      description: "Get data-driven estimates of potential cost savings and efficiency gains from AI implementation."
    }
  ];

  const processSteps = [
    "Complete a 5-minute guided questionnaire about your business processes",
    "ACI analyzes your responses using advanced pattern recognition",
    "Receive a comprehensive audit report with actionable insights",
    "Access personalized workflow templates and tool recommendations"
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-surface-void to-surface-void/50">
      <div className="container mx-auto px-6">
        {/* Main CTA Section */}
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="4xl" 
            weight="bold" 
            gradient="consciousness"
            className="mb-6"
          >
            Unlock Your Business's AI Potential
          </Typography>
          <Typography 
            as="h3" 
            variant="2xl" 
            weight="semibold" 
            color="secondary"
            className="mb-8"
          >
            Start with Your Free Symbiotic AI Readiness Audit
          </Typography>
          <Typography 
            variant="lg" 
            color="tertiary"
            className="max-w-3xl mx-auto mb-12"
          >
            Discover how SymbioAutomate can transform your business operations through intelligent automation. 
            Get personalized AI recommendations and workflow strategies tailored to your unique needs.
          </Typography>

          <div className="flex flex-col md:flex-row gap-6 justify-center mb-16">
            <Link to="/ai-readiness-audit">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<Brain className="w-6 h-6" />}
                className="w-full md:w-auto"
              >
                Start Your Free AI Readiness Audit
              </Button>
            </Link>
            <Link to="/symbioautomate/workflows">
              <Button 
                variant="outline-quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="w-full md:w-auto"
              >
                Explore Full Workflow Automation
              </Button>
            </Link>
          </div>
        </div>

        {/* Benefits Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {auditBenefits.map((benefit, index) => {
            const Icon = benefit.icon;
            return (
              <Card key={index} variant="neural" className="p-8 text-center border-symbioautomate/30">
                <div className="w-16 h-16 rounded-cellular bg-symbioautomate/20 flex items-center justify-center mx-auto mb-6">
                  <Icon className="w-8 h-8 text-symbioautomate" />
                </div>
                
                <Typography 
                  as="h4" 
                  variant="xl" 
                  weight="semibold" 
                  color="consciousness"
                  className="mb-4"
                >
                  {benefit.title}
                </Typography>
                
                <Typography 
                  variant="sm" 
                  color="secondary"
                  className="leading-relaxed"
                >
                  {benefit.description}
                </Typography>
              </Card>
            );
          })}
        </div>

        {/* Process Overview */}
        <Card variant="quantum" className="p-8 mb-12">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <Typography 
                as="h3" 
                variant="2xl" 
                weight="bold" 
                color="consciousness"
                className="mb-6"
              >
                How the AI Readiness Audit Works
              </Typography>
              <div className="space-y-4">
                {processSteps.map((step, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-6 h-6 rounded-full bg-consciousness/20 flex items-center justify-center flex-shrink-0 mt-1">
                      <Typography variant="xs" weight="bold" color="consciousness">
                        {index + 1}
                      </Typography>
                    </div>
                    <Typography variant="sm" color="secondary" className="leading-relaxed">
                      {step}
                    </Typography>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="relative">
              <Card variant="neural" className="p-6 border-symbioautomate/30">
                <div className="flex items-center space-x-3 mb-4">
                  <Zap className="w-6 h-6 text-symbioautomate" />
                  <Typography variant="lg" weight="semibold" color="symbioautomate">
                    Powered by ACI
                  </Typography>
                </div>
                <Typography variant="sm" color="secondary" className="mb-4">
                  Our Artificial Cellular Intelligence analyzes millions of business process patterns 
                  to provide insights that traditional consulting can't match.
                </Typography>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <Typography variant="xs" color="tertiary">
                    100% Confidential & Secure
                  </Typography>
                </div>
              </Card>
            </div>
          </div>
        </Card>

        {/* Value Proposition */}
        <div className="text-center">
          <Typography 
            as="h3" 
            variant="xl" 
            weight="semibold" 
            color="consciousness"
            className="mb-4"
          >
            From AI Education to Full Automation
          </Typography>
          <Typography 
            variant="sm" 
            color="secondary"
            className="max-w-2xl mx-auto mb-8"
          >
            Start with understanding your AI opportunities, then seamlessly transition to building 
            and deploying intelligent workflows that transform your business operations.
          </Typography>
          
          <Link to="/ai-readiness-audit">
            <Button 
              variant="cellular" 
              size="lg"
              rightIcon={<ArrowRight className="w-5 h-5" />}
            >
              Begin Your AI Transformation Journey
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default AIReadinessLanding;
