
// N8N Workflow Engine API Service
import config from '../config';
import type { ChatMessage, WorkflowDefinition } from '../types/symbioAutomate';

export class N8NApiService {
  private webhookUrl = config.n8n.webhookUrl;
  private apiUrl = config.n8n.workflowApiUrl;

  async sendChatMessage(message: string, userId: string, sessionId: string): Promise<ChatMessage> {
    try {
      const payload = {
        userId,
        sessionId,
        message,
        timestamp: new Date().toISOString(),
        context: 'symbio-automate'
      };

      console.log('N8N: Sending chat message', payload);

      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`N8N API error: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        id: Date.now().toString(),
        type: 'aci',
        content: data.responseMessage || 'I understand your request. Let me process that for you.',
        timestamp: new Date().toISOString(),
        metadata: {
          workflowAction: data.workflowAction,
          canvasData: data.canvasData,
          attachments: data.attachments
        }
      };
    } catch (error) {
      console.error('N8N API Error:', error);
      
      // Fallback response for development
      return {
        id: Date.now().toString(),
        type: 'aci',
        content: 'I\'m currently processing your request. The ACI systems are analyzing optimal workflow configurations.',
        timestamp: new Date().toISOString(),
        metadata: {}
      };
    }
  }

  async executeWorkflow(workflowId: string, inputData?: any) {
    console.log('N8N: Executing workflow', { workflowId, inputData });
    
    try {
      const response = await fetch(`${this.apiUrl}/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ inputData }),
      });

      return await response.json();
    } catch (error) {
      console.error('N8N Workflow Execution Error:', error);
      return { success: false, error: error.message };
    }
  }

  async getWorkflowStatus(workflowId: string) {
    console.log('N8N: Getting workflow status', workflowId);
    
    // Mock status for development
    const statuses = ['running', 'idle', 'optimizing', 'success', 'error'];
    return {
      workflowId,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      lastExecution: new Date().toISOString(),
      nextExecution: new Date(Date.now() + 60000).toISOString()
    };
  }

  async getWorkflowLogs(workflowId: string, limit = 50) {
    console.log('N8N: Getting workflow logs', { workflowId, limit });
    
    // Mock logs for development
    return Array.from({ length: limit }, (_, i) => ({
      id: `log-${i}`,
      timestamp: new Date(Date.now() - i * 60000).toISOString(),
      level: Math.random() > 0.9 ? 'error' : Math.random() > 0.7 ? 'warning' : 'info',
      message: `Workflow execution step ${i + 1} completed`,
      nodeId: `node-${Math.floor(Math.random() * 5)}`,
      executionTime: Math.random() * 1000 + 100
    }));
  }
}

export const n8nApi = new N8NApiService();
