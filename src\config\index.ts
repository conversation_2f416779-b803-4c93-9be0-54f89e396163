
// SymbioAutomate Core Configuration
export const config = {
  // N8N Workflow Engine Integration
  n8n: {
    webhookUrl: process.env.VITE_N8N_WEBHOOK_URL || 'https://your-n8n-instance.com/webhook/symbio-assist',
    workflowApiUrl: process.env.VITE_N8N_API_URL || 'https://your-n8n-instance.com/api/v1',
  },
  
  // Supabase Database Integration
  supabase: {
    url: process.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co',
    anonKey: process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key',
  },
  
  // SymbioWave Ecosystem APIs
  ecosystem: {
    symbioCore: process.env.VITE_SYMBIO_CORE_API || 'https://api.symbiocore.com',
    symbioXchange: process.env.VITE_SYMBIO_XCHANGE_API || 'https://api.symbioxchange.com',
    symbioEdge: process.env.VITE_SYMBIO_EDGE_API || 'https://api.symbioedge.com',
    symbioImpact: process.env.VITE_SYMBIO_IMPACT_API || 'https://api.symbioimpact.com',
  },
  
  // Feature Flags
  features: {
    aciOptimization: true,
    realTimeMonitoring: true,
    holographicDepth: true,
    quantumEntanglement: true,
  },
  
  // UI Configuration
  ui: {
    defaultTheme: 'bio-quantum',
    animationDuration: 233, // quantum-medium
    morphicTransitions: true,
    anticipatoryDelay: 150,
  }
};

export default config;
