import React, { useState } from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Button from '../components/atoms/Button';
import Card from '../components/atoms/Card';
import { ArrowRight, CheckCircle, Brain, BarChart3, Zap, Shield, Target, Clock } from 'lucide-react';

const AIReadinessAudit: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [isCompleted, setIsCompleted] = useState(false);

  const questions = [
    {
      id: 'current_automation',
      title: 'Current Automation Level',
      question: 'How would you describe your current automation capabilities?',
      type: 'radio',
      options: [
        { value: 'none', label: 'No automation - All processes are manual' },
        { value: 'basic', label: 'Basic automation - Simple rule-based systems' },
        { value: 'intermediate', label: 'Intermediate - Some workflow automation tools' },
        { value: 'advanced', label: 'Advanced - Comprehensive automation platform' }
      ]
    },
    {
      id: 'data_infrastructure',
      title: 'Data Infrastructure',
      question: 'How would you rate your data collection and management capabilities?',
      type: 'radio',
      options: [
        { value: 'limited', label: 'Limited - Data scattered across systems' },
        { value: 'basic', label: 'Basic - Some centralized data storage' },
        { value: 'good', label: 'Good - Well-organized data warehouse' },
        { value: 'excellent', label: 'Excellent - Real-time data pipeline' }
      ]
    },
    {
      id: 'team_readiness',
      title: 'Team Readiness',
      question: 'How prepared is your team for AI implementation?',
      type: 'radio',
      options: [
        { value: 'beginner', label: 'Beginner - Limited AI/automation experience' },
        { value: 'learning', label: 'Learning - Some team members have basic knowledge' },
        { value: 'competent', label: 'Competent - Several team members are AI-literate' },
        { value: 'expert', label: 'Expert - Strong AI/ML capabilities in-house' }
      ]
    },
    {
      id: 'business_goals',
      title: 'Primary Business Goals',
      question: 'What are your main objectives for AI automation?',
      type: 'checkbox',
      options: [
        { value: 'cost_reduction', label: 'Cost reduction and efficiency gains' },
        { value: 'quality_improvement', label: 'Quality improvement and error reduction' },
        { value: 'scalability', label: 'Scalability and growth enablement' },
        { value: 'innovation', label: 'Innovation and competitive advantage' },
        { value: 'compliance', label: 'Compliance and risk management' }
      ]
    },
    {
      id: 'timeline',
      title: 'Implementation Timeline',
      question: 'What is your preferred timeline for AI implementation?',
      type: 'radio',
      options: [
        { value: 'immediate', label: 'Immediate (1-3 months)' },
        { value: 'short', label: 'Short-term (3-6 months)' },
        { value: 'medium', label: 'Medium-term (6-12 months)' },
        { value: 'long', label: 'Long-term (12+ months)' }
      ]
    }
  ];

  const handleAnswer = (questionId: string, value: any) => {
    setAnswers(prev => ({ ...prev, [questionId]: value }));
  };

  const handleNext = () => {
    if (currentStep < questions.length) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsCompleted(true);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const calculateReadinessScore = () => {
    let score = 0;
    
    // Current automation level scoring
    const automation = answers.current_automation;
    if (automation === 'advanced') score += 25;
    else if (automation === 'intermediate') score += 18;
    else if (automation === 'basic') score += 10;
    else score += 5;

    // Data infrastructure scoring
    const data = answers.data_infrastructure;
    if (data === 'excellent') score += 25;
    else if (data === 'good') score += 18;
    else if (data === 'basic') score += 10;
    else score += 5;

    // Team readiness scoring
    const team = answers.team_readiness;
    if (team === 'expert') score += 25;
    else if (team === 'competent') score += 18;
    else if (team === 'learning') score += 10;
    else score += 5;

    // Timeline scoring
    const timeline = answers.timeline;
    if (timeline === 'immediate') score += 25;
    else if (timeline === 'short') score += 20;
    else if (timeline === 'medium') score += 15;
    else score += 10;

    return Math.min(score, 100);
  };

  const getReadinessLevel = (score: number) => {
    if (score >= 80) return { level: 'Excellent', color: 'harmony', description: 'Your organization is ready for advanced AI implementation' };
    if (score >= 60) return { level: 'Good', color: 'consciousness', description: 'You have a solid foundation for AI adoption' };
    if (score >= 40) return { level: 'Moderate', color: 'creativity', description: 'Some preparation needed before AI implementation' };
    return { level: 'Basic', color: 'secondary', description: 'Significant preparation required for AI readiness' };
  };

  if (isCompleted) {
    const score = calculateReadinessScore();
    const readiness = getReadinessLevel(score);

    return (
      <div className="min-h-screen surface-void">
        <Header />
        <main className="pt-32 pb-20">
          <div className="container mx-auto px-6">
            <div className="max-w-4xl mx-auto text-center">
              <div className="mb-12">
                <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-harmony/20 flex items-center justify-center">
                  <CheckCircle className="w-12 h-12 text-harmony" />
                </div>
                
                <Typography variant="3xl" weight="bold" color="consciousness" className="mb-4">
                  Your AI Readiness Assessment
                </Typography>
                
                <Typography variant="lg" color="secondary" className="mb-8">
                  Based on your responses, here's your personalized AI readiness report
                </Typography>
              </div>

              {/* Score Display */}
              <Card variant="quantum" className="p-8 mb-8 border-consciousness/30">
                <div className="text-center mb-6">
                  <div className="relative w-32 h-32 mx-auto mb-4">
                    <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                      <circle
                        cx="60"
                        cy="60"
                        r="50"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="none"
                        className="text-abyssal-base"
                      />
                      <circle
                        cx="60"
                        cy="60"
                        r="50"
                        stroke="currentColor"
                        strokeWidth="8"
                        fill="none"
                        strokeDasharray={`${(score / 100) * 314} 314`}
                        className={`text-${readiness.color}`}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Typography variant="2xl" weight="bold" color={readiness.color as any}>
                        {score}%
                      </Typography>
                    </div>
                  </div>
                  
                  <Typography variant="xl" weight="bold" color={readiness.color as any} className="mb-2">
                    {readiness.level} Readiness
                  </Typography>
                  
                  <Typography variant="sm" color="secondary">
                    {readiness.description}
                  </Typography>
                </div>
              </Card>

              {/* Recommendations */}
              <div className="grid md:grid-cols-2 gap-8 mb-12">
                <Card variant="neural" className="p-6 border-harmony/20 text-left">
                  <Brain className="w-8 h-8 text-harmony mb-4" />
                  <Typography variant="lg" weight="bold" color="harmony" className="mb-3">
                    Recommended Next Steps
                  </Typography>
                  <div className="space-y-2">
                    {score >= 80 ? (
                      <>
                        <Typography variant="sm" color="secondary">• Schedule advanced AI implementation consultation</Typography>
                        <Typography variant="sm" color="secondary">• Begin pilot project planning</Typography>
                        <Typography variant="sm" color="secondary">• Explore enterprise-grade solutions</Typography>
                      </>
                    ) : score >= 60 ? (
                      <>
                        <Typography variant="sm" color="secondary">• Strengthen data infrastructure</Typography>
                        <Typography variant="sm" color="secondary">• Provide team AI training</Typography>
                        <Typography variant="sm" color="secondary">• Start with basic automation</Typography>
                      </>
                    ) : (
                      <>
                        <Typography variant="sm" color="secondary">• Assess current data quality</Typography>
                        <Typography variant="sm" color="secondary">• Build foundational automation</Typography>
                        <Typography variant="sm" color="secondary">• Develop AI strategy roadmap</Typography>
                      </>
                    )}
                  </div>
                </Card>

                <Card variant="neural" className="p-6 border-consciousness/20 text-left">
                  <Target className="w-8 h-8 text-consciousness mb-4" />
                  <Typography variant="lg" weight="bold" color="consciousness" className="mb-3">
                    Estimated Timeline
                  </Typography>
                  <div className="space-y-2">
                    {score >= 80 ? (
                      <>
                        <Typography variant="sm" color="secondary">• Implementation: 1-3 months</Typography>
                        <Typography variant="sm" color="secondary">• ROI realization: 3-6 months</Typography>
                        <Typography variant="sm" color="secondary">• Full optimization: 6-12 months</Typography>
                      </>
                    ) : score >= 60 ? (
                      <>
                        <Typography variant="sm" color="secondary">• Preparation: 2-4 months</Typography>
                        <Typography variant="sm" color="secondary">• Implementation: 3-6 months</Typography>
                        <Typography variant="sm" color="secondary">• ROI realization: 6-12 months</Typography>
                      </>
                    ) : (
                      <>
                        <Typography variant="sm" color="secondary">• Foundation building: 3-6 months</Typography>
                        <Typography variant="sm" color="secondary">• Implementation: 6-12 months</Typography>
                        <Typography variant="sm" color="secondary">• ROI realization: 12-18 months</Typography>
                      </>
                    )}
                  </div>
                </Card>
              </div>

              {/* CTAs */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button 
                  variant="quantum" 
                  size="lg"
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                  className="rounded-[20px]"
                  onClick={() => window.location.href = '/request-demo'}
                >
                  Schedule Expert Consultation
                </Button>
                
                <Button 
                  variant="outline-quantum" 
                  size="lg"
                  className="rounded-[20px]"
                  onClick={() => window.location.href = '/symbioautomate'}
                >
                  Explore SymbioAutomate
                </Button>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const currentQuestion = questions[currentStep - 1];
  const isAnswered = answers[currentQuestion.id] !== undefined;

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-32 pb-20">
        <div className="container mx-auto px-6">
          <div className="max-w-3xl mx-auto">
            {/* Progress Bar */}
            <div className="mb-12">
              <div className="flex items-center justify-between mb-4">
                <Typography variant="sm" color="secondary">
                  Question {currentStep} of {questions.length}
                </Typography>
                <Typography variant="sm" color="secondary">
                  {Math.round((currentStep / questions.length) * 100)}% Complete
                </Typography>
              </div>
              <div className="w-full bg-abyssal-base rounded-full h-2">
                <div 
                  className="bg-consciousness h-2 rounded-full transition-all duration-500"
                  style={{ width: `${(currentStep / questions.length) * 100}%` }}
                />
              </div>
            </div>

            {/* Question Card */}
            <Card variant="quantum" className="p-8 border-consciousness/30 mb-8">
              <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                {currentQuestion.title}
              </Typography>
              
              <Typography variant="xl" weight="bold" color="primary" className="mb-8">
                {currentQuestion.question}
              </Typography>

              <div className="space-y-4">
                {currentQuestion.type === 'radio' ? (
                  currentQuestion.options.map((option) => (
                    <label key={option.value} className="flex items-start space-x-3 cursor-pointer group">
                      <input
                        type="radio"
                        name={currentQuestion.id}
                        value={option.value}
                        checked={answers[currentQuestion.id] === option.value}
                        onChange={(e) => handleAnswer(currentQuestion.id, e.target.value)}
                        className="mt-1 w-4 h-4 text-consciousness bg-abyssal-base border-consciousness/30 focus:ring-consciousness/50"
                      />
                      <Typography variant="sm" color="secondary" className="group-hover:text-primary transition-colors">
                        {option.label}
                      </Typography>
                    </label>
                  ))
                ) : (
                  currentQuestion.options.map((option) => (
                    <label key={option.value} className="flex items-start space-x-3 cursor-pointer group">
                      <input
                        type="checkbox"
                        value={option.value}
                        checked={answers[currentQuestion.id]?.includes(option.value) || false}
                        onChange={(e) => {
                          const currentAnswers = answers[currentQuestion.id] || [];
                          if (e.target.checked) {
                            handleAnswer(currentQuestion.id, [...currentAnswers, option.value]);
                          } else {
                            handleAnswer(currentQuestion.id, currentAnswers.filter((v: string) => v !== option.value));
                          }
                        }}
                        className="mt-1 w-4 h-4 text-consciousness bg-abyssal-base border-consciousness/30 focus:ring-consciousness/50"
                      />
                      <Typography variant="sm" color="secondary" className="group-hover:text-primary transition-colors">
                        {option.label}
                      </Typography>
                    </label>
                  ))
                )}
              </div>
            </Card>

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Button
                variant="outline-quantum"
                size="lg"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="rounded-[12px]"
              >
                Previous
              </Button>

              <Button
                variant="quantum"
                size="lg"
                onClick={handleNext}
                disabled={!isAnswered}
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="rounded-[12px]"
              >
                {currentStep === questions.length ? 'Complete Assessment' : 'Next Question'}
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AIReadinessAudit;
