
import React, { useEffect, useRef } from 'react';
import Advanced3DVisualization from './Advanced3DVisualization';

interface AutomationVisualizationProps {
  isActive: boolean;
}

const AutomationVisualization: React.FC<AutomationVisualizationProps> = ({ isActive }) => {
  return (
    <div className="w-full h-full relative">
      <Advanced3DVisualization 
        isActive={isActive}
        variant="automation"
        intensity="intense"
        className="w-full h-full"
      />
      
      {/* Additional automation-specific overlays */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Process flow indicators */}
        <div className="absolute top-4 left-4 flex space-x-2">
          <div className="w-3 h-3 rounded-full bg-consciousness animate-pulse" />
          <div className="w-3 h-3 rounded-full bg-harmony animate-pulse" style={{ animationDelay: '0.5s' }} />
          <div className="w-3 h-3 rounded-full bg-creativity animate-pulse" style={{ animationDelay: '1s' }} />
        </div>
        
        {/* Data flow visualization */}
        <div className="absolute bottom-4 right-4 text-xs text-white/60 font-mono">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 rounded-full bg-consciousness animate-quantum-flicker" />
            <span>Processing: 1.2M ops/sec</span>
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <div className="w-2 h-2 rounded-full bg-harmony animate-consciousness-wave" />
            <span>Efficiency: 98.7%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutomationVisualization;
