import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from '../components/organisms/Header';
import MorphicContainer from '../components/atoms/MorphicContainer';
import { cn } from '@/lib/utils';

interface MainAppLayoutProps {
  children?: React.ReactNode;
}

const MainAppLayout: React.FC<MainAppLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-abyssal-void w-full relative">
      {/* Quantum Background Effects - Lowest z-index */}
      <div className="fixed inset-0 z-[-1]">
        <div className="absolute inset-0 bg-gradient-to-br from-abyssal-void via-abyssal-base to-abyssal-deep" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_30%,rgba(0,229,255,0.08)_0%,transparent_60%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_70%,rgba(0,255,127,0.06)_0%,transparent_50%)]" />
      </div>

      {/* Header - Highest z-index */}
      <Header />

      {/* Main Content - Proper spacing and z-index */}
      <main className="relative z-[10] pt-20 lg:pt-24">
        <MorphicContainer
          variant="surface"
          className="min-h-[calc(100vh-5rem)] lg:min-h-[calc(100vh-6rem)] w-full p-0"
        >
          <div className="w-full max-w-none overflow-x-hidden p-6">
            {children || <Outlet />}
          </div>
        </MorphicContainer>
      </main>
    </div>
  );
};

export default MainAppLayout;