
import React from 'react';
import { useIsMobile } from '../hooks/use-mobile';
import Typography from '../components/atoms/Typography';
import MorphicContainer from '../components/atoms/MorphicContainer';
import DashboardMetrics from '../features/symbio-automate/dashboard/DashboardMetrics';
import ActiveWorkflows from '../features/symbio-automate/dashboard/ActiveWorkflows';
import ACIInsights from '../features/symbio-automate/dashboard/ACIInsights';
import RecentActivity from '../features/symbio-automate/dashboard/RecentActivity';
import { Brain, Zap } from 'lucide-react';

const SymbioAutomateDashboard: React.FC = () => {
  const isMobile = useIsMobile();

  return (
    <div className="min-h-screen bg-abyssal-void p-4 sm:p-6 lg:p-8 w-full">
      {/* Header Section */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-2">
            <Typography 
              variant={isMobile ? "2xl" : "3xl"} 
              weight="bold" 
              gradient="consciousness"
              className="leading-tight"
            >
              SymbioAutomate Command Center
            </Typography>
            <Typography 
              variant={isMobile ? "sm" : "lg"} 
              color="secondary"
              className="leading-relaxed"
            >
              Orchestrate Intelligent Process Automation with ACI
            </Typography>
          </div>
          
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4">
            <div className="flex items-center gap-2 px-3 py-2 rounded-organic bg-consciousness-500/10 border border-consciousness-500/20">
              <Brain className="text-consciousness-500 animate-quantum-flicker" size={isMobile ? 16 : 18} />
              <Typography variant="xs" color="consciousness" weight="medium">
                ACI Active
              </Typography>
            </div>
            <div className="flex items-center gap-2 px-3 py-2 rounded-organic bg-symbioautomate-500/10 border border-symbioautomate-500/20">
              <Zap className="text-symbioautomate-500" size={isMobile ? 16 : 18} />
              <Typography variant="xs" color="symbioautomate" weight="medium">
                12 Workflows Running
              </Typography>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 lg:gap-8">
        {/* Metrics Section - Full width on mobile, spans 8 cols on desktop */}
        <div className="lg:col-span-8 space-y-4 sm:space-y-6">
          <DashboardMetrics />
          <ActiveWorkflows />
        </div>
        
        {/* Sidebar Section - Full width on mobile, spans 4 cols on desktop */}
        <div className="lg:col-span-4 space-y-4 sm:space-y-6">
          <ACIInsights />
          <RecentActivity />
        </div>
      </div>
    </div>
  );
};

export default SymbioAutomateDashboard;
