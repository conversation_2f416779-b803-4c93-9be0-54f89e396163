
import React from 'react';
import { useIsMobile } from '../../../hooks/use-mobile';
import MorphicContainer from '../../../components/atoms/MorphicContainer';
import Typography from '../../../components/atoms/Typography';
import { TrendingUp, Zap, DollarSign, Leaf, Cpu, Target } from 'lucide-react';

const DashboardMetrics: React.FC = () => {
  const isMobile = useIsMobile();

  const metrics = [
    {
      id: 'efficiency',
      label: 'Automation Efficiency',
      value: '87%',
      change: '+12%',
      trend: 'up',
      icon: TrendingUp,
      color: 'symbioautomate'
    },
    {
      id: 'cost-savings',
      label: 'Cost Optimization',
      value: '$42.3K',
      change: '+23%',
      trend: 'up',
      icon: DollarSign,
      color: 'consciousness'
    },
    {
      id: 'carbon-reduction',
      label: 'CO₂ Reduction',
      value: '2.8T',
      change: '+8%',
      trend: 'up',
      icon: Leaf,
      color: 'symbioimpact'
    },
    {
      id: 'aci-optimizations',
      label: 'ACI Optimizations',
      value: '156',
      change: '+34%',
      trend: 'up',
      icon: Cpu,
      color: 'consciousness'
    },
    {
      id: 'workflow-accuracy',
      label: 'Process Accuracy',
      value: '99.2%',
      change: '****%',
      trend: 'up',
      icon: Target,
      color: 'symbioedge'
    },
    {
      id: 'energy-savings',
      label: 'Energy Efficiency',
      value: '15.4%',
      change: '+5%',
      trend: 'up',
      icon: Zap,
      color: 'symbioautomate'
    }
  ];

  return (
    <MorphicContainer
      variant="card"
      division="symbioautomate"
      className="p-4 sm:p-6"
    >
      <div className="flex items-center gap-3 mb-4 sm:mb-6">
        <TrendingUp className="text-consciousness-500" size={isMobile ? 18 : 20} />
        <Typography 
          variant={isMobile ? "base" : "lg"} 
          weight="semibold" 
          color="consciousness"
        >
          Performance Metrics
        </Typography>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
        {metrics.map((metric) => (
          <div
            key={metric.id}
            className="p-3 sm:p-4 rounded-cellular bg-consciousness-500/5 border border-consciousness-500/20 hover:border-consciousness-500/40 transition-all duration-medium"
          >
            <div className="flex items-start justify-between mb-2">
              <metric.icon 
                size={isMobile ? 16 : 20} 
                className={`text-${metric.color}-500 flex-shrink-0`}
              />
              <span 
                className={`text-xs px-2 py-1 rounded-full bg-${metric.color}-500/20 text-${metric.color}-500 font-medium`}
              >
                {metric.change}
              </span>
            </div>
            
            <div className="space-y-1">
              <Typography 
                variant={isMobile ? "lg" : "xl"} 
                weight="bold" 
                color={metric.color as any}
              >
                {metric.value}
              </Typography>
              <Typography 
                variant="xs" 
                color="secondary" 
                className="leading-tight"
              >
                {metric.label}
              </Typography>
            </div>
          </div>
        ))}
      </div>
    </MorphicContainer>
  );
};

export default DashboardMetrics;
