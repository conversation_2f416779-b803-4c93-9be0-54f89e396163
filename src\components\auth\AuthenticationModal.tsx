import React, { useState, useEffect } from 'react';
import { X, Lock, User, Eye, EyeOff, Zap, Shield, Chrome, Atom, Orbit } from 'lucide-react';
import Typography from '../atoms/Typography';
import Button from '../atoms/Button';
import Card from '../atoms/Card';

interface AuthenticationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (userData: any) => void;
}

const AuthenticationModal: React.FC<AuthenticationModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    companyName: '',
    fullName: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate authentication
    setTimeout(() => {
      const userData = {
        id: '1',
        email: formData.email,
        fullName: formData.fullName || 'User',
        companyName: formData.companyName || 'Company',
        accessLevel: 'premium',
        joinedDate: new Date().toISOString()
      };
      
      localStorage.setItem('symbiowave_user', JSON.stringify(userData));
      onSuccess(userData);
      setIsLoading(false);
    }, 2000);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleGoogleAuth = async () => {
    setIsGoogleLoading(true);
    
    // Simulate Google OAuth flow
    setTimeout(() => {
      const userData = {
        id: 'google_' + Math.random().toString(36).substr(2, 9),
        email: '<EMAIL>',
        fullName: 'Google User',
        companyName: 'Tech Company',
        accessLevel: 'premium',
        joinedDate: new Date().toISOString(),
        authProvider: 'google',
        avatar: 'https://via.placeholder.com/100'
      };
      
      localStorage.setItem('symbiowave_user', JSON.stringify(userData));
      onSuccess(userData);
      setIsGoogleLoading(false);
    }, 1500);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[200000] flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Card variant="neural" className="relative w-full max-w-md mx-4 p-8 border-blue-400/40">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 rounded-full hover:bg-blue-500/20 transition-colors"
        >
          <X className="w-5 h-5 text-blue-400" />
        </button>

        {/* Background Effects */}
        <div className="absolute inset-0 opacity-20 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl animate-pulse" />
          <div className="absolute bottom-1/4 right-1/4 w-32 h-32 bg-purple-500/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }} />
          <div className="absolute top-3/4 left-3/4 w-24 h-24 bg-pink-500/10 rounded-full blur-xl animate-pulse" style={{ animationDelay: '2s' }} />
        </div>

        {/* Header */}
        <div className="text-center mb-8 relative z-10">
          {/* Logo */}
          <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center border-2 border-blue-400/30 relative group hover:scale-110 transition-all duration-500">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            <Typography variant="xl" weight="bold" className="text-white font-mono relative z-10">
              SW
            </Typography>
          </div>
          
          <Typography variant="2xl" weight="bold" className="mb-2 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
            {isLogin ? 'Welcome Back' : 'Join SymbioWave'}
          </Typography>
          
          <Typography variant="sm" color="secondary">
            {isLogin 
              ? 'Sign in to access your automation dashboard' 
              : 'Create your account to get started'
            }
          </Typography>
        </div>

        {/* Google Authentication */}
        <div className="mb-6 relative z-10">
          <Button
            type="button"
            onClick={handleGoogleAuth}
            disabled={isGoogleLoading}
            className="w-full bg-white hover:bg-gray-100 text-gray-900 border border-gray-300 rounded-[12px] py-3 px-4 transition-all duration-300 hover:scale-105 group"
          >
            {isGoogleLoading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-5 h-5 border-2 border-gray-400 border-t-gray-900 rounded-full animate-spin" />
                <span className="font-medium">Connecting to Google...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-3">
                <div className="relative">
                  <Chrome className="w-5 h-5 text-blue-500 group-hover:scale-110 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <span className="font-medium tracking-wide">Continue with Google</span>
                <Zap className="w-4 h-4 text-blue-500 animate-pulse opacity-60" />
              </div>
            )}
          </Button>
          
          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600/20" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gray-900 text-gray-400">
                OR MANUAL ENTRY
              </span>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {!isLogin && (
            <>
              <div>
                <label className="block text-sm font-medium text-blue-400 mb-2">
                  Full Name
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-400/60" />
                  <input
                    type="text"
                    required
                    value={formData.fullName}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-gray-900 border border-blue-400/30 rounded-[12px] text-white placeholder-gray-400 focus:border-blue-400/60 focus:outline-none transition-colors"
                    placeholder="Enter your full name"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-blue-400 mb-2">
                  Company Name
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-400/60" />
                  <input
                    type="text"
                    required
                    value={formData.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 bg-gray-900 border border-blue-400/30 rounded-[12px] text-white placeholder-gray-400 focus:border-blue-400/60 focus:outline-none transition-colors"
                    placeholder="Enter your company name"
                  />
                </div>
              </div>
            </>
          )}

          <div>
            <label className="block text-sm font-medium text-blue-400 mb-2">
              Email Address
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-400/60" />
              <input
                type="email"
                required
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-gray-900 border border-blue-400/30 rounded-[12px] text-white placeholder-gray-400 focus:border-blue-400/60 focus:outline-none transition-colors"
                placeholder="Enter your email"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-blue-400 mb-2">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-400/60" />
              <input
                type={showPassword ? 'text' : 'password'}
                required
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="w-full pl-10 pr-12 py-3 bg-gray-900 border border-blue-400/30 rounded-[12px] text-white placeholder-gray-400 focus:border-blue-400/60 focus:outline-none transition-colors"
                placeholder="Enter your password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-400/60 hover:text-blue-400 transition-colors"
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {!isLogin && (
            <div>
              <label className="block text-sm font-medium text-blue-400 mb-2">
                Confirm Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-400/60" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-gray-900 border border-blue-400/30 rounded-[12px] text-white placeholder-gray-400 focus:border-blue-400/60 focus:outline-none transition-colors"
                  placeholder="Confirm your password"
                />
              </div>
            </div>
          )}

          <Button
            type="submit"
            variant="quantum"
            size="lg"
            className="w-full rounded-[12px]"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                <span>Authenticating...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5" />
                <span>{isLogin ? 'Access Interface' : 'Create Account'}</span>
              </div>
            )}
          </Button>
        </form>

        {/* Toggle */}
        <div className="mt-6 text-center">
          <Typography variant="sm" color="secondary">
            {isLogin ? "Don't have an account?" : "Already have an account?"}
          </Typography>
          <button
            onClick={() => setIsLogin(!isLogin)}
            className="ml-2 text-blue-400 hover:text-blue-300 transition-colors font-medium"
          >
            {isLogin ? 'Sign Up' : 'Sign In'}
          </button>
        </div>

        {/* Demo Credentials */}
        {isLogin && (
          <div className="mt-6 p-4 bg-blue-500/10 rounded-[12px] border border-blue-400/20">
            <Typography variant="xs" className="mb-2 font-semibold text-blue-400">
              Demo Credentials:
            </Typography>
            <Typography variant="xs" color="secondary">
              Email: <EMAIL><br />
              Password: demo123
            </Typography>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AuthenticationModal;
