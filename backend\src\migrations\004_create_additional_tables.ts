import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Create workflow_metrics table
  await knex.schema.createTable('workflow_metrics', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('workflow_id').notNullable().references('id').inTable('workflows').onDelete('CASCADE');
    table.integer('execution_count').defaultTo(0);
    table.decimal('avg_execution_time', 10, 2).defaultTo(0);
    table.decimal('success_rate', 5, 2).defaultTo(0);
    table.decimal('resources_saved', 15, 2).defaultTo(0);
    table.decimal('co2_reduction', 10, 2).defaultTo(0);
    table.decimal('cost_optimization', 15, 2).defaultTo(0);
    table.integer('aci_optimizations').defaultTo(0);
    table.timestamp('last_calculated').defaultTo(knex.fn.now());
    table.timestamps(true, true);

    table.index(['workflow_id']);
    table.index(['last_calculated']);
  });

  // Create ai_readiness_assessments table
  await knex.schema.createTable('ai_readiness_assessments', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.jsonb('responses').notNullable();
    table.integer('score').notNullable();
    table.enum('readiness_level', ['basic', 'moderate', 'good', 'excellent']).notNullable();
    table.jsonb('recommendations').defaultTo('[]');
    table.string('estimated_timeline');
    table.timestamps(true, true);

    table.index(['user_id']);
    table.index(['score']);
    table.index(['readiness_level']);
    table.index(['created_at']);
  });

  // Create demo_requests table
  await knex.schema.createTable('demo_requests', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('full_name').notNullable();
    table.string('email').notNullable();
    table.string('company').notNullable();
    table.string('role').notNullable();
    table.string('phone');
    table.string('employees');
    table.string('industry').notNullable();
    table.text('challenges');
    table.string('timeline');
    table.enum('status', ['pending', 'contacted', 'scheduled', 'completed', 'cancelled']).defaultTo('pending');
    table.timestamp('scheduled_at');
    table.string('assigned_to');
    table.text('notes');
    table.timestamps(true, true);

    table.index(['email']);
    table.index(['status']);
    table.index(['industry']);
    table.index(['created_at']);
  });

  // Create chat_sessions table
  await knex.schema.createTable('chat_sessions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('title').notNullable();
    table.string('context').defaultTo('symbio-automate');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);

    table.index(['user_id']);
    table.index(['is_active']);
    table.index(['created_at']);
  });

  // Create chat_messages table
  await knex.schema.createTable('chat_messages', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('session_id').notNullable().references('id').inTable('chat_sessions').onDelete('CASCADE');
    table.enum('type', ['user', 'aci', 'system']).notNullable();
    table.text('content').notNullable();
    table.jsonb('metadata');
    table.timestamps(true, true);

    table.index(['session_id']);
    table.index(['type']);
    table.index(['created_at']);
  });

  // Create integrations table
  await knex.schema.createTable('integrations', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('name').notNullable();
    table.enum('type', ['symbiowave', 'external', 'n8n', 'zapier', 'webhook']).notNullable();
    table.enum('division', ['symbiocore', 'symbioxchange', 'symbioedge', 'symbioimpact']);
    table.jsonb('config').defaultTo('{}');
    table.jsonb('credentials').defaultTo('{}');
    table.enum('status', ['connected', 'disconnected', 'error', 'pending']).defaultTo('pending');
    table.timestamp('last_sync');
    table.timestamps(true, true);

    table.index(['user_id']);
    table.index(['type']);
    table.index(['status']);
    table.index(['division']);
  });

  // Create api_keys table
  await knex.schema.createTable('api_keys', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE');
    table.string('name').notNullable();
    table.string('key_hash').notNullable();
    table.jsonb('permissions').defaultTo('[]');
    table.timestamp('last_used');
    table.timestamp('expires_at');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);

    table.index(['user_id']);
    table.index(['key_hash']);
    table.index(['is_active']);
    table.index(['expires_at']);
  });

  // Create audit_logs table
  await knex.schema.createTable('audit_logs', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('user_id').references('id').inTable('users').onDelete('SET NULL');
    table.string('action').notNullable();
    table.string('resource_type').notNullable();
    table.string('resource_id');
    table.jsonb('details').defaultTo('{}');
    table.string('ip_address');
    table.text('user_agent');
    table.timestamps(true, true);

    table.index(['user_id']);
    table.index(['action']);
    table.index(['resource_type']);
    table.index(['created_at']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('audit_logs');
  await knex.schema.dropTable('api_keys');
  await knex.schema.dropTable('integrations');
  await knex.schema.dropTable('chat_messages');
  await knex.schema.dropTable('chat_sessions');
  await knex.schema.dropTable('demo_requests');
  await knex.schema.dropTable('ai_readiness_assessments');
  await knex.schema.dropTable('workflow_metrics');
}
