
import React from 'react';
import { ArrowRight, Zap } from 'lucide-react';
import Typography from '../atoms/Typography';
import Button from '../atoms/Button';
import Card from '../atoms/Card';

const CallToAction: React.FC = () => {
  return (
    <section className="py-32 relative">
      {/* Dynamic Background */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute top-1/3 left-1/4 w-80 h-80 bg-consciousness/10 rounded-cellular blur-3xl animate-consciousness-wave"></div>
        <div className="absolute bottom-1/3 right-1/4 w-60 h-60 bg-harmony/12 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
        <div className="absolute top-2/3 left-2/3 w-40 h-40 bg-creativity/8 rounded-organic blur-xl animate-quantum-flicker"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <Card 
          variant="quantum" 
          className="text-center max-w-5xl mx-auto border-consciousness/30 relative overflow-hidden"
        >
          {/* Glow Effects */}
          <div className="absolute top-0 left-1/4 w-32 h-32 bg-consciousness/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-24 h-24 bg-harmony/15 rounded-full blur-2xl"></div>

          <div className="relative z-10">
            {/* Icon */}
            <div className="w-20 h-20 mx-auto mb-8 rounded-quantum bg-consciousness/20 flex items-center justify-center animate-consciousness-wave">
              <Zap className="w-10 h-10 text-consciousness" />
            </div>

            {/* Headline */}
            <Typography 
              as="h2" 
              variant="3xl" 
              weight="bold" 
              gradient="quantum"
              align="center"
              className="mb-6"
            >
              Join the Symbiotic Revolution
            </Typography>

            {/* Description */}
            <Typography 
              as="p" 
              variant="lg" 
              color="secondary" 
              align="center"
              className="mb-8 max-w-3xl mx-auto leading-relaxed"
            >
              We are not just building a billion-dollar company; we are architecting the future 
              of industry. The transition to symbiotic intelligence is happening now, and the 
              leaders of tomorrow are being defined today.
            </Typography>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-8">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-6 h-6" />}
                className="group relative overflow-hidden"
              >
                <span className="relative z-10">Start Your Transformation</span>
                <div className="absolute inset-0 bg-gradient-to-r from-consciousness/20 to-harmony/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-bio origin-left"></div>
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="group"
              >
                <span className="group-hover:text-consciousness transition-colors duration-quantum">
                  Request Partnership Discussion
                </span>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="grid md:grid-cols-3 gap-8 pt-8 border-t border-subtle">
              <div className="text-center">
                <Typography variant="sm" weight="semibold" color="consciousness" className="mb-1">
                  AI Ethics Board
                </Typography>
                <Typography variant="xs" color="tertiary">
                  Responsible AI Development
                </Typography>
              </div>
              <div className="text-center">
                <Typography variant="sm" weight="semibold" color="harmony" className="mb-1">
                  First-Mover Advantage
                </Typography>
                <Typography variant="xs" color="tertiary">
                  Defining the Category
                </Typography>
              </div>
              <div className="text-center">
                <Typography variant="sm" weight="semibold" color="creativity" className="mb-1">
                  Proprietary ACI
                </Typography>
                <Typography variant="xs" color="tertiary">
                  Unmatched Technology Moat
                </Typography>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default CallToAction;
