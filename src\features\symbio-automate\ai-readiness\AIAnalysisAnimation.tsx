
import React, { useEffect, useState } from 'react';
import Typography from '../../../components/atoms/Typography';
import Card from '../../../components/atoms/Card';
import { Brain, Cpu, Network, Zap } from 'lucide-react';

interface Props {
  onComplete: () => void;
}

const AIAnalysisAnimation: React.FC<Props> = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const analysisSteps = [
    {
      icon: Brain,
      title: "Analyzing Business Context",
      description: "ACI processes your industry patterns and organizational structure"
    },
    {
      icon: Network,
      title: "Mapping Process Dependencies",
      description: "Identifying workflow connections and optimization opportunities"
    },
    {
      icon: Cpu,
      title: "Running Physarum Optimizer",
      description: "Bio-inspired algorithms finding optimal automation pathways"
    },
    {
      icon: Zap,
      title: "Generating Recommendations",
      description: "Synthesizing personalized AI tool and workflow suggestions"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(onComplete, 1000);
          return 100;
        }
        return prev + 2;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [onComplete]);

  useEffect(() => {
    const stepInterval = setInterval(() => {
      setCurrentStep(prev => {
        if (prev < analysisSteps.length - 1) {
          return prev + 1;
        }
        clearInterval(stepInterval);
        return prev;
      });
    }, 1250);

    return () => clearInterval(stepInterval);
  }, []);

  return (
    <Card variant="quantum" className="p-12 text-center max-w-2xl mx-auto">
      <div className="mb-8">
        <Typography 
          as="h2" 
          variant="2xl" 
          weight="bold" 
          color="consciousness"
          className="mb-4"
        >
          ACI Analysis in Progress
        </Typography>
        <Typography 
          variant="sm" 
          color="secondary"
          className="mb-8"
        >
          Our Artificial Cellular Intelligence is analyzing your responses to create 
          personalized recommendations tailored to your business needs.
        </Typography>
      </div>

      {/* Progress Bar */}
      <div className="mb-12">
        <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
          <div 
            className="bg-gradient-to-r from-consciousness to-symbioautomate h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
        <Typography variant="sm" color="consciousness" weight="medium">
          {Math.round(progress)}% Complete
        </Typography>
      </div>

      {/* Current Analysis Step */}
      <div className="space-y-6">
        {analysisSteps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;
          
          return (
            <div 
              key={index}
              className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-500 ${
                isActive ? 'bg-consciousness/10 border border-consciousness/30' : 
                isCompleted ? 'bg-green-500/10 border border-green-500/30' : 
                'bg-gray-800/50'
              }`}
            >
              <div className={`w-12 h-12 rounded-cellular flex items-center justify-center ${
                isActive ? 'bg-consciousness/20 animate-pulse' :
                isCompleted ? 'bg-green-500/20' :
                'bg-gray-700'
              }`}>
                <Icon className={`w-6 h-6 ${
                  isActive ? 'text-consciousness' :
                  isCompleted ? 'text-green-400' :
                  'text-gray-500'
                }`} />
              </div>
              
              <div className="text-left flex-1">
                <Typography 
                  variant="sm" 
                  weight="semibold" 
                  color={isActive ? 'consciousness' : isCompleted ? 'quantum' : 'tertiary'}
                  className="mb-1"
                >
                  {step.title}
                </Typography>
                <Typography 
                  variant="xs" 
                  color={isActive ? 'secondary' : 'quaternary'}
                >
                  {step.description}
                </Typography>
              </div>
              
              {isCompleted && (
                <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Floating Particles Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-consciousness/30 rounded-full animate-pulse"
            style={{
              left: `${20 + i * 12}%`,
              top: `${30 + (i % 2) * 40}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: '2s'
            }}
          />
        ))}
      </div>
    </Card>
  );
};

export default AIAnalysisAnimation;
