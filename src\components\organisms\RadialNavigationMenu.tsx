
import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Heart, 
  Microscope, 
  ArrowRightLeft, 
  Cog, 
  Cpu, 
  Shield, 
  TrendingUp, 
  Globe,
  ChevronDown 
} from 'lucide-react';
import Typography from '../atoms/Typography';
import { cn } from '@/lib/utils';

interface Division {
  name: string;
  tagline: string;
  icon: React.ComponentType<any>;
  color: string;
  description: string;
  link: string;
  status: 'active' | 'coming-soon';
  angle: number;
}

const RadialNavigationMenu: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredDivision, setHoveredDivision] = useState<string | null>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const divisions: Division[] = [
    {
      name: 'SymbioCore',
      tagline: 'The Heart of Intelligence',
      icon: Heart,
      color: 'consciousness',
      description: 'Cloud-based PaaS hosting the full suite of ACI models',
      link: '/symbiocore',
      status: 'coming-soon',
      angle: 0
    },
    {
      name: 'SymbioLabs',
      tagline: 'The Engine of Innovation',
      icon: Microscope,
      color: 'creativity',
      description: 'R&D division advancing ACI frontiers',
      link: '/symbiolabs',
      status: 'coming-soon',
      angle: 45
    },
    {
      name: 'SymbioXchange',
      tagline: 'The Symbiosis Marketplace',
      icon: ArrowRightLeft,
      color: 'intuition',
      description: 'AI-driven B2B marketplace for circular economy',
      link: '/symbioxchange',
      status: 'coming-soon',
      angle: 90
    },
    {
      name: 'SymbioAutomate',
      tagline: 'The Efficiency Engine',
      icon: Cog,
      color: 'harmony',
      description: 'Intelligent process automation platform',
      link: '/symbioautomate',
      status: 'active',
      angle: 135
    },
    {
      name: 'SymbioEdge',
      tagline: 'Intelligence at the Frontier',
      icon: Cpu,
      color: 'transcendence',
      description: 'Lightweight ACI for edge devices',
      link: '/symbioedge',
      status: 'coming-soon',
      angle: 180
    },
    {
      name: 'SymbioImpact',
      tagline: 'The Conscience of the Ecosystem',
      icon: Shield,
      color: 'symbioimpact',
      description: 'ESG technology and advisory platform',
      link: '/symbioimpact',
      status: 'coming-soon',
      angle: 225
    },
    {
      name: 'SymbioVentures',
      tagline: 'The Catalyst for Growth',
      icon: TrendingUp,
      color: 'symbioventures',
      description: 'Corporate venture capital arm',
      link: '/symbioventures',
      status: 'coming-soon',
      angle: 270
    },
    {
      name: 'SymbioAlliance',
      tagline: 'The Global Scaling Engine',
      icon: Globe,
      color: 'symbioalliance',
      description: 'Strategic partnerships for global adoption',
      link: '/symbioalliance',
      status: 'coming-soon',
      angle: 315
    }
  ];

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    if (isOpen) {
      document.addEventListener('mousemove', handleMouseMove);
      return () => document.removeEventListener('mousemove', handleMouseMove);
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  const handleDivisionClick = (link: string) => {
    setIsOpen(false);
    navigate(link);
  };

  const getRadialPosition = (angle: number, radius: number) => {
    const radian = (angle * Math.PI) / 180;
    return {
      x: Math.cos(radian) * radius,
      y: Math.sin(radian) * radius
    };
  };

  return (
    <div className="relative" ref={menuRef}>
      {/* Trigger Button */}
      <button
        className={cn(
          "flex items-center space-x-2 px-4 py-3 rounded-[16px] transition-all duration-[400ms] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] group",
          "hover:bg-consciousness/10 hover:backdrop-blur-xl",
          "focus:outline-none focus:ring-2 focus:ring-consciousness/50",
          isOpen && "bg-consciousness/15 backdrop-blur-xl"
        )}
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setHoveredDivision('trigger')}
        onMouseLeave={() => setHoveredDivision(null)}
      >
        <div className={cn(
          "w-6 h-6 rounded-full bg-harmony/20 flex items-center justify-center transition-all duration-[300ms]",
          "group-hover:bg-harmony/30 group-hover:scale-110",
          isOpen && "bg-harmony/40 scale-110"
        )}>
          <Cog className={cn(
            "w-4 h-4 text-harmony transition-all duration-[300ms]",
            "group-hover:rotate-90",
            isOpen && "rotate-180"
          )} />
        </div>
        <Typography variant="sm" weight="semibold" color="secondary" className="group-hover:text-consciousness transition-colors duration-[300ms]">
          Divisions
        </Typography>
        <ChevronDown className={cn(
          "w-4 h-4 transition-all duration-[300ms] text-secondary group-hover:text-consciousness",
          isOpen && "rotate-180"
        )} />
        
        {/* Anticipatory Glow */}
        <div className={cn(
          "absolute -inset-2 rounded-[20px] bg-gradient-to-r from-consciousness/20 via-harmony/15 to-consciousness/20",
          "opacity-0 transition-opacity duration-[500ms] pointer-events-none",
          hoveredDivision === 'trigger' && "opacity-100"
        )} />
      </button>

      {/* Radial Menu */}
      <div className={cn(
        "absolute top-full left-1/2 transform -translate-x-1/2 mt-4 transition-all duration-[600ms] ease-[cubic-bezier(0.25,0.1,0.25,1.0)]",
        isOpen ? "opacity-100 visible scale-100 translate-y-0" : "opacity-0 invisible scale-75 translate-y-8"
      )}>
        <div className="relative w-[400px] h-[400px]">
          {/* Central Hub */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
            <div className={cn(
              "w-16 h-16 rounded-full glass-quantum border-2 border-consciousness/30",
              "flex items-center justify-center backdrop-blur-xl",
              "transition-all duration-[400ms] ease-[cubic-bezier(0.25,0.1,0.25,1.0)]",
              hoveredDivision && "border-consciousness/60 bg-consciousness/10 scale-110"
            )}>
              <div className="w-8 h-8 rounded-full bg-consciousness/20 flex items-center justify-center">
                <div className="w-4 h-4 rounded-full bg-consciousness animate-sentient-pulse" />
              </div>
            </div>
            
            {/* Energy Conduits */}
            {divisions.map((division, index) => (
              <div
                key={division.name}
                className={cn(
                  "absolute top-1/2 left-1/2 origin-center transition-all duration-[400ms]",
                  hoveredDivision === division.name ? "opacity-100 scale-100" : "opacity-30 scale-90"
                )}
                style={{
                  transform: `translate(-50%, -50%) rotate(${division.angle}deg) translateX(120px) rotate(-${division.angle}deg)`,
                  transitionDelay: `${index * 50}ms`
                }}
              >
                <div className={cn(
                  "w-1 h-24 bg-gradient-to-t opacity-60 transition-all duration-[300ms]",
                  `from-${division.color}/40 to-${division.color}/10`,
                  hoveredDivision === division.name && "opacity-100 scale-y-110"
                )} />
              </div>
            ))}
          </div>

          {/* Division Nodes */}
          {divisions.map((division, index) => {
            const IconComponent = division.icon;
            const position = getRadialPosition(division.angle, 150);
            
            return (
              <div
                key={division.name}
                className={cn(
                  "absolute top-1/2 left-1/2 transition-all duration-[500ms] ease-[cubic-bezier(0.25,0.1,0.25,1.0)] cursor-pointer",
                  "hover:scale-125 hover:-translate-y-2",
                  hoveredDivision === division.name && "z-30"
                )}
                style={{
                  transform: `translate(-50%, -50%) translate(${position.x}px, ${position.y}px)`,
                  transitionDelay: `${index * 80}ms`
                }}
                onClick={() => handleDivisionClick(division.link)}
                onMouseEnter={() => setHoveredDivision(division.name)}
                onMouseLeave={() => setHoveredDivision(null)}
              >
                {/* Node Container */}
                <div className={cn(
                  "relative w-20 h-20 rounded-cellular glass-quantum border transition-all duration-[400ms]",
                  `border-${division.color}/40 hover:border-${division.color}/80`,
                  "backdrop-blur-xl group"
                )}>
                  {/* Icon */}
                  <div className={cn(
                    "w-full h-full flex items-center justify-center transition-all duration-[300ms]",
                    "group-hover:scale-110"
                  )}>
                    <IconComponent className={cn(
                      "w-8 h-8 transition-all duration-[300ms]",
                      `text-${division.color} group-hover:text-${division.color}`
                    )} />
                  </div>

                  {/* Status Indicator */}
                  <div className={cn(
                    "absolute -top-1 -right-1 w-6 h-6 rounded-full border-2 border-abyssal-base",
                    "flex items-center justify-center text-xs font-bold transition-all duration-[300ms]",
                    division.status === 'active' 
                      ? "bg-harmony text-abyssal-void animate-pulse" 
                      : "bg-consciousness/30 text-consciousness"
                  )}>
                    {division.status === 'active' ? '✦' : '•'}
                  </div>

                  {/* Quantum Glow */}
                  <div className={cn(
                    "absolute -inset-2 rounded-cellular transition-all duration-[400ms] pointer-events-none",
                    `bg-gradient-to-br from-${division.color}/20 to-transparent`,
                    "opacity-0 group-hover:opacity-100"
                  )} />
                </div>

                {/* Division Info Popup */}
                <div className={cn(
                  "absolute top-full left-1/2 transform -translate-x-1/2 mt-4 w-64 p-4",
                  "glass-quantum border border-consciousness/25 rounded-[16px] backdrop-blur-xl",
                  "transition-all duration-[300ms] ease-[cubic-bezier(0.25,0.1,0.25,1.0)]",
                  hoveredDivision === division.name 
                    ? "opacity-100 visible translate-y-0" 
                    : "opacity-0 invisible translate-y-4 pointer-events-none"
                )}>
                  <Typography variant="sm" weight="bold" color={division.color as any} className="mb-1">
                    {division.name}
                  </Typography>
                  <Typography variant="xs" color="secondary" className="mb-2 italic">
                    {division.tagline}
                  </Typography>
                  <Typography variant="xs" color="tertiary" className="leading-relaxed">
                    {division.description}
                  </Typography>
                  
                  {division.status === 'active' && (
                    <div className="mt-3 flex items-center space-x-2">
                      <div className="w-2 h-2 bg-harmony rounded-full animate-pulse" />
                      <Typography variant="micro" color="harmony" weight="bold">
                        ACTIVE DIVISION
                      </Typography>
                    </div>
                  )}
                </div>
              </div>
            );
          })}

          {/* Background Quantum Field */}
          <div className={cn(
            "absolute inset-0 rounded-full border border-consciousness/10 transition-all duration-[600ms]",
            "bg-gradient-radial from-consciousness/5 via-transparent to-transparent",
            isOpen && "animate-morphic-pulse"
          )} />
        </div>
      </div>

      {/* Global Overlay */}
      <div className={cn(
        "fixed inset-0 bg-abyssal-void/20 backdrop-blur-sm transition-opacity duration-[400ms] pointer-events-none",
        isOpen ? "opacity-100" : "opacity-0"
      )} />
    </div>
  );
};

export default RadialNavigationMenu;
