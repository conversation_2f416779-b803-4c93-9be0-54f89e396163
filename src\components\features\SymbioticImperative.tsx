
import React from 'react';
import { TrendingDown, Clock, AlertTriangle, RotateCcw } from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';

const SymbioticImperative: React.FC = () => {
  const problems = [
    {
      icon: TrendingDown,
      title: "The Inefficiency of Isolation",
      description: "Businesses operate in silos. A factory pays to dispose of excess heat while a greenhouse next door pays to generate it.",
      stat: "25% of trucking miles are 'empty miles'",
      color: "consciousness"
    },
    {
      icon: Clock,
      title: "The Ticking Clock of Sustainability", 
      description: "EU's CBAM, SEC climate disclosure rules, and $40T+ in ESG-managed assets make sustainability a financial necessity.",
      stat: "70% increase in waste by 2050",
      color: "creativity"
    },
    {
      icon: AlertTriangle,
      title: "The Inadequacy of Current Technology",
      description: "Traditional AI relies on static data. ERP systems are backward-looking. Consultancies lack implementation backbone.",
      stat: "Most automation fails at complexity",
      color: "intuition"
    }
  ];

  return (
    <section className="py-32 relative">
      {/* Organic Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-10 w-32 h-32 bg-consciousness/10 rounded-cellular blur-xl animate-consciousness-wave"></div>
        <div className="absolute bottom-1/4 right-10 w-24 h-24 bg-creativity/10 rounded-biomorphic blur-lg animate-neural-pulse"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Typography 
            as="h2" 
            variant="4xl" 
            weight="bold" 
            gradient="neural"
            align="center"
            className="mb-6"
          >
            The Symbiotic Imperative
          </Typography>
          
          <Typography 
            as="p" 
            variant="xl" 
            color="secondary" 
            align="center"
            className="mb-6"
          >
            A Global Market Failure & A Trillion-Dollar Opportunity
          </Typography>

          <Typography 
            as="p" 
            variant="lg" 
            color="tertiary" 
            align="center"
            className="max-w-4xl mx-auto leading-relaxed"
          >
            The industrial model of the 20th century—<strong className="text-consciousness">Take, Make, Dispose</strong>—is 
            fundamentally broken. This linear paradigm has created unprecedented wealth but at the cost of 
            staggering inefficiency and environmental degradation.
          </Typography>
        </div>

        {/* Problem Breakdown */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {problems.map((problem, index) => {
            const IconComponent = problem.icon;
            return (
              <Card 
                key={problem.title}
                variant="neural"
                className={`group text-center hover:border-${problem.color}/50 transition-all duration-bio hover:transform hover:scale-105 hover:-translate-y-2`}
              >
                <div className={`w-20 h-20 mx-auto mb-6 rounded-organic bg-${problem.color}/10 flex items-center justify-center group-hover:bg-${problem.color}/20 transition-colors duration-quantum`}>
                  <IconComponent className={`w-10 h-10 text-${problem.color}`} />
                </div>

                <Typography 
                  variant="xl" 
                  weight="semibold" 
                  className={`mb-4 text-${problem.color}`}
                >
                  {problem.title}
                </Typography>

                <Typography 
                  variant="sm" 
                  color="secondary"
                  className="mb-6 leading-relaxed"
                >
                  {problem.description}
                </Typography>

                <div className={`p-3 glass-quantum rounded-lg border border-${problem.color}/20`}>
                  <Typography 
                    variant="sm" 
                    weight="bold" 
                    className={`text-${problem.color}`}
                  >
                    {problem.stat}
                  </Typography>
                </div>
              </Card>
            );
          })}
        </div>

        {/* The Solution - Symbiotic Transformation */}
        <div className="text-center">
          <Card 
            variant="quantum" 
            className="max-w-4xl mx-auto p-12 border-consciousness/30"
          >
            <div className="w-24 h-24 mx-auto mb-8 rounded-quantum bg-consciousness/20 flex items-center justify-center animate-consciousness-wave">
              <RotateCcw className="w-12 h-12 text-consciousness" />
            </div>

            <Typography 
              as="h3" 
              variant="2xl" 
              weight="bold" 
              gradient="quantum"
              align="center"
              className="mb-6"
            >
              The SymbioWave Thesis: From Linear to Circular
            </Typography>

            <Typography 
              as="p" 
              variant="lg" 
              color="secondary" 
              align="center"
              className="mb-8 leading-relaxed"
            >
              Our core thesis is that the future of competitive advantage and sustainable growth lies in 
              <strong className="text-consciousness"> intelligent symbiosis</strong>. The solution is not to make 
              isolated linear processes slightly more efficient, but to weave them into a resilient, 
              adaptive, and circular ecosystem.
            </Typography>

            <div className="grid md:grid-cols-2 gap-8 text-left">
              <div>
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-3">
                  What We Replace:
                </Typography>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-creativity/60 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <Typography variant="sm" color="tertiary">Siloed, static operations</Typography>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-creativity/60 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <Typography variant="sm" color="tertiary">Waste as liability</Typography>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-creativity/60 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <Typography variant="sm" color="tertiary">Rigid, backward-looking systems</Typography>
                  </li>
                </ul>
              </div>

              <div>
                <Typography variant="lg" weight="semibold" color="harmony" className="mb-3">
                  What We Create:
                </Typography>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-harmony/80 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <Typography variant="sm" color="secondary">Connected, dynamic networks</Typography>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-harmony/80 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <Typography variant="sm" color="secondary">Waste as tradable asset</Typography>
                  </li>
                  <li className="flex items-start">
                    <span className="w-2 h-2 bg-harmony/80 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                    <Typography variant="sm" color="secondary">Self-optimizing ecosystems</Typography>
                  </li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default SymbioticImperative;
