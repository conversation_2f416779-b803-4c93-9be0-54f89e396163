{"name": "symbio-wave-backend", "version": "1.0.0", "description": "Professional backend for SymbioWave Genesis platform", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "knex migrate:latest", "migrate:rollback": "knex migrate:rollback", "seed": "knex seed:run", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "knex": "^3.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0", "uuid": "^9.0.1", "axios": "^1.6.2", "socket.io": "^4.7.4", "cron": "^3.1.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "express-session": "^1.17.3", "connect-redis": "^7.1.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/joi": "^17.2.3", "@types/pg": "^8.10.7", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-google-oauth20": "^2.0.14", "@types/express-session": "^1.17.10", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["symbiowave", "automation", "ai", "workflow", "backend", "api"], "author": "SymbioWave Team", "license": "MIT"}