import React, { useEffect, useRef, useState } from 'react';

interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  color: string;
  size: number;
  type: 'cellular' | 'dna' | 'chemical' | 'neural';
  phase: number;
  amplitude: number;
  baseX: number;
  baseY: number;
  frequency: number;
  brightness: number;
  baseBrightness: number;
}

interface ACIParticleSystemProps {
  mousePosition: { x: number; y: number };
  isHovering?: boolean;
}

const ACIParticleSystem: React.FC<ACIParticleSystemProps> = ({ mousePosition, isHovering = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationRef = useRef<number>();
  const mousePositionRef = useRef(mousePosition);
  const isHoveringRef = useRef(isHovering);

  const colors = [
    'rgba(0, 255, 170, 0.6)', // consciousness - primary cellular
    'rgba(0, 153, 255, 0.5)', // creativity - neural  
    'rgba(0, 255, 119, 0.4)', // harmony - chemical
    'rgba(170, 0, 255, 0.3)', // intuition - DNA
  ];

  const particleTypes: Array<Particle['type']> = ['cellular', 'dna', 'chemical', 'neural'];

  // Update refs when props change
  useEffect(() => {
    mousePositionRef.current = mousePosition;
  }, [mousePosition]);

  useEffect(() => {
    isHoveringRef.current = isHovering;
  }, [isHovering]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const devicePixelRatio = window.devicePixelRatio || 1;
    let canvasWidth = 0;
    let canvasHeight = 0;

    // Initialize particles with cosmic stillness
    const initParticles = () => {
      particlesRef.current = [];
      for (let i = 0; i < 60; i++) { // Reduced particle count for subtlety
        const x = Math.random() * canvasWidth;
        const y = Math.random() * canvasHeight;
        particlesRef.current.push({
          x,
          y,
          baseX: x,
          baseY: y,
          vx: (Math.random() - 0.5) * 0.02, // Ultra-slow velocity
          vy: (Math.random() - 0.5) * 0.02,
          life: Math.random() * 100,
          maxLife: 500 + Math.random() * 300,
          color: colors[Math.floor(Math.random() * colors.length)],
          size: Math.random() * 0.8 + 0.4, // Smaller, subtler particles
          type: particleTypes[Math.floor(Math.random() * particleTypes.length)],
          phase: Math.random() * Math.PI * 2,
          amplitude: Math.random() * 2 + 1, // Much smaller amplitude
          frequency: Math.random() * 0.001 + 0.0005, // Ultra-slow frequency
          brightness: 0.3 + Math.random() * 0.3,
          baseBrightness: 0.3 + Math.random() * 0.3,
        });
      }
    };

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvasWidth = rect.width;
      canvasHeight = rect.height;
      canvas.width = rect.width * devicePixelRatio;
      canvas.height = rect.height * devicePixelRatio;
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';
      ctx.scale(devicePixelRatio, devicePixelRatio);
      
      // Force re-initialization after resize
      if (canvasWidth > 0 && canvasHeight > 0) {
        initParticles();
      }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const updateParticles = () => {
      const particles = particlesRef.current;
      const mouseX = mousePositionRef.current.x * canvasWidth;
      const mouseY = mousePositionRef.current.y * canvasHeight;
      const time = Date.now() * 0.0001; // Ultra-slow time progression

      particles.forEach((particle, index) => {
        // Gentle mouse interaction - localized ripple effect
        if (isHoveringRef.current) {
          const dx = mouseX - particle.x;
          const dy = mouseY - particle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 150) { // Reduced interaction radius
            const force = Math.max(0, (150 - distance) / 150);
            const easedForce = force * force * (3 - 2 * force); // Smooth easing
            
            // Gentle brightness increase
            particle.brightness = Math.min(1, particle.baseBrightness + easedForce * 0.4);
            
            // Subtle positional offset instead of velocity change
            const offsetStrength = easedForce * 8;
            particle.x = particle.baseX + (dx / distance) * offsetStrength * Math.sin(time * 2 + particle.phase);
            particle.y = particle.baseY + (dy / distance) * offsetStrength * Math.cos(time * 2 + particle.phase);
          } else {
            // Gradual return to baseline
            particle.brightness = particle.brightness * 0.98 + particle.baseBrightness * 0.02;
            particle.x = particle.x * 0.95 + particle.baseX * 0.05;
            particle.y = particle.y * 0.95 + particle.baseY * 0.05;
          }
        } else {
          // Natural return to base state
          particle.brightness = particle.brightness * 0.99 + particle.baseBrightness * 0.01;
          particle.x = particle.x * 0.98 + particle.baseX * 0.02;
          particle.y = particle.y * 0.98 + particle.baseY * 0.02;
        }

        // Cosmic drift patterns - ultra-subtle
        switch (particle.type) {
          case 'cellular':
            // Gentle membrane-like oscillation
            const cellularOffset = Math.sin(time * particle.frequency + particle.phase) * particle.amplitude * 0.3;
            if (!isHoveringRef.current || Math.sqrt(Math.pow(mouseX - particle.baseX, 2) + Math.pow(mouseY - particle.baseY, 2)) > 150) {
              particle.x += cellularOffset * 0.1;
              particle.y += Math.cos(time * particle.frequency + particle.phase) * particle.amplitude * 0.2;
            }
            break;
            
          case 'dna':
            // Slow double helix pattern
            const helixTime = time * 0.3;
            if (!isHoveringRef.current || Math.sqrt(Math.pow(mouseX - particle.baseX, 2) + Math.pow(mouseY - particle.baseY, 2)) > 150) {
              particle.x += Math.sin(helixTime + particle.phase) * 0.3;
              particle.y += Math.sin(helixTime * 2 + particle.phase) * 0.2;
            }
            break;
            
          case 'chemical':
            // Very subtle Brownian motion
            if (!isHoveringRef.current || Math.sqrt(Math.pow(mouseX - particle.baseX, 2) + Math.pow(mouseY - particle.baseY, 2)) > 150) {
              particle.x += (Math.random() - 0.5) * 0.1;
              particle.y += (Math.random() - 0.5) * 0.1;
            }
            break;
            
          case 'neural':
            // Gentle synapse pulses
            const neuralPulse = Math.sin(time * 0.5 + particle.phase) * 0.2;
            if (!isHoveringRef.current || Math.sqrt(Math.pow(mouseX - particle.baseX, 2) + Math.pow(mouseY - particle.baseY, 2)) > 150) {
              particle.x += neuralPulse * 0.2;
              particle.y += Math.cos(time * 0.3 + particle.phase) * 0.15;
            }
            break;
        }

        // Boundary wrapping with smooth transitions
        if (particle.baseX < -50) particle.baseX = canvasWidth + 50;
        if (particle.baseX > canvasWidth + 50) particle.baseX = -50;
        if (particle.baseY < -50) particle.baseY = canvasHeight + 50;
        if (particle.baseY > canvasHeight + 50) particle.baseY = -50;

        // Ultra-slow life cycle
        particle.life += 0.02;
        if (particle.life > particle.maxLife) {
          particle.life = 0;
          particle.baseX = particle.x = Math.random() * canvasWidth;
          particle.baseY = particle.y = Math.random() * canvasHeight;
          particle.phase = Math.random() * Math.PI * 2;
          particle.baseBrightness = 0.3 + Math.random() * 0.3;
          particle.brightness = particle.baseBrightness;
        }
      });
    };

    const drawParticles = () => {
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      const particles = particlesRef.current;
      const time = Date.now() * 0.0001;

      // Subtle connections between nearby particles
      particles.forEach((particle1, i) => {
        particles.slice(i + 1).forEach(particle2 => {
          const dx = particle1.x - particle2.x;
          const dy = particle1.y - particle2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 80) { // Reduced connection distance
            const opacity = (1 - distance / 80) * 0.15 * Math.min(particle1.brightness, particle2.brightness);
            
            ctx.strokeStyle = `rgba(0, 255, 170, ${opacity})`;
            ctx.lineWidth = 0.5;
            ctx.setLineDash([]);

            ctx.beginPath();
            ctx.moveTo(particle1.x, particle1.y);
            ctx.lineTo(particle2.x, particle2.y);
            ctx.stroke();

            // Subtle data flow animation
            if (opacity > 0.05) {
              const progress = (Math.sin(time * 0.5 + distance * 0.1) + 1) / 2;
              const flowX = particle1.x + (particle2.x - particle1.x) * progress;
              const flowY = particle1.y + (particle2.y - particle1.y) * progress;

              ctx.fillStyle = `rgba(255, 255, 255, ${opacity * 0.8})`;
              ctx.beginPath();
              ctx.arc(flowX, flowY, 0.5, 0, Math.PI * 2);
              ctx.fill();
            }
          }
        });
      });

      // Draw particles with cosmic serenity
      particles.forEach((particle, index) => {
        const alpha = (1 - (particle.life / particle.maxLife)) * particle.brightness;
        const radius = particle.size * alpha * 0.8;
        const pulseMultiplier = Math.sin(time * 0.2 + particle.phase) * 0.1 + 1;
        
        ctx.globalAlpha = alpha * 0.7;

        // Type-specific rendering with subtle variations
        switch (particle.type) {
          case 'cellular':
            ctx.shadowBlur = 12;
            ctx.shadowColor = particle.color;
            
            const cellGradient = ctx.createRadialGradient(
              particle.x, particle.y, 0,
              particle.x, particle.y, radius * pulseMultiplier * 2
            );
            cellGradient.addColorStop(0, particle.color.replace(/[\d\.]+\)$/g, `${alpha * 0.8})`));
            cellGradient.addColorStop(0.7, particle.color.replace(/[\d\.]+\)$/g, `${alpha * 0.3})`));
            cellGradient.addColorStop(1, 'transparent');
            
            ctx.fillStyle = cellGradient;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, radius * pulseMultiplier, 0, Math.PI * 2);
            ctx.fill();
            break;
            
          case 'dna':
            ctx.shadowBlur = 15;
            ctx.shadowColor = particle.color;
            
            const helixOffset = Math.sin(time * 0.1 + particle.phase) * 1;
            
            ctx.fillStyle = particle.color.replace(/[\d\.]+\)$/g, `${alpha * 0.6})`);
            ctx.beginPath();
            ctx.arc(particle.x + helixOffset, particle.y, radius * 0.8, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(particle.x - helixOffset, particle.y, radius * 0.8, 0, Math.PI * 2);
            ctx.fill();
            break;
            
          case 'chemical':
            const reactionIntensity = Math.sin(time * 0.3 + particle.phase) * 0.2 + 0.8;
            
            ctx.shadowBlur = 20;
            ctx.shadowColor = particle.color;
            
            const chemGradient = ctx.createRadialGradient(
              particle.x, particle.y, 0,
              particle.x, particle.y, radius * reactionIntensity * 1.5
            );
            chemGradient.addColorStop(0, particle.color.replace(/[\d\.]+\)$/g, `${alpha * 0.7})`));
            chemGradient.addColorStop(1, 'transparent');
            
            ctx.fillStyle = chemGradient;
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, radius * reactionIntensity, 0, Math.PI * 2);
            ctx.fill();
            break;
            
          case 'neural':
            const spike = Math.max(0, Math.sin(time * 0.4 + particle.phase) * 0.3 + 0.7);
            
            ctx.shadowBlur = 10;
            ctx.shadowColor = particle.color;
            
            ctx.fillStyle = particle.color.replace(/[\d\.]+\)$/g, `${alpha * spike * 0.8})`);
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, radius * spike, 0, Math.PI * 2);
            ctx.fill();
            break;
            
          default:
            ctx.shadowBlur = 8;
            ctx.shadowColor = particle.color;
            ctx.fillStyle = particle.color.replace(/[\d\.]+\)$/g, `${alpha * 0.6})`);
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, radius, 0, Math.PI * 2);
            ctx.fill();
            break;
        }

        ctx.shadowBlur = 0;
      });
      
      ctx.globalAlpha = 1;
    };

    const animate = () => {
      updateParticles();
      drawParticles();
      animationRef.current = requestAnimationFrame(animate);
    };

    // Initialize after canvas is properly sized
    const initTimeout = setTimeout(() => {
      if (canvasWidth > 0 && canvasHeight > 0) {
        initParticles();
        animate();
      }
    }, 100);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      clearTimeout(initTimeout);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = undefined;
      }
    };
  }, []); // Empty dependency array - runs only once!

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full pointer-events-none"
      style={{ zIndex: 1 }}
    />
  );
};

export default ACIParticleSystem;