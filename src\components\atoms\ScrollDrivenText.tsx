
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import Typography from './Typography';

interface ScrollDrivenTextProps {
  children: string;
  variant?: 'quantum' | 'cosmic' | '5xl' | '4xl' | '3xl' | '2xl' | 'xl' | 'lg' | 'md' | 'base' | 'sm' | 'xs' | 'micro' | 'nano';
  color?: string;
  className?: string;
  delay?: number;
  stagger?: number;
  effect?: 'materialize' | 'lightTrace' | 'quantumReveal';
}

const ScrollDrivenText: React.FC<ScrollDrivenTextProps> = ({
  children,
  variant = 'base',
  color = 'primary',
  className,
  delay = 0,
  stagger = 50,
  effect = 'materialize'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [revealedChars, setRevealedChars] = useState(0);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (textRef.current) {
      observer.observe(textRef.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (isVisible && effect === 'materialize') {
      const timer = setTimeout(() => {
        const interval = setInterval(() => {
          setRevealedChars(prev => {
            if (prev >= children.length) {
              clearInterval(interval);
              return children.length;
            }
            return prev + 1;
          });
        }, stagger);
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [isVisible, children.length, delay, stagger, effect]);

  const renderEffect = () => {
    switch (effect) {
      case 'materialize':
        return (
          <span>
            {children.split('').map((char, index) => (
              <span
                key={index}
                className={cn(
                  "inline-block transition-all duration-300 ease-out",
                  index < revealedChars 
                    ? "opacity-100 transform translate-y-0" 
                    : "opacity-0 transform translate-y-4"
                )}
                style={{
                  transitionDelay: `${index * (stagger / 10)}ms`
                }}
              >
                {char === ' ' ? '\u00A0' : char}
              </span>
            ))}
          </span>
        );

      case 'lightTrace':
        return (
          <span className="relative">
            {children}
            <span
              className={cn(
                "absolute inset-0 bg-gradient-to-r from-transparent via-consciousness/60 to-transparent",
                "transition-all duration-1000 ease-out",
                isVisible ? "translate-x-full opacity-0" : "-translate-x-full opacity-100"
              )}
              style={{ transitionDelay: `${delay}ms` }}
            />
          </span>
        );

      case 'quantumReveal':
        return (
          <span
            className={cn(
              "relative transition-all duration-800 ease-out",
              isVisible ? "opacity-100 blur-none" : "opacity-0 blur-sm"
            )}
            style={{ transitionDelay: `${delay}ms` }}
          >
            {children}
            <span
              className={cn(
                "absolute inset-0 bg-consciousness/20 transition-all duration-600",
                isVisible ? "scale-x-0 opacity-0" : "scale-x-100 opacity-100"
              )}
              style={{ 
                transformOrigin: 'left',
                transitionDelay: `${delay + 200}ms` 
              }}
            />
          </span>
        );

      default:
        return children;
    }
  };

  return (
    <div ref={textRef} className={cn("relative", className)}>
      <Typography
        variant={variant}
        color={color as any}
      >
        {renderEffect()}
      </Typography>
    </div>
  );
};

export default ScrollDrivenText;
