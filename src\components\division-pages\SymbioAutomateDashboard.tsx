
import React, { useState } from 'react';
import { Activity, TrendingUp, Zap, Users, Clock, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';
import Button from '../atoms/Button';

const SymbioAutomateDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const metrics = [
    { label: 'Active Workflows', value: '247', change: '+12%', icon: <Activity className="w-5 h-5" />, color: 'consciousness' as const },
    { label: 'Tasks Automated', value: '15,892', change: '+28%', icon: <Zap className="w-5 h-5" />, color: 'harmony' as const },
    { label: 'Hours Saved', value: '2,340', change: '+35%', icon: <Clock className="w-5 h-5" />, color: 'creativity' as const },
    { label: 'Success Rate', value: '98.7%', change: '+2.1%', icon: <CheckCircle className="w-5 h-5" />, color: 'consciousness' as const }
  ];

  const recentActivities = [
    { type: 'success', message: 'Lead qualification workflow completed', time: '2 minutes ago' },
    { type: 'warning', message: 'Email campaign requires approval', time: '5 minutes ago' },
    { type: 'success', message: 'Data sync with CRM completed', time: '10 minutes ago' },
    { type: 'info', message: 'New automation template available', time: '15 minutes ago' }
  ];

  const workflows = [
    { name: 'Lead Qualification', status: 'Running', processed: 127, success: 98.4 },
    { name: 'Email Marketing', status: 'Paused', processed: 0, success: 0 },
    { name: 'Data Processing', status: 'Running', processed: 89, success: 100 },
    { name: 'Customer Onboarding', status: 'Running', processed: 45, success: 95.6 }
  ];

  return (
    <section className="py-20 bg-abyssal-base/30">
      <div className="container mx-auto px-8">
        <div className="text-center mb-16">
          <Typography 
            as="h2" 
            variant="3xl" 
            weight="bold" 
            color="consciousness"
            className="mb-4"
          >
            Live Automation Dashboard
          </Typography>
          <Typography 
            variant="lg" 
            color="secondary"
            className="max-w-2xl mx-auto"
          >
            Monitor, manage, and optimize your AI automation workflows in real-time
          </Typography>
        </div>

        {/* Metrics Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {metrics.map((metric, index) => (
            <Card key={metric.label} variant="neural" className="p-6 border-consciousness/20 rounded-[20px]">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-full bg-${metric.color}/15 flex items-center justify-center`}>
                  {metric.icon}
                </div>
                <div className="text-right">
                  <Typography variant="xs" color="tertiary" className="mb-1">
                    {metric.change}
                  </Typography>
                  <div className="w-2 h-2 bg-harmony rounded-full animate-pulse"></div>
                </div>
              </div>
              <Typography variant="2xl" weight="bold" color={metric.color} className="mb-2">
                {metric.value}
              </Typography>
              <Typography variant="sm" color="secondary">
                {metric.label}
              </Typography>
            </Card>
          ))}
        </div>

        {/* Dashboard Tabs */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-consciousness/5 p-1 rounded-[16px] w-fit">
            {['overview', 'workflows', 'analytics'].map((tab) => (
              <Button
                key={tab}
                variant={activeTab === tab ? 'quantum' : 'secondary'}
                size="sm"
                className="rounded-[12px] capitalize"
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        {/* Dashboard Content */}
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {activeTab === 'overview' && (
              <Card variant="neural" className="p-8 border-consciousness/20 rounded-[24px]">
                <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                  Active Workflows
                </Typography>
                <div className="space-y-4">
                  {workflows.map((workflow, index) => (
                    <div key={workflow.name} className="flex items-center justify-between p-4 bg-consciousness/5 rounded-[16px]">
                      <div className="flex items-center space-x-4">
                        <div className={`w-3 h-3 rounded-full ${workflow.status === 'Running' ? 'bg-harmony animate-pulse' : 'bg-tertiary'}`}></div>
                        <div>
                          <Typography variant="sm" weight="semibold" color="primary">
                            {workflow.name}
                          </Typography>
                          <Typography variant="xs" color="tertiary">
                            {workflow.status} • {workflow.processed} processed
                          </Typography>
                        </div>
                      </div>
                      <div className="text-right">
                        <Typography variant="sm" weight="semibold" color="harmony">
                          {workflow.success}%
                        </Typography>
                        <Typography variant="xs" color="tertiary">
                          Success Rate
                        </Typography>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {activeTab === 'workflows' && (
              <Card variant="neural" className="p-8 border-consciousness/20 rounded-[24px]">
                <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                  Workflow Management
                </Typography>
                <div className="flex flex-wrap gap-4 mb-6">
                  <Button variant="quantum" size="sm" className="rounded-[12px]">
                    Create Workflow
                  </Button>
                  <Button variant="outline-quantum" size="sm" className="rounded-[12px]">
                    Import Template
                  </Button>
                  <Button variant="secondary" size="sm" className="rounded-[12px]">
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </Button>
                </div>
                <Typography variant="sm" color="secondary">
                  Workflow builder and management tools will be displayed here
                </Typography>
              </Card>
            )}

            {activeTab === 'analytics' && (
              <Card variant="neural" className="p-8 border-consciousness/20 rounded-[24px]">
                <Typography variant="xl" weight="semibold" color="consciousness" className="mb-6">
                  Performance Analytics
                </Typography>
                <div className="h-64 bg-consciousness/5 rounded-[16px] flex items-center justify-center">
                  <Typography variant="sm" color="tertiary">
                    Analytics charts and reports will be displayed here
                  </Typography>
                </div>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Activity */}
            <Card variant="neural" className="p-6 border-consciousness/20 rounded-[20px]">
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Recent Activity
              </Typography>
              <div className="space-y-3">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      activity.type === 'success' ? 'bg-harmony' :
                      activity.type === 'warning' ? 'bg-creativity' :
                      'bg-consciousness'
                    }`}></div>
                    <div>
                      <Typography variant="xs" color="primary" className="mb-1">
                        {activity.message}
                      </Typography>
                      <Typography variant="xs" color="tertiary">
                        {activity.time}
                      </Typography>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Quick Actions */}
            <Card variant="neural" className="p-6 border-consciousness/20 rounded-[20px]">
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Quick Actions
              </Typography>
              <div className="space-y-3">
                <Button variant="outline-quantum" size="sm" className="w-full rounded-[12px]">
                  Schedule Workflow
                </Button>
                <Button variant="outline-quantum" size="sm" className="w-full rounded-[12px]">
                  View Reports
                </Button>
                <Button variant="outline-quantum" size="sm" className="w-full rounded-[12px]">
                  System Health
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SymbioAutomateDashboard;
