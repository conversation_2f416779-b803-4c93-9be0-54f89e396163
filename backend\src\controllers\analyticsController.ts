import { Request, Response, NextFunction } from 'express';
import db from '../utils/database';
import { AuthenticatedRequest, requireRole } from '../middleware/auth';
import { NotFoundError } from '../types';

export class AnalyticsController {
  async getDashboardMetrics(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const userRole = req.user!.role;

      let metrics: any = {};

      if (userRole === 'admin') {
        // Admin gets global metrics
        metrics = await this.getGlobalMetrics();
      } else {
        // Regular users get their own metrics
        metrics = await this.getUserMetrics(userId);
      }

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      next(error);
    }
  }

  async getUsageStatistics(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const { period = '30d' } = req.query;

      const stats = await this.calculateUsageStats(userId, period as string);

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  }

  async getWorkflowMetrics(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user!.id;

      // Verify workflow ownership
      const workflow = await db('workflows')
        .where({ id, user_id: userId })
        .first();

      if (!workflow) {
        throw new NotFoundError('Workflow not found');
      }

      const metrics = await this.getDetailedWorkflowMetrics(id);

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      next(error);
    }
  }

  private async getGlobalMetrics(): Promise<any> {
    const [
      totalUsers,
      totalWorkflows,
      totalExecutions,
      activeWorkflows,
    ] = await Promise.all([
      db('users').count('* as count').first(),
      db('workflows').count('* as count').first(),
      db('workflow_executions').count('* as count').first(),
      db('workflows').where({ is_active: true }).count('* as count').first(),
    ]);

    // Calculate success rate
    const [successfulExecutions] = await db('workflow_executions')
      .where({ status: 'success' })
      .count('* as count');

    const totalExecCount = Number(totalExecutions?.count || 0);
    const successCount = Number(successfulExecutions?.count || 0);
    const successRate = totalExecCount > 0 ? (successCount / totalExecCount) * 100 : 0;

    // Get recent activity
    const recentExecutions = await db('workflow_executions')
      .select('*')
      .orderBy('started_at', 'desc')
      .limit(10);

    return {
      overview: {
        total_users: Number(totalUsers?.count || 0),
        total_workflows: Number(totalWorkflows?.count || 0),
        total_executions: totalExecCount,
        active_workflows: Number(activeWorkflows?.count || 0),
        success_rate: Math.round(successRate),
      },
      recent_activity: recentExecutions,
      performance: {
        avg_execution_time: await this.calculateAverageExecutionTime(),
        resource_savings: await this.calculateResourceSavings(),
        co2_reduction: await this.calculateCO2Reduction(),
      },
    };
  }

  private async getUserMetrics(userId: string): Promise<any> {
    const [
      userWorkflows,
      userExecutions,
      activeWorkflows,
    ] = await Promise.all([
      db('workflows').where({ user_id: userId }).count('* as count').first(),
      db('workflow_executions')
        .join('workflows', 'workflow_executions.workflow_id', 'workflows.id')
        .where('workflows.user_id', userId)
        .count('* as count')
        .first(),
      db('workflows').where({ user_id: userId, is_active: true }).count('* as count').first(),
    ]);

    // Calculate user success rate
    const [successfulExecutions] = await db('workflow_executions')
      .join('workflows', 'workflow_executions.workflow_id', 'workflows.id')
      .where('workflows.user_id', userId)
      .where('workflow_executions.status', 'success')
      .count('* as count');

    const userExecCount = Number(userExecutions?.count || 0);
    const userSuccessCount = Number(successfulExecutions?.count || 0);
    const successRate = userExecCount > 0 ? (userSuccessCount / userExecCount) * 100 : 0;

    // Get user's recent executions
    const recentExecutions = await db('workflow_executions')
      .join('workflows', 'workflow_executions.workflow_id', 'workflows.id')
      .where('workflows.user_id', userId)
      .select('workflow_executions.*', 'workflows.name as workflow_name')
      .orderBy('workflow_executions.started_at', 'desc')
      .limit(10);

    return {
      overview: {
        total_workflows: Number(userWorkflows?.count || 0),
        total_executions: userExecCount,
        active_workflows: Number(activeWorkflows?.count || 0),
        success_rate: Math.round(successRate),
      },
      recent_activity: recentExecutions,
      performance: {
        avg_execution_time: await this.calculateUserAverageExecutionTime(userId),
        resource_savings: await this.calculateUserResourceSavings(userId),
        co2_reduction: await this.calculateUserCO2Reduction(userId),
      },
    };
  }

  private async calculateUsageStats(userId: string, period: string): Promise<any> {
    const days = this.parsePeriod(period);
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const executions = await db('workflow_executions')
      .join('workflows', 'workflow_executions.workflow_id', 'workflows.id')
      .where('workflows.user_id', userId)
      .where('workflow_executions.started_at', '>=', startDate)
      .select(
        db.raw('DATE(workflow_executions.started_at) as date'),
        db.raw('COUNT(*) as executions'),
        db.raw('AVG(workflow_executions.duration_ms) as avg_duration'),
        db.raw('SUM(CASE WHEN workflow_executions.status = \'success\' THEN 1 ELSE 0 END) as successful')
      )
      .groupBy(db.raw('DATE(workflow_executions.started_at)'))
      .orderBy('date');

    return {
      period,
      daily_stats: executions,
      summary: {
        total_executions: executions.reduce((sum: number, day: any) => sum + Number(day.executions), 0),
        avg_daily_executions: executions.length > 0 
          ? Math.round(executions.reduce((sum: number, day: any) => sum + Number(day.executions), 0) / executions.length)
          : 0,
        overall_success_rate: executions.length > 0
          ? Math.round((executions.reduce((sum: number, day: any) => sum + Number(day.successful), 0) / 
             executions.reduce((sum: number, day: any) => sum + Number(day.executions), 0)) * 100)
          : 0,
      },
    };
  }

  private async getDetailedWorkflowMetrics(workflowId: string): Promise<any> {
    const [executions, metrics] = await Promise.all([
      db('workflow_executions')
        .where({ workflow_id: workflowId })
        .select('*')
        .orderBy('started_at', 'desc')
        .limit(50),
      db('workflow_metrics')
        .where({ workflow_id: workflowId })
        .first(),
    ]);

    const successfulExecutions = executions.filter(e => e.status === 'success').length;
    const failedExecutions = executions.filter(e => e.status === 'failed').length;
    const avgDuration = executions.length > 0
      ? executions.reduce((sum, e) => sum + (e.duration_ms || 0), 0) / executions.length
      : 0;

    return {
      workflow_id: workflowId,
      execution_history: executions,
      performance: {
        total_executions: executions.length,
        successful_executions: successfulExecutions,
        failed_executions: failedExecutions,
        success_rate: executions.length > 0 ? Math.round((successfulExecutions / executions.length) * 100) : 0,
        avg_execution_time: Math.round(avgDuration),
      },
      metrics: metrics || {
        resources_saved: 0,
        co2_reduction: 0,
        cost_optimization: 0,
        aci_optimizations: 0,
      },
    };
  }

  private async calculateAverageExecutionTime(): Promise<number> {
    const result = await db('workflow_executions')
      .avg('duration_ms as avg_duration')
      .whereNotNull('duration_ms')
      .first();
    
    return Math.round(Number(result?.avg_duration) || 0);
  }

  private async calculateUserAverageExecutionTime(userId: string): Promise<number> {
    const result = await db('workflow_executions')
      .join('workflows', 'workflow_executions.workflow_id', 'workflows.id')
      .where('workflows.user_id', userId)
      .avg('workflow_executions.duration_ms as avg_duration')
      .whereNotNull('workflow_executions.duration_ms')
      .first();
    
    return Math.round(Number(result?.avg_duration) || 0);
  }

  private async calculateResourceSavings(): Promise<number> {
    const result = await db('workflow_metrics')
      .sum('resources_saved as total_savings')
      .first();
    
    return Number(result?.total_savings) || 0;
  }

  private async calculateUserResourceSavings(userId: string): Promise<number> {
    const result = await db('workflow_metrics')
      .join('workflows', 'workflow_metrics.workflow_id', 'workflows.id')
      .where('workflows.user_id', userId)
      .sum('workflow_metrics.resources_saved as total_savings')
      .first();
    
    return Number(result?.total_savings) || 0;
  }

  private async calculateCO2Reduction(): Promise<number> {
    const result = await db('workflow_metrics')
      .sum('co2_reduction as total_reduction')
      .first();
    
    return Number(result?.total_reduction) || 0;
  }

  private async calculateUserCO2Reduction(userId: string): Promise<number> {
    const result = await db('workflow_metrics')
      .join('workflows', 'workflow_metrics.workflow_id', 'workflows.id')
      .where('workflows.user_id', userId)
      .sum('workflow_metrics.co2_reduction as total_reduction')
      .first();
    
    return Number(result?.total_reduction) || 0;
  }

  private parsePeriod(period: string): number {
    const match = period.match(/^(\d+)([dwmy])$/);
    if (!match) return 30; // Default to 30 days

    const [, num, unit] = match;
    const value = parseInt(num);

    switch (unit) {
      case 'd': return value;
      case 'w': return value * 7;
      case 'm': return value * 30;
      case 'y': return value * 365;
      default: return 30;
    }
  }
}

export default new AnalyticsController();
