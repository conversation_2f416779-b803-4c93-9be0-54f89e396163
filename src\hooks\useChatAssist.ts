
import { useState, useCallback } from 'react';
import { n8nApi } from '../services/n8nApi';
import type { ChatMessage } from '../types/symbioAutomate';

export const useChatAssist = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '0',
      type: 'aci',
      content: 'Welcome to SymbioAutomate. I\'m your ACI assistant, ready to help you orchestrate intelligent workflows. What would you like to automate today?',
      timestamp: new Date().toISOString(),
      metadata: {}
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId] = useState(() => `session-${Date.now()}`);

  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString(),
      metadata: {}
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      const aciResponse = await n8nApi.sendChatMessage(
        content.trim(),
        'current-user', // This would come from auth context
        sessionId
      );

      setMessages(prev => [...prev, aciResponse]);
    } catch (error) {
      console.error('Chat error:', error);
      
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'aci',
        content: 'I apologize, but I\'m experiencing a temporary connection issue. Please try again in a moment.',
        timestamp: new Date().toISOString(),
        metadata: {}
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, sessionId]);

  const clearChat = useCallback(() => {
    setMessages([messages[0]]); // Keep welcome message
  }, [messages]);

  return {
    messages,
    isLoading,
    sendMessage,
    clearChat
  };
};
