
import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const morphicContainerVariants = cva(
  "relative transition-all duration-medium ease-morphic",
  {
    variants: {
      variant: {
        field: [
          "morphic-field",
          "p-6",
          "backdrop-blur-xl",
          "border border-text-tertiary/12"
        ],
        card: [
          "bg-gradient-to-br from-text-primary/8 to-text-secondary/3",
          "backdrop-blur-2xl",
          "border border-text-tertiary/15",
          "rounded-organic",
          "p-8",
          "hover:rounded-cellular",
          "hover:shadow-morphic-depth",
          "hover:-translate-y-1 hover:scale-[1.02]"
        ],
        surface: [
          "bg-gradient-to-br from-abyssal-elevated to-abyssal-deep",
          "border border-consciousness-500/20",
          "rounded-neural",
          "p-6",
          "hover:border-consciousness-500/40"
        ],
        quantum: [
          "bg-gradient-to-br from-consciousness-500/5 to-consciousness-secondary/3",
          "border border-consciousness-500/25",
          "rounded-quantum",
          "p-8",
          "hover:bg-gradient-to-br hover:from-consciousness-500/10 hover:to-consciousness-secondary/8",
          "hover:border-consciousness-500/50",
          "hover:shadow-quantum-primary"
        ]
      },
      depth: {
        base: "transform-gpu",
        layer1: "transform-gpu holographic-layer-1",
        layer2: "transform-gpu holographic-layer-2", 
        layer3: "transform-gpu holographic-layer-3",
      },
      interactive: {
        none: "",
        hover: "anticipatory cursor-pointer",
        morphic: "hover:animate-morphic-flow",
        pulse: "animate-sentient-pulse",
      },
      division: {
        none: "",
        symbioautomate: "border-symbioautomate-500/25 hover:border-symbioautomate-500/50 hover:shadow-division-auto",
        symbiolabs: "border-symbiolabs-500/25 hover:border-symbiolabs-500/50 hover:shadow-division-labs",
        symbioxchange: "border-symbioxchange-500/25 hover:border-symbioxchange-500/50",
        symbioedge: "border-symbioedge-500/25 hover:border-symbioedge-500/50",
        symbioimpact: "border-symbioimpact-500/25 hover:border-symbioimpact-500/50",
        symbioventures: "border-symbioventures-500/25 hover:border-symbioventures-500/50",
        symbioalliance: "border-symbioalliance-500/25 hover:border-symbioalliance-500/50",
      }
    },
    defaultVariants: {
      variant: "field",
      depth: "base",
      interactive: "none",
      division: "none",
    },
  }
);

interface MorphicContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof morphicContainerVariants> {
  children: React.ReactNode;
  anticipatoryDelay?: number;
}

const MorphicContainer: React.FC<MorphicContainerProps> = ({
  className,
  variant,
  depth,
  interactive,
  division,
  children,
  anticipatoryDelay = 150,
  onMouseEnter,
  onMouseLeave,
  ...props
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isAnticipating, setIsAnticipating] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const anticipationTimeoutRef = useRef<NodeJS.Timeout>();

  // Anticipatory interaction - slight pre-hover effect
  const handleMouseMove = (e: MouseEvent) => {
    if (!containerRef.current || interactive === "none") return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const isNearContainer = (
      e.clientX >= rect.left - 20 &&
      e.clientX <= rect.right + 20 &&
      e.clientY >= rect.top - 20 &&
      e.clientY <= rect.bottom + 20
    );
    
    if (isNearContainer && !isAnticipating && !isHovered) {
      setIsAnticipating(true);
      anticipationTimeoutRef.current = setTimeout(() => {
        setIsAnticipating(false);
      }, 1000);
    }
  };

  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsHovered(true);
    setIsAnticipating(false);
    if (anticipationTimeoutRef.current) {
      clearTimeout(anticipationTimeoutRef.current);
    }
    onMouseEnter?.(e);
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLDivElement>) => {
    setIsHovered(false);
    onMouseLeave?.(e);
  };

  useEffect(() => {
    if (interactive !== "none") {
      document.addEventListener('mousemove', handleMouseMove);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        if (anticipationTimeoutRef.current) {
          clearTimeout(anticipationTimeoutRef.current);
        }
      };
    }
  }, [interactive, isAnticipating, isHovered]);

  return (
    <div
      ref={containerRef}
      className={cn(
        morphicContainerVariants({ variant, depth, interactive, division }),
        // Anticipatory state styling
        isAnticipating && interactive !== "none" && [
          "border-consciousness-500/15",
          "shadow-sm shadow-consciousness-500/10"
        ],
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      {/* Quantum particle overlay for enhanced containers */}
      {(variant === "quantum" || division !== "none") && (
        <div
          className={cn(
            "absolute inset-0 opacity-0 transition-opacity duration-medium ease-sentient pointer-events-none rounded-inherit",
            "bg-gradient-to-br from-consciousness-500/5 via-transparent to-consciousness-secondary/5",
            isHovered && "opacity-100"
          )}
        />
      )}
      
      {/* Division-specific accent glow */}
      {division !== "none" && (
        <div
          className={cn(
            "absolute -inset-px rounded-inherit opacity-0 transition-opacity duration-medium ease-sentient pointer-events-none",
            division === "symbioautomate" && "bg-gradient-to-br from-symbioautomate-500/10 via-transparent to-symbioautomate-600/5",
            division === "symbiolabs" && "bg-gradient-to-br from-symbiolabs-500/10 via-transparent to-symbiolabs-600/5",
            division === "symbioxchange" && "bg-gradient-to-br from-symbioxchange-500/10 via-transparent to-symbioxchange-600/5",
            isHovered && "opacity-100"
          )}
        />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Morphic border animation */}
      {interactive === "morphic" && (
        <div className="absolute -inset-px border border-consciousness-500/20 rounded-inherit animate-morphic-flow opacity-60" />
      )}
    </div>
  );
};

export default MorphicContainer;
