
import React from 'react';
import { Globe, Leaf, Zap, TrendingUp } from 'lucide-react';
import Typography from '../atoms/Typography';
import Card from '../atoms/Card';

const GlobalImpact: React.FC = () => {
  const impactAreas = [
    {
      icon: Globe,
      title: "Autonomous Industrial Ecosystems",
      description: "Entire industrial parks operating as self-optimizing organisms where waste is eliminated entirely.",
      color: "consciousness"
    },
    {
      icon: Leaf,
      title: "Resilient Global Supply Chains", 
      description: "Networks that dynamically reconfigure in response to disruptions, ensuring stable flow of goods.",
      color: "harmony"
    },
    {
      icon: Zap,
      title: "Symbiotic Value Standard",
      description: "A world where company valuations depend on Symbiotic Score—contribution to ecosystem health.",
      color: "creativity"
    },
    {
      icon: TrendingUp,
      title: "Circular Economy Foundation",
      description: "The technological infrastructure for a future where economic growth drives ecological harmony.",
      color: "intuition"
    }
  ];

  return (
    <section className="py-32 relative">
      {/* Organic Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 left-1/6 w-56 h-56 bg-consciousness/8 rounded-cellular blur-3xl animate-consciousness-wave"></div>
        <div className="absolute bottom-1/4 right-1/6 w-40 h-40 bg-harmony/10 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Typography 
            as="h2" 
            variant="4xl" 
            weight="bold" 
            gradient="holographic"
            align="center"
            className="mb-6"
          >
            Global Impact & Vision
          </Typography>
          
          <Typography 
            as="p" 
            variant="xl" 
            color="secondary" 
            align="center"
            className="mb-6"
          >
            Building the Foundational Infrastructure for Tomorrow
          </Typography>

          <Typography 
            as="p" 
            variant="lg" 
            color="tertiary" 
            align="center"
            className="max-w-4xl mx-auto leading-relaxed"
          >
            SymbioWave's ambition extends far beyond financial returns. We are architecting 
            the fundamental systems for a global circular economy where sustainability 
            and profitability are intrinsically linked.
          </Typography>
        </div>

        {/* Impact Areas */}
        <div className="grid md:grid-cols-2 gap-8 mb-20">
          {impactAreas.map((area, index) => {
            const IconComponent = area.icon;
            return (
              <Card 
                key={area.title}
                variant="neural"
                className={`group hover:border-${area.color}/50 transition-all duration-bio hover:transform hover:scale-105`}
              >
                <div className="flex items-start space-x-4">
                  <div className={`w-16 h-16 rounded-cellular bg-${area.color}/10 flex items-center justify-center flex-shrink-0 group-hover:bg-${area.color}/20 transition-colors duration-quantum`}>
                    <IconComponent className={`w-8 h-8 text-${area.color}`} />
                  </div>
                  
                  <div className="flex-1">
                    <Typography 
                      variant="lg" 
                      weight="semibold" 
                      className={`mb-3 text-${area.color}`}
                    >
                      {area.title}
                    </Typography>
                    <Typography 
                      variant="sm" 
                      color="secondary"
                      className="leading-relaxed"
                    >
                      {area.description}
                    </Typography>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        {/* Vision Statement */}
        <Card variant="quantum" className="text-center border-consciousness/30">
          <Typography 
            as="h3" 
            variant="2xl" 
            weight="bold" 
            gradient="neural"
            className="mb-6"
          >
            A Generational Transformation
          </Typography>
          
          <Typography 
            as="p" 
            variant="lg" 
            color="secondary" 
            className="mb-8 max-w-4xl mx-auto leading-relaxed"
          >
            The transition from a linear to a symbiotic economy is the single greatest 
            business transformation of the 21st century. SymbioWave is positioned to become 
            a generational company—one whose success is intrinsically tied to creating a 
            more resilient, efficient, and sustainable world for all.
          </Typography>

          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <Typography variant="2xl" weight="bold" color="consciousness" className="mb-2">
                $500M
              </Typography>
              <Typography variant="sm" color="tertiary">
                Projected Annual Revenue by Year 5
              </Typography>
            </div>
            <div>
              <Typography variant="2xl" weight="bold" color="harmony" className="mb-2">
                $10B+
              </Typography>
              <Typography variant="sm" color="tertiary">
                Marketplace GMV Target
              </Typography>
            </div>
            <div>
              <Typography variant="2xl" weight="bold" color="creativity" className="mb-2">
                Global
              </Typography>
              <Typography variant="sm" color="tertiary">
                Circular Economy Impact
              </Typography>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default GlobalImpact;
