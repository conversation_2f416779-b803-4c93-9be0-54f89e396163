import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import assessmentController from '../controllers/assessmentController';

const router = Router();

/**
 * @swagger
 * /assessments/ai-readiness:
 *   post:
 *     summary: Submit AI readiness assessment
 *     tags: [Assessments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - responses
 *             properties:
 *               responses:
 *                 type: object
 *                 description: Assessment responses
 *               company_info:
 *                 type: object
 *                 properties:
 *                   industry:
 *                     type: string
 *                   size:
 *                     type: string
 *                   current_tech_stack:
 *                     type: array
 *                     items:
 *                       type: string
 *     responses:
 *       201:
 *         description: Assessment submitted successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 */
router.post('/ai-readiness', authenticateToken as any, assessmentController.submitAIReadinessAssessment as any);

/**
 * @swagger
 * /assessments/ai-readiness:
 *   get:
 *     summary: Get user's AI readiness assessments
 *     tags: [Assessments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: latest
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Get only the latest assessment
 *     responses:
 *       200:
 *         description: Assessments retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/ai-readiness', authenticateToken as any, assessmentController.getAssessments as any);

/**
 * @swagger
 * /assessments/ai-readiness/{id}:
 *   get:
 *     summary: Get specific AI readiness assessment
 *     tags: [Assessments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Assessment retrieved successfully
 *       404:
 *         description: Assessment not found
 *       401:
 *         description: Unauthorized
 */
router.get('/ai-readiness/:id', authenticateToken as any, assessmentController.getAssessment as any);

export default router;
