import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('workflow_executions', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('workflow_id').notNullable().references('id').inTable('workflows').onDelete('CASCADE');
    table.enum('status', ['pending', 'running', 'success', 'error', 'cancelled']).defaultTo('pending');
    table.timestamp('started_at').notNullable();
    table.timestamp('completed_at');
    table.integer('duration_ms');
    table.jsonb('input_data');
    table.jsonb('output_data');
    table.text('error_message');
    table.timestamps(true, true);

    // Indexes
    table.index(['workflow_id']);
    table.index(['status']);
    table.index(['started_at']);
    table.index(['completed_at']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('workflow_executions');
}
