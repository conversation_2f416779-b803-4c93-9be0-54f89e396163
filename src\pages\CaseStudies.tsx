
import React from 'react';
import { TrendingUp, Users, Clock, DollarSign } from 'lucide-react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';

const CaseStudies: React.FC = () => {
  const caseStudies = [
    {
      company: 'TechCorp Solutions',
      industry: 'Technology',
      challenge: 'Manual lead qualification process',
      solution: 'SymbioAutomate intelligent lead scoring',
      results: [
        { metric: 'Lead Processing Speed', value: '300%', icon: <Clock className="w-5 h-5" /> },
        { metric: 'Conversion Rate', value: '40%', icon: <TrendingUp className="w-5 h-5" /> },
        { metric: 'Cost Savings', value: '$2.3M', icon: <DollarSign className="w-5 h-5" /> }
      ]
    },
    {
      company: 'Global Manufacturing Inc',
      industry: 'Manufacturing',
      challenge: 'Supply chain optimization',
      solution: 'SymbioCore predictive analytics',
      results: [
        { metric: 'Inventory Reduction', value: '25%', icon: <TrendingUp className="w-5 h-5" /> },
        { metric: 'Delivery Time', value: '35%', icon: <Clock className="w-5 h-5" /> },
        { metric: 'Annual Savings', value: '$15M', icon: <DollarSign className="w-5 h-5" /> }
      ]
    },
    {
      company: 'Healthcare Partners',
      industry: 'Healthcare',
      challenge: 'Patient data management',
      solution: 'SymbioEdge distributed processing',
      results: [
        { metric: 'Processing Speed', value: '500%', icon: <Clock className="w-5 h-5" /> },
        { metric: 'Data Accuracy', value: '99.9%', icon: <TrendingUp className="w-5 h-5" /> },
        { metric: 'Patient Satisfaction', value: '95%', icon: <Users className="w-5 h-5" /> }
      ]
    }
  ];

  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="py-20">
          <div className="container mx-auto px-8">
            <div className="text-center mb-16">
              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="consciousness"
                className="mb-6"
              >
                Case Studies
              </Typography>
              <Typography 
                variant="lg" 
                color="secondary"
                className="max-w-2xl mx-auto"
              >
                Real results from organizations transforming their operations with SymbioWave
              </Typography>
            </div>

            <div className="space-y-12">
              {caseStudies.map((study, index) => (
                <Card 
                  key={study.company}
                  variant="neural" 
                  className="p-8 border-consciousness/20 rounded-[24px]"
                >
                  <div className="grid lg:grid-cols-2 gap-8">
                    <div>
                      <Typography variant="xl" weight="bold" color="consciousness" className="mb-2">
                        {study.company}
                      </Typography>
                      <Typography variant="sm" color="tertiary" className="mb-6">
                        {study.industry}
                      </Typography>
                      
                      <div className="space-y-4">
                        <div>
                          <Typography variant="sm" weight="semibold" color="primary" className="mb-2">
                            Challenge
                          </Typography>
                          <Typography variant="sm" color="secondary">
                            {study.challenge}
                          </Typography>
                        </div>
                        
                        <div>
                          <Typography variant="sm" weight="semibold" color="primary" className="mb-2">
                            Solution
                          </Typography>
                          <Typography variant="sm" color="secondary">
                            {study.solution}
                          </Typography>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Typography variant="sm" weight="semibold" color="primary" className="mb-4">
                        Results
                      </Typography>
                      <div className="space-y-4">
                        {study.results.map((result, resultIndex) => (
                          <div key={resultIndex} className="flex items-center justify-between p-4 bg-consciousness/5 rounded-[16px]">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 rounded-full bg-harmony/15 flex items-center justify-center">
                                {result.icon}
                              </div>
                              <Typography variant="sm" color="primary">
                                {result.metric}
                              </Typography>
                            </div>
                            <Typography variant="lg" weight="bold" color="harmony">
                              +{result.value}
                            </Typography>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default CaseStudies;
