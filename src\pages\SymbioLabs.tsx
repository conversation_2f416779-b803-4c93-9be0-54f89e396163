
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import { Microscope, Lightbulb, Beaker, Target } from 'lucide-react';

const SymbioLabs: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-creativity/10 rounded-cellular blur-3xl animate-consciousness-wave"></div>
            <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-creativity/15 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
          </div>

          <div className="container mx-auto px-6 py-20 relative z-10">
            <div className="text-center space-y-8 max-w-4xl mx-auto">
              <div className="inline-flex items-center space-x-3 px-4 py-2 glass-quantum rounded-full border border-creativity/30">
                <Microscope className="w-5 h-5 text-creativity animate-pulse" />
                <Typography variant="sm" weight="medium" color="creativity">
                  Division II
                </Typography>
              </div>

              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="creativity"
                className="mb-4"
              >
                SymbioLabs
              </Typography>
              
              <Typography 
                as="p" 
                variant="xl" 
                color="creativity" 
                weight="medium"
                className="mb-6"
              >
                The Engine of Innovation
              </Typography>

              <Typography 
                as="p" 
                variant="lg" 
                color="secondary"
                className="leading-relaxed max-w-2xl mx-auto"
              >
                Our dedicated R&D division responsible for advancing the frontiers of ACI and developing next-generation, nature-inspired AI models.
              </Typography>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <Lightbulb className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    Biomimicry Research
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Nature-inspired algorithms
                  </Typography>
                </Card>

                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <Beaker className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    ACI Development
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Next-gen AI models
                  </Typography>
                </Card>

                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <Target className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    Patent Portfolio
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Foundational IP protection
                  </Typography>
                </Card>

                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <Microscope className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    Research Partnerships
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Academic collaboration
                  </Typography>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default SymbioLabs;
