import React, { useRef, useEffect, useState } from 'react';
import { ArrowRight, Cog, Zap, Network, Target } from 'lucide-react';
import Typography from '../atoms/Typography';
import Button from '../atoms/Button';
import Card from '../atoms/Card';
import AutomationVisualization from '../interactive/AutomationVisualization';
import DataSculpture from '../interactive/DataSculpture';
import HolographicLayers from '../interactive/HolographicLayers';

const SymbioAutomateHero: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const heroRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    if (heroRef.current) {
      observer.observe(heroRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const performanceData = [
    { value: 87, label: 'Automation Rate', color: 'rgba(0, 255, 170, 0.8)', category: 'efficiency' },
    { value: 65, label: 'Cost Reduction', color: 'rgba(0, 255, 119, 0.8)', category: 'savings' },
    { value: 93, label: 'Accuracy', color: 'rgba(0, 153, 255, 0.8)', category: 'quality' },
    { value: 78, label: 'Speed Increase', color: 'rgba(170, 0, 255, 0.8)', category: 'performance' }
  ];

  const holographicLayers = [
    {
      content: (
        <div className="absolute inset-0 bg-gradient-to-br from-consciousness/10 to-transparent rounded-[32px]" />
      ),
      depth: 1,
      opacity: 0.6,
      blur: 0
    },
    {
      content: (
        <div className="absolute inset-0">
          <AutomationVisualization isActive={isVisible} />
        </div>
      ),
      depth: 2,
      opacity: 1,
      blur: 0
    },
    {
      content: (
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-consciousness/5 rounded-[32px]" />
      ),
      depth: 3,
      opacity: 0.8,
      blur: 1
    }
  ];

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20"
    >
      {/* Sacred Geometry SymbioAutomate Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-[23.6%] left-[23.6%] w-[382px] h-[382px] bg-harmony/8 rounded-full blur-3xl animate-consciousness-wave"></div>
        <div className="absolute bottom-[23.6%] right-[23.6%] w-[236px] h-[236px] bg-harmony/12 rounded-full blur-2xl animate-neural-pulse"></div>
        <div className="absolute top-[61.8%] left-[50%] w-[191px] h-[191px] bg-consciousness/6 rounded-full blur-xl animate-quantum-flicker"></div>
      </div>

      {/* Sacred Floating Automation Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-[30%] left-[30%] w-6 h-6 bg-harmony/40 rounded-full animate-consciousness-wave"></div>
        <div className="absolute top-[70%] right-[25%] w-8 h-8 bg-consciousness/35 rounded-full animate-neural-pulse"></div>
        <div className="absolute top-[50%] left-[75%] w-4 h-4 bg-harmony/50 rounded-full animate-quantum-flicker"></div>
        <div className="absolute top-[35%] right-[35%] w-6 h-6 bg-creativity/25 rounded-full animate-consciousness-wave"></div>
      </div>

      <div className="container mx-auto px-8 py-24 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left: Sacred Content Layout */}
          <div className="space-y-10">
            {/* Sacred Division Badge */}
            <div className="inline-flex items-center space-x-4 px-6 py-3 glass-quantum rounded-[24px] border border-harmony/30">
              <div className="w-8 h-8 rounded-full bg-harmony/20 flex items-center justify-center">
                <Cog className="w-5 h-5 text-harmony animate-spin-slow" />
              </div>
              <Typography variant="sm" weight="semibold" color="harmony">
                Division IV - First Active
              </Typography>
            </div>

            {/* Sacred Headlines */}
            <div className="space-y-6">
              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="consciousness"
                className="mb-4"
              >
                SymbioAutomate
              </Typography>
              
              <Typography 
                as="p" 
                variant="xl" 
                color="harmony" 
                weight="semibold"
                className="mb-6"
              >
                The Efficiency Engine
              </Typography>

              <Typography 
                as="p" 
                variant="lg" 
                color="secondary"
                className="leading-relaxed"
              >
                Intelligent Process Automation, Evolved by ACI
              </Typography>
            </div>

            {/* Enhanced Performance Metrics with Data Sculpture */}
            <Card variant="neural" className="border-harmony/25 p-8 rounded-[24px]">
              <Typography 
                as="h3" 
                variant="lg" 
                weight="semibold" 
                color="consciousness"
                className="mb-6"
              >
                Live Performance Metrics
              </Typography>
              
              <div className="h-48 mb-6">
                <DataSculpture 
                  data={performanceData}
                  variant="constellation"
                  isActive={isVisible}
                  height={192}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                {performanceData.map((metric, index) => (
                  <div key={metric.label} className="text-center">
                    <Typography variant="xl" weight="bold" color="harmony">
                      {metric.value}%
                    </Typography>
                    <Typography variant="xs" color="tertiary">
                      {metric.label}
                    </Typography>
                  </div>
                ))}
              </div>
            </Card>

            {/* Sacred Value Proposition */}
            <Card variant="neural" className="border-harmony/25 p-8 rounded-[24px]">
              <Typography 
                as="h3" 
                variant="lg" 
                weight="semibold" 
                color="consciousness"
                className="mb-4"
              >
                Beyond RPA: Fluid Intelligence
              </Typography>
              <Typography 
                variant="sm" 
                color="secondary"
                className="leading-relaxed"
              >
                SymbioAutomate transcends rigid, rule-based automation. Using ACI from SymbioCore, 
                it creates dynamic, self-optimizing workflows that don't break when conditions change—they evolve.
              </Typography>
            </Card>

            {/* Sacred Key Benefits */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-harmony/15 flex items-center justify-center flex-shrink-0 mt-1">
                  <Zap className="w-6 h-6 text-harmony" />
                </div>
                <div>
                  <Typography variant="sm" weight="semibold" color="harmony" className="mb-2">
                    Real-time Adaptation
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Workflows adapt to changing conditions automatically
                  </Typography>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-consciousness/15 flex items-center justify-center flex-shrink-0 mt-1">
                  <Network className="w-6 h-6 text-consciousness" />
                </div>
                <div>
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Ecosystem Integration
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Leverages SymbioXchange data for optimization
                  </Typography>
                </div>
              </div>
            </div>

            {/* Sacred CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="group rounded-[20px]"
              >
                <span className="relative z-10">Request Demo</span>
              </Button>
              
              <Button 
                variant="outline-quantum" 
                size="lg"
                className="rounded-[20px]"
              >
                Explore Use Cases
              </Button>
            </div>

            {/* Sacred Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-subtle">
              <div className="text-center">
                <Typography variant="2xl" weight="bold" color="harmony">15%</Typography>
                <Typography variant="xs" color="tertiary">Energy Reduction</Typography>
              </div>
              <div className="text-center">
                <Typography variant="2xl" weight="bold" color="consciousness">10%</Typography>
                <Typography variant="xs" color="tertiary">Less Spoilage</Typography>
              </div>
              <div className="text-center">
                <Typography variant="2xl" weight="bold" color="creativity">Real-time</Typography>
                <Typography variant="xs" color="tertiary">Optimization</Typography>
              </div>
            </div>
          </div>

          {/* Right: Enhanced 3D Holographic Visualization */}
          <div className="relative">
            <Card variant="quantum" className="overflow-hidden border-harmony/25 rounded-[32px] p-4">
              <div className="aspect-square">
                <HolographicLayers 
                  layers={holographicLayers}
                  isActive={isVisible}
                />
              </div>
            </Card>
            
            {/* Sacred Floating Feature Cards */}
            <div className="absolute -top-6 -right-6 w-28 h-28 glass-quantum rounded-full border border-consciousness/25 flex items-center justify-center">
              <Target className="w-8 h-8 text-consciousness animate-pulse" />
            </div>
            
            <div className="absolute -bottom-6 -left-6 w-24 h-24 glass-quantum rounded-full border border-harmony/25 flex items-center justify-center">
              <Zap className="w-6 h-6 text-harmony animate-quantum-flicker" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SymbioAutomateHero;
