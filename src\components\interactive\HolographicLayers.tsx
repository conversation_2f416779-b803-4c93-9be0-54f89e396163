
import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface HolographicLayersProps {
  layers: {
    content: React.ReactNode;
    depth: number;
    opacity: number;
    blur: number;
  }[];
  isActive: boolean;
  className?: string;
}

const HolographicLayers: React.FC<HolographicLayersProps> = ({
  layers,
  isActive,
  className
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isActive) return;
      
      const rect = container.getBoundingClientRect();
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;
      
      const deltaX = (mouseX - centerX) / centerX;
      const deltaY = (mouseY - centerY) / centerY;
      
      // Apply parallax effect to layers based on depth
      const layerElements = container.querySelectorAll('.holographic-layer');
      layerElements.forEach((layer, index) => {
        const depth = layers[index]?.depth || 1;
        const translateX = deltaX * depth * 20;
        const translateY = deltaY * depth * 20;
        
        (layer as HTMLElement).style.transform = 
          `translate3d(${translateX}px, ${translateY}px, ${depth * 10}px) rotateX(${deltaY * depth * 5}deg) rotateY(${deltaX * depth * 5}deg)`;
      });
    };

    container.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isActive, layers]);

  // Create keyframes animation using CSS custom properties
  useEffect(() => {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
      @keyframes holographic-sweep {
        0% { background-position: 0% 0%; }
        50% { background-position: 100% 100%; }
        100% { background-position: 0% 0%; }
      }
    `;
    document.head.appendChild(styleSheet);
    
    return () => {
      document.head.removeChild(styleSheet);
    };
  }, []);

  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative w-full h-full overflow-hidden",
        "transform-gpu will-change-transform",
        className
      )}
      style={{ perspective: '1000px' }}
    >
      {layers.map((layer, index) => (
        <div
          key={index}
          className="holographic-layer absolute inset-0 transition-transform duration-100 ease-out"
          style={{
            opacity: isActive ? layer.opacity : layer.opacity * 0.5,
            filter: `blur(${layer.blur}px)`,
            zIndex: layers.length - index,
            transform: `translateZ(${layer.depth * 10}px)`,
            transformStyle: 'preserve-3d'
          }}
        >
          <div className="relative w-full h-full">
            {layer.content}
            
            {/* Holographic overlay effect */}
            <div 
              className="absolute inset-0 pointer-events-none"
              style={{
                background: `linear-gradient(45deg, 
                  rgba(0, 255, 170, ${0.1 * layer.opacity}) 0%, 
                  transparent 25%, 
                  rgba(0, 255, 255, ${0.05 * layer.opacity}) 50%, 
                  transparent 75%, 
                  rgba(170, 0, 255, ${0.1 * layer.opacity}) 100%)`,
                backgroundSize: '200% 200%',
                animation: `holographic-sweep ${3 + index}s ease-in-out infinite`
              }}
            />
          </div>
        </div>
      ))}
      
      {/* Depth field effect */}
      <div className="absolute inset-0 pointer-events-none">
        <div 
          className="w-full h-full"
          style={{
            background: 'radial-gradient(ellipse at center, transparent 0%, rgba(0, 0, 0, 0.1) 70%)',
            opacity: isActive ? 1 : 0.5,
            transition: 'opacity 0.3s ease-out'
          }}
        />
      </div>
    </div>
  );
};

export default HolographicLayers;
