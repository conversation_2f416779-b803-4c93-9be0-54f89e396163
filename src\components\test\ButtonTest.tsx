import React from 'react';
import { But<PERSON> } from '@/components/atoms/Button';
import { ArrowR<PERSON>, Download, Setting<PERSON>, Zap } from 'lucide-react';

/**
 * Button Test Component - Verify all variants and features work correctly
 */
const ButtonTest: React.FC = () => {
  return (
    <div className="p-8 space-y-8 bg-abyssal-base min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-consciousness-primary mb-8">
          Button Component Integration Test
        </h1>
        
        {/* Core Variants */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Core Variants</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="primary">Primary Button</Button>
            <Button variant="quantum">Quantum Button</Button>
            <Button variant="outline-quantum">Outline Quantum</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="success">Success</Button>
            <Button variant="danger">Danger</Button>
            <Button variant="warning">Warning</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="link">Link Button</Button>
          </div>
        </section>

        {/* Division-Specific Variants */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Division-Specific Variants</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="symbioautomate">SymbioAutomate</Button>
            <Button variant="symbiolabs">SymbioLabs</Button>
            <Button variant="symbioxchange">SymbioXchange</Button>
            <Button variant="symbioedge">SymbioEdge</Button>
            <Button variant="symbioimpact">SymbioImpact</Button>
            <Button variant="symbioventures">SymbioVentures</Button>
            <Button variant="symbioalliance">SymbioAlliance</Button>
          </div>
        </section>

        {/* Sizes */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Sizes</h2>
          <div className="flex flex-wrap items-center gap-4">
            <Button variant="quantum" size="xs">Extra Small</Button>
            <Button variant="quantum" size="sm">Small</Button>
            <Button variant="quantum" size="md">Medium</Button>
            <Button variant="quantum" size="lg">Large</Button>
            <Button variant="quantum" size="xl">Extra Large</Button>
          </div>
        </section>

        {/* With Icons */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">With Icons</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="quantum" leftIcon={<Download className="w-4 h-4" />}>
              Download
            </Button>
            <Button variant="primary" rightIcon={<ArrowRight className="w-4 h-4" />}>
              Continue
            </Button>
            <Button variant="outline-quantum" leftIcon={<Settings className="w-4 h-4" />} rightIcon={<ArrowRight className="w-4 h-4" />}>
              Settings
            </Button>
          </div>
        </section>

        {/* Loading States */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Loading States</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="quantum" isLoading>
              Loading...
            </Button>
            <Button variant="primary" isLoading loadingText="Processing">
              Process
            </Button>
            <Button variant="symbioautomate" isLoading loadingText="Optimizing">
              Optimize
            </Button>
          </div>
        </section>

        {/* Morphic Variants */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Morphic Design</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="quantum" morphic>
              Morphic Quantum
            </Button>
            <Button variant="symbioautomate" morphic size="lg">
              Morphic Large
            </Button>
            <Button variant="outline-quantum" morphic>
              Morphic Outline
            </Button>
          </div>
        </section>

        {/* Full Width */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Full Width</h2>
          <div className="space-y-4">
            <Button variant="quantum" fullWidth rightIcon={<Zap className="w-4 h-4" />}>
              Full Width Quantum Button
            </Button>
            <Button variant="outline-quantum" fullWidth>
              Full Width Outline Button
            </Button>
          </div>
        </section>

        {/* Disabled States */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Disabled States</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="quantum" disabled>
              Disabled Quantum
            </Button>
            <Button variant="primary" disabled>
              Disabled Primary
            </Button>
            <Button variant="symbioautomate" disabled>
              Disabled Division
            </Button>
          </div>
        </section>

        {/* Real Usage Examples */}
        <section className="mb-8">
          <h2 className="text-xl font-semibold text-foreground mb-4">Real Usage Examples</h2>
          <div className="space-y-4">
            {/* CTA Example */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                variant="quantum" 
                size="lg"
                rightIcon={<ArrowRight className="w-5 h-5" />}
                className="group"
              >
                Start Your Transformation
              </Button>
              <Button 
                variant="outline-quantum" 
                size="lg"
              >
                Request Partnership Discussion
              </Button>
            </div>
            
            {/* Interface Example */}
            <Button 
              variant="quantum" 
              size="lg"
              rightIcon={<ArrowRight className="w-5 h-5" />}
              className="rounded-[20px]"
            >
              Begin Your Journey
            </Button>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ButtonTest;
