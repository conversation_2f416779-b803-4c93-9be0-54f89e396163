import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('users', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('email').unique().notNullable();
    table.string('password_hash');
    table.string('full_name').notNullable();
    table.string('company_name');
    table.enum('role', ['admin', 'user', 'enterprise', 'demo']).defaultTo('user');
    table.enum('access_level', ['basic', 'premium', 'enterprise', 'unlimited']).defaultTo('basic');
    table.enum('auth_provider', ['local', 'google', 'microsoft']).defaultTo('local');
    table.string('avatar_url');
    table.boolean('is_verified').defaultTo(false);
    table.boolean('is_active').defaultTo(true);
    table.timestamp('last_login');
    table.timestamps(true, true);

    // Indexes
    table.index(['email']);
    table.index(['role']);
    table.index(['access_level']);
    table.index(['is_active']);
    table.index(['created_at']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('users');
}
