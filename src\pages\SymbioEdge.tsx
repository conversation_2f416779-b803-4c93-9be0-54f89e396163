
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import { Wifi, Cpu, Zap, MapPin } from 'lucide-react';

const SymbioEdge: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-consciousness/10 rounded-cellular blur-3xl animate-consciousness-wave"></div>
            <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-consciousness/15 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
          </div>

          <div className="container mx-auto px-6 py-20 relative z-10">
            <div className="text-center space-y-8 max-w-4xl mx-auto">
              <div className="inline-flex items-center space-x-3 px-4 py-2 glass-quantum rounded-full border border-consciousness/30">
                <Wifi className="w-5 h-5 text-consciousness animate-pulse" />
                <Typography variant="sm" weight="medium" color="consciousness">
                  Division V
                </Typography>
              </div>

              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="consciousness"
                className="mb-4"
              >
                SymbioEdge
              </Typography>
              
              <Typography 
                as="p" 
                variant="xl" 
                color="consciousness" 
                weight="medium"
                className="mb-6"
              >
                Intelligence at the Frontier
              </Typography>

              <Typography 
                as="p" 
                variant="lg" 
                color="secondary"
                className="leading-relaxed max-w-2xl mx-auto"
              >
                Deploys lightweight, specialized ACI models on edge devices for real-time decision-making without cloud dependency.
              </Typography>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <Cpu className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Edge Computing
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Local AI processing
                  </Typography>
                </Card>

                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <Zap className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Real-time Response
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Instant decision making
                  </Typography>
                </Card>

                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <MapPin className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    IoT Integration
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Sensor network intelligence
                  </Typography>
                </Card>

                <Card variant="neural" className="border-consciousness/30 text-center p-6">
                  <Wifi className="w-8 h-8 text-consciousness mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="consciousness" className="mb-2">
                    Autonomous Systems
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Self-managing operations
                  </Typography>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default SymbioEdge;
