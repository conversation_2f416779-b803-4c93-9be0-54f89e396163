
import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import { TrendingUp, Lightbulb, Users, Target } from 'lucide-react';

const SymbioVentures: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-20">
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-creativity/10 rounded-cellular blur-3xl animate-consciousness-wave"></div>
            <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-creativity/15 rounded-biomorphic blur-2xl animate-neural-pulse"></div>
          </div>

          <div className="container mx-auto px-6 py-20 relative z-10">
            <div className="text-center space-y-8 max-w-4xl mx-auto">
              <div className="inline-flex items-center space-x-3 px-4 py-2 glass-quantum rounded-full border border-creativity/30">
                <TrendingUp className="w-5 h-5 text-creativity animate-pulse" />
                <Typography variant="sm" weight="medium" color="creativity">
                  Division VII
                </Typography>
              </div>

              <Typography 
                as="h1" 
                variant="4xl" 
                weight="bold" 
                gradient="creativity"
                className="mb-4"
              >
                SymbioVentures
              </Typography>
              
              <Typography 
                as="p" 
                variant="xl" 
                color="creativity" 
                weight="medium"
                className="mb-6"
              >
                The Catalyst for Growth
              </Typography>

              <Typography 
                as="p" 
                variant="lg" 
                color="secondary"
                className="leading-relaxed max-w-2xl mx-auto"
              >
                A corporate venture capital arm that invests in and incubates early-stage startups in symbiotic technologies, AI, and sustainability.
              </Typography>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <TrendingUp className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    Strategic Investment
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Early-stage funding
                  </Typography>
                </Card>

                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <Lightbulb className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    Innovation Labs
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Startup incubation
                  </Typography>
                </Card>

                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <Users className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    Ecosystem Access
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Platform integration
                  </Typography>
                </Card>

                <Card variant="neural" className="border-creativity/30 text-center p-6">
                  <Target className="w-8 h-8 text-creativity mx-auto mb-3" />
                  <Typography variant="sm" weight="semibold" color="creativity" className="mb-2">
                    Market Focus
                  </Typography>
                  <Typography variant="xs" color="tertiary">
                    Sustainability tech
                  </Typography>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default SymbioVentures;
