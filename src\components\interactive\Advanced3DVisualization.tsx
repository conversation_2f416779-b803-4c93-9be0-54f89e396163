
import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface Advanced3DVisualizationProps {
  isActive: boolean;
  variant?: 'automation' | 'ecosystem' | 'network';
  intensity?: 'subtle' | 'medium' | 'intense';
  className?: string;
}

const Advanced3DVisualization: React.FC<Advanced3DVisualizationProps> = ({ 
  isActive, 
  variant = 'automation',
  intensity = 'medium',
  className 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const particlesRef = useRef<any[]>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * 2; // High DPI
      canvas.height = rect.height * 2;
      ctx.scale(2, 2);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize particles based on variant
    const initParticles = () => {
      particlesRef.current = [];
      const particleCount = intensity === 'intense' ? 150 : intensity === 'medium' ? 100 : 50;
      
      for (let i = 0; i < particleCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width / 2,
          y: Math.random() * canvas.height / 2,
          z: Math.random() * 1000,
          vx: (Math.random() - 0.5) * 2,
          vy: (Math.random() - 0.5) * 2,
          vz: (Math.random() - 0.5) * 4,
          size: Math.random() * 3 + 1,
          alpha: Math.random() * 0.8 + 0.2,
          color: variant === 'automation' ? 
            `rgba(0, 255, ${Math.floor(Math.random() * 100) + 155}, ${Math.random() * 0.8 + 0.2})` :
            variant === 'ecosystem' ?
            `rgba(${Math.floor(Math.random() * 100) + 155}, 0, 255, ${Math.random() * 0.8 + 0.2})` :
            `rgba(0, ${Math.floor(Math.random() * 100) + 155}, 255, ${Math.random() * 0.8 + 0.2})`,
          phase: Math.random() * Math.PI * 2,
          frequency: Math.random() * 0.02 + 0.01
        });
      }
    };

    initParticles();

    let time = 0;
    const centerX = canvas.width / 4;
    const centerY = canvas.height / 4;

    const animate = () => {
      if (!isActive) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width / 2, canvas.height / 2);
      
      time += 0.016;

      // Render particles with 3D projection
      particlesRef.current.forEach((particle, index) => {
        // Update particle position
        particle.x += particle.vx * Math.sin(time * particle.frequency);
        particle.y += particle.vy * Math.cos(time * particle.frequency);
        particle.z += particle.vz;
        
        // 3D projection
        const perspective = 500;
        const scale = perspective / (perspective + particle.z);
        const projX = (particle.x - centerX) * scale + centerX;
        const projY = (particle.y - centerY) * scale + centerY;
        
        // Reset particle if it goes too far
        if (particle.z > 1000 || projX < 0 || projX > canvas.width / 2 || projY < 0 || projY > canvas.height / 2) {
          particle.x = Math.random() * canvas.width / 2;
          particle.y = Math.random() * canvas.height / 2;
          particle.z = -Math.random() * 200;
        }

        // Draw particle with holographic effect
        const size = particle.size * scale;
        const alpha = particle.alpha * scale;
        
        if (size > 0.1 && alpha > 0.1) {
          // Holographic glow
          ctx.shadowBlur = size * 4;
          ctx.shadowColor = particle.color;
          
          ctx.fillStyle = particle.color.replace(/[\d\.]+\)$/g, `${alpha})`);
          ctx.beginPath();
          ctx.arc(projX, projY, size, 0, Math.PI * 2);
          ctx.fill();
          
          // Core light
          ctx.shadowBlur = 0;
          ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.8})`;
          ctx.beginPath();
          ctx.arc(projX, projY, size * 0.3, 0, Math.PI * 2);
          ctx.fill();
        }
      });

      // Draw interconnecting energy streams
      if (variant === 'network') {
        ctx.strokeStyle = 'rgba(0, 255, 170, 0.3)';
        ctx.lineWidth = 1;
        
        particlesRef.current.forEach((particle, i) => {
          particlesRef.current.slice(i + 1).forEach((otherParticle) => {
            const dx = particle.x - otherParticle.x;
            const dy = particle.y - otherParticle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 100) {
              const alpha = 1 - (distance / 100);
              ctx.strokeStyle = `rgba(0, 255, 170, ${alpha * 0.3})`;
              
              ctx.beginPath();
              const scale1 = 500 / (500 + particle.z);
              const scale2 = 500 / (500 + otherParticle.z);
              
              ctx.moveTo(
                (particle.x - centerX) * scale1 + centerX,
                (particle.y - centerY) * scale1 + centerY
              );
              ctx.lineTo(
                (otherParticle.x - centerX) * scale2 + centerX,
                (otherParticle.y - centerY) * scale2 + centerY
              );
              ctx.stroke();
            }
          });
        });
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, variant, intensity]);

  return (
    <div className={cn("relative w-full h-full overflow-hidden", className)}>
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ 
          background: variant === 'automation' 
            ? 'radial-gradient(ellipse at center, rgba(0, 255, 170, 0.1) 0%, rgba(0, 0, 0, 0.8) 70%)'
            : variant === 'ecosystem'
            ? 'radial-gradient(ellipse at center, rgba(155, 0, 255, 0.1) 0%, rgba(0, 0, 0, 0.8) 70%)'
            : 'radial-gradient(ellipse at center, rgba(0, 155, 255, 0.1) 0%, rgba(0, 0, 0, 0.8) 70%)'
        }}
      />
      
      {/* Holographic overlay effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-consciousness/5 to-creativity/5 animate-pulse" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(0,255,170,0.1),transparent_50%)] animate-consciousness-wave" />
    </div>
  );
};

export default Advanced3DVisualization;
